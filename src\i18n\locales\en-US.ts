export default {
  translation: {
    settings: {
      title: "Settings",
      tabs: {
        account: "Account",
        general: "General",
        index: "Index",
        search: "Search",
        dataType: "Data Type",
        cache: "Cache",
        hotkey: "Hotkey",
        advanced: "Advanced",
        attachExplorer: "Attach searchbar to Windows Explorer",
        about: "About",
      },
      account: {
        title: "Account Settings",
        tip: "Manage your account settings here",
        username: "<PERSON><PERSON><PERSON>",
        email: "Email",
        login: "Login",
        licenseType: "License: {{type}}",
        expireDate: "Expires: {{date}}",
        logout: "Log out",
        confirmLogout: "Are you sure to log out?",
        confirmLogoutTitle: "Confirm log out",
        loginFailed: "Login failed",
        loginSuccess: "Login successful",
        loginRedirect: "Redirecting...",
      },
      general: {
        title: "General Settings",
        startup: "Add to startup",
        startupDescription:
          "Allow application to run automatically when system starts",
        searchEngine: "Search Engine",
        searchEngineDescription: "Choose default search engine for web search",
        selectSearchEngine: "Choose default search engine",
        language: "Language",
        languageDescription: "Choose language for application interface",
        attachExplorer: "Attach Windows Explorer",
        attachExplorerDescription:
          "Attach search window to the bottom-right corner of Windows Explorer",
      },
      index: {
        title: "Index Settings",
        indexingConfigs: "Indexing Configs",
        indexingConfigsDescription: "Configure disks and files to be indexed",
        addDisk: "Add",
        addDiskDescription: "Select disk to be indexed",
        updateIndex: "Update disk index",
        updateIndexDescription: "Re-scan selected disk and update index",
        selectDisk: "Select a disk",
        maxCache: "Max cache number",
        maxCacheDescription: "Set the maximum number of index cache",
        checkFileChange: "Check file change time(second)",
        checkFileChangeDescription:
          "Set the time interval to check file change (seconds)",
        ignoreConfigs: "Ignore Configs",
        ignoreConfigsDescription:
          "Add directory paths that do not need to be indexed",
        ignoreDirectories: "Ignore Directories",
        isDropPrevious: "Whether to drop previous file index",
        isDropPreviousDescription:
          "Whether to delete existing index data when updating index",
      },
      search: {
        title: "Search Settings",
        searchConfigs: "Search Configs",
        fuzzyMatch: "Enable fuzzy match",
        fuzzyMatchDescription:
          "Enable fuzzy search, only files with all input letters (in order) will be matched",
        gpuDevice: "GPU Device",
        gpuDeviceDescription: "Select GPU device for acceleration",
        enableGPU: "Enable GPU Accelerate",
        priorityFolder: "Select a priority folder",
        priorityFolderDescription:
          "Set priority folders for search, content in these folders will be prioritized in search results",
        priorityFolderClear: "Priority folder(Double click here to clear)",
      },
      dataType: {
        title: "Data Type Settings",
        suffixMap: "Data Type Suffix Map",
        suffixMapDescription:
          "Configure suffix priority for different file types",
        dataType: "Data Type",
        dataTypeDescription: "Classification name of file type",
        add: "Add",
        addDescription: "Add new data type or suffix",
        delete: "Delete",
        deleteDescription: "Delete selected data type or suffix",
        suffix: "Suffix",
        suffixDescription: "Suffix of file under this type",
        description: "Description",
        noDescription: "No description",
        addDataType: "Add Data Type",
        enterDataType: "Please enter the new data type you want to add",
        suffixTable: "Suffix Table",
      },
      cache: {
        title: "Cache Settings",
        cacheList: "Cache List",
        cacheListDescription: "Display all cached files",
        searchCache: "Search cache",
        searchCacheDescription: "Search specific file in cache list",
        path: "Path",
      },
      hotkey: {
        title: "Hotkey Settings",
        globalHotkey: "Global Hotkey",
        globalHotkeyDescription: "Set global hotkey to open search window",
        openParentFolder: "Open Parent Folder Key",
        openParentFolderDescription: "Set hotkey to open file parent folder",
        copyFolder: "Copy Folder Key",
        copyFolderDescription: "Set hotkey to copy file path",
        openWithAdmin: "Open With Admin Key",
        openWithAdminDescription:
          "Set hotkey to open file with admin permission",
        enableDoubleCtrl: "Enable Double-Click Ctrl to open search bar",
      },
      buttons: {
        save: "Save",
        cancel: "Cancel",
        ok: "OK",
        apply: "Apply",
        add: "Add",
        delete: "Delete",
        category: "Category",
        sort: "Sort",
        noGrouping: "No Grouping",
      },
      messages: {
        saveSuccess:
          "Save configs successfully, some configs take effect after restarting",
        saveFailed: "Save configs failed",
        confirmDelete: "Are you sure to delete?",
        confirmDeleteTitle: "Delete",
        invalidGlobalHotkey: "Global hotkey is invalid",
        invalidCopyKey: "Copy folder key is invalid",
        invalidOpenKey: "Open parent folder key is invalid",
        invalidOpenWithAdminKey: "Open with admin key is invalid",
        sameHotkeys: "Hotkey should not be the same",
        configError: "Config Error",
        confirmUpdateIndex: "Are you sure to update file index",
        confirmUpdateIndexTitle: "Update index",
        updatingIndex: "Start updating file index",
        alreadyUpdatingIndex: "Already updating file index",
        stillOptimizing: "Still optimizing database",
        browserOpenFailed: "Failed to open browser",
      },
      advanced: {
        title: "Advanced Settings",
        waitForSearchTasksTimeoutInMills:
          "Wait for search task timeout in milliseconds",
        waitForSearchTasksTimeoutInMillsDescription:
          "Set the maximum wait time for search task (milliseconds)",
        isDeleteUsnOnExit: "Delete Usn Journal when program exited",
        isDeleteUsnOnExitDescription:
          "Whether to delete USN journal when program exits",
        restartMonitorDiskThreadTimeoutInMills:
          "Restart file monitor thread timeout in milliseconds",
        restartMonitorDiskThreadTimeoutInMillsDescription:
          "Set restart interval for file monitor thread (milliseconds)",
        isReadPictureByLLM: "Read picture content by llm",
        isReadPictureByLLMDescription:
          "Whether to use large language model to parse image content",
        isEnableContentIndex: "Enable file content index",
        isEnableContentIndexDescription:
          "Whether to index file content (may increase index time)",
        cacheConfigs: "Cache Block Configs",
        cacheConfigsDescription: "Configure related parameters of index cache",
        minCacheBlockNumber: "Minimum memory cache block record number",
        minCacheBlockNumberDescription:
          "Set the minimum number of file records in each memory cache block",
        maxCacheBlockNumber: "Maximum memory cache block record number",
        maxCacheBlockNumberDescription:
          "Set the maximum number of file records in each memory cache block",
        minGpuCacheBlockNumber: "Minimum GPU cache block record number",
        minGpuCacheBlockNumberDescription:
          "Set the minimum number of file records in each GPU cache block",
        maxGpuCacheBlockNumber: "Maximum GPU cache block record number",
        maxGpuCacheBlockNumberDescription:
          "Set the maximum number of file records in each GPU cache block",
        debugMode: "Debug mode",
        debugModeDescription: "Enable debug related features",
        enableDebugMode: "Enable debug mode",
        enableDebugModeDescription:
          "Enable debug mode to open plugin service debug port for plugin development",
        pluginServiceDebugPort: "Plugin-Service remote debug port is {{port}}",
        jdkHome: "JDK HOME",
        jdkHomeDescription:
          "Set Java development tool package installation path",
        javaAgent: "Java Agent",
        javaAgentDescription:
          "Set Java Agent path for debugging and monitoring Java applications",
        llmSettings: "LLM Settings",
        llmSettingsDescription:
          "Configure large language model related settings",
        llmProvider: "LLM Provider",
        llmProviderDescription: "Choose large language model provider to use",
        ollamaAddress: "Ollama Server Address",
        ollamaAddressDescription: "Set Ollama server address",
        ollamaApiKey: "Ollama API Key",
        ollamaApiKeyDescription: "Ollama API key",
        ollamaModel: "Ollama Model Type",
        ollamaModelDescription: "Choose Ollama model to use",
      },
      effectiveMode: {
        instant: "Takes effect immediately",
        restart: "Takes effect after restart",
        reindex: "Takes effect after reindexing",
      },
      about: {
        title: "About",
        version: "Version {{version}}",
        description:
          "Aiverything is a powerful local file search tool that combines AI intelligence to help you find files faster.",
        openSource: "Thanks to the following open source projects",
        checkUpdate: "Check for Updates",
        checking: "Checking...",
        installUpdate: "Install Version {{version}}",
        noUpdateAvailable: "You're up to date",
        checkUpdateError: "Failed to check for updates",
        updateError: "Update failed, please try again later",
        copyright: "© 2025 Aiverything. All rights reserved.",
      },
    },
    searchBar: {
      copyPath: "Copy File Path",
      copyName: "Copy File Name",
      openParentDirectory: "Open Parent Directory",
      openFile: "Open File",
      openWithAdmin: "Open File With Admin",
      footerHint: "You can also callout Aiverything from right-bottom tray",
      openInTerminal: "Open In Terminal",
      filePathCopied: "File path copied",
      noPluginInstalled:
        "You have no plugin installed. Please install one from the store",
      noPluginFound: 'No plugin found containing "{{keyword}}"',
      uwpAppNotSupport:
        "UWP app does not support opening with admin permission",
      detail: {
        fileName: "File Name",
        fileLocation: "File Location",
        lastModified: "Last Modified",
        createdAt: "Created At",
        searchOnWeb: "Search On Web",
        searchNow: "Search Now",
        searchFor: "Search For {{inputValue}}",
        searchBrowserPrompt:
          'Press Enter to search "{{inputValue}}" on your default web browser.',
      },
      plugin: {
        identifier: "Identifier",
        version: "Version",
        author: "Author",
        description: "Description",
      },
      aiMode: "AI Search Mode",
      searchPlaceholder: "Find everything here...",
      aiSearchPlaceholder: "Search with AI...",
      aiSearching: "AI is searching...",
      aiSearchPrompt: "Please wait while AI analyzes your query",
      searching: "Searching...",
      searchPrompt: "Please wait while searching files",
      waitingForInput: "Waiting for input to complete...",
      waitingPrompt: "Please wait, preparing search",
      pressEnterToSearch: "Press Enter to search",
      aiModeHint: "Use AI for intelligent search and analysis",
      result: "result",
      results: "results",
      showRightPanel: "Show right panel",
      hideRightPanel: "Hide right panel",
      quickDirectToType: "Quick Direct to Type",
      categorizeByType: "Categorize by Type",
      sortByRelevance: "Sort by relevance",
      sortByName: "Sort by name",
      sortByDate: "Sort by date",
      sortBySize: "Sort by size",
      sortByType: "Sort by type",
      ascending: "Ascending",
      descending: "Descending",
      rankBy: "Rank By",
      relevance: "Relevance",
      lastModifiedTime: "Last Modified Time",
      name: "Name",
      type: "Type",
      fileSize: "File Size",
    },
    buttons: {
      category: "Category",
      sort: "Sort",
    },
    category: {
      all: "All",
      document: "Document",
      image: "Image",
      video: "Video",
      audio: "Audio",
      archive: "Archive",
      executable: "Executable",
    },
    dataTypes: {
      Search: "Search",
      Shortcut: "Recommend",
      Apps: "Apps",
      "UWP Apps": "UWP Apps",
      Folders: "Folders",
      Documents: "Documents",
      Sheets: "Sheets",
      Slides: "Slides",
      Pictures: "Pictures",
      Videos: "Videos",
      Audios: "Audios",
      Developer: "Developer",
      Others: "Others",
    },
    sort: {
      name: "Name",
      size: "Size",
      date: "Date",
      type: "Type",
    },
    tray: {
      settings: "Settings",
      pluginSettings: "Plugin Settings",
      pluginStore: "Plugin Store",
      restart: "Restart",
      quit: "Quit",
    },
    attach: {
      doubleClickShift: "Double click Shift to switch here",
    },
    core: {
      updateFileIndex: "Start to update file index",
      optimizeDatabase: "Start to optimize database",
      updateFileIndexDone: "Updating file index finished",
      optimizeDatabaseDone: "Optimizing database finished",
    },
    pluginSettings: {
      title: "Plugin Settings",
      settings: "{{pluginName}} Settings",
      root: "Root",
      currentLevelFields: "Current level fields:",
      fieldName: "Field Name",
      fieldType: "Field Type",
      fieldDescription: "Field Description",
      currentObjectStructure: "Current Object Structure:",
      addingFieldsTo: "Adding fields to: {{path}}",
      defaultValue: "Default Value",
      currentValue: "Current Value",
      objectField: {
        addFields: "Add fields to object",
      },
      booleanField: {
        defaultValue: "Default Value",
        currentValue: "Current Value",
        description: "Checked is true, unchecked is false",
      },
      types: {
        string: "String",
        number: "Number",
        boolean: "Boolean",
        object: "Object",
        array: "Array",
      },
      dialog: {
        title: "Add New Configuration",
        type: "Type",
        description: "Description",
        defaultValue: "Default Value",
        currentValue: "Current Value",
      },
      buttons: {
        cancel: "Cancel",
        add: "Add",
        addField: "Add Field",
        backToParent: "Back to Parent Object",
      },
      arrayItem: {
        title: "Item {{index}}",
      },
      store: {
        title: "Plugin Market",
        description: "Here you can find and install various plugins",
      },
      editJson: "Edit settings.json",
      openEntryPage: "Open Entry Page",
      loadError: "Plugin load error: {{error}}",
      checkUpdate: "Check for Updates",
      updateAvailable: "New version available: {{version}}",
      noUpdateAvailable: "You're up to date",
      checking: "Checking...",
      updateError: "Update check failed",
      clickToUpdate: "Click to update",
      updatePrompt: "New version {{version}} available, click to visit website",
      version: "Version",
      updateAllPlugins: "Checking updates for all plugins...",
      updateAvailableMultiple: "Multiple plugin updates available",
    },
    fileDetail: {
      aiSummary: "AI Summary",
      aiSummarize: "AI Summarize",
      viewSummary: "View AI Summary",
      aiSummaryFailed: "Summary failed, please try again later",
      noFile: "No file found",
      generating: "Generating summary...",
      fileEmpty: "File is empty",
      thinkingProcess: "Thinking Process",
    },
    common: {
      back: "Back",
      loading: "Loading...",
    },
    fileList: {
      viewAllCategories: "View all categories",
      categories: "categories",
      fileTypes: "File Types",
      fileCount: "{{count}} files",
    },
    update: {
      available: "New version available",
      newVersionAvailable:
        "New version available: {{version}}, please check for updates in settings",
    },
    shortcuts: {
      quickAccess: "Quick Access",
      thisPC: "This PC",
      recycleBin: "Recycle Bin",
      documents: "Documents",
      downloads: "Downloads",
      oneDrive: "OneDrive",
      settings: "Settings",
      controlPanel: "Control Panel",
      systemSettings: "System Settings",
      environmentVars: "Environment Variables",
      userAccounts: "User Accounts",
      networkSettings: "Network Settings",
      powerOptions: "Power Options",
      previousPage: "Previous Page",
      nextPage: "Next Page",
      goToPage: "Go to Page {{page}}",
      registryEditor: "Registry Editor",
      deviceManager: "Device Manager",
      diskManagement: "Disk Management",
      services: "Services",
      taskManager: "Task Manager",
      eventViewer: "Event Viewer",
      groupPolicy: "Group Policy",
    },
  },
};
