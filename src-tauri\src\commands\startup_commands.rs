use crate::utils::startup_util;

#[tauri::command]
pub fn has_startup() -> bool {
    return startup_util::has_startup() == 0;
}

#[tauri::command]
pub async fn add_startup() -> bool {
    let ret = startup_util::add_startup();
    match ret {
        Ok(_) => true,
        Err(e) => {
            eprintln!("Failed to add startup: {}", e);
            false
        }
    }
}

#[tauri::command]
pub async fn delete_startup() -> bool {
    let ret = startup_util::delete_startup();
    match ret {
        Ok(_) => true,
        Err(e) => {
            eprintln!("Failed to delete startup: {}", e);
            false
        }
    }
}
