### **Aiverything：基于 GPU 加速的高效文件搜索工具**  

在文件数量日益庞大的今天，传统的搜索工具往往难以满足高效办公的需求。**Aiverything** 结合 **GPU 并行计算、智能索引和优化排序算法**，提供更快、更精准的本地文件搜索体验，尤其在海量数据环境下，能显著提升搜索速度。目前，Aiverything 正处于 **Beta 测试阶段**，持续优化核心性能，以适应不同场景的高效搜索需求。  

## 官网
https://aiverything.me/

---  
### 下载地址
 - GitHub release： https://github.com/panwangwin/aiverything-official-forum/releases/download/0.2.1-beta/aiverything_0.2.1_x64-setup.exe   
 - 夸克网盘：https://pan.quark.cn/s/5ac2bf9154d7   
 - 查毒报告：https://www.virustotal.com/gui/file-analysis/OWIxY2IyZjYwMmVmNmE3ZGNiODdhYjc1NmQ5MTEwN2Q6MTc0MzQxNjcwNg==
 
---
目前我们仍在Beta-Test阶段，如果您在使用过程中发现任何问题、Bug，或有功能需求、操作体验方面的反馈，欢迎向我们提出建议或意见。   
您可以在这个Repo中提交相应的issue: https://github.com/panwangwin/aiverything-official-forum/issues   
或者加入我们的QQ交流群：893463594   


### **1️⃣ GPU 加速，让搜索更快**  
传统文件搜索通常依赖 CPU 串行扫描，面对大量文件时，速度受限。而 Aiverything 通过 **GPU 并行计算**，在支持 **AMD/NVIDIA** 显卡的设备上，充分利用显卡的计算能力，实现**多线程匹配**，相比传统方法，搜索效率提升**数倍甚至数十倍**。  

### **2️⃣ 多关键字搜索，匹配更精准**  
Aiverything 允许输入多个关键词，使用 `;`（分号）分隔，如 `test;file` 可同时匹配 `test` 和 `file`。  
此外，还支持 `|`（竖线）筛选搜索范围，例如：  
- `test;file|f` **仅搜索文件**  
- `test|c` **在文件内容中搜索（需开启内容索引）**  
- `test;file|case` **区分大小写匹配**  
#### 可用条件:
 - f (file / 文件)
 - d (directory / 目录)
 - full (full match / 全匹配)
 - case (case-sensitive / 区分大小写)
 - p (regex pattern / 正则匹配)
 - c (content indexing / 内容索引，需在设置中开启)

[![pEmPmHH.png](https://s21.ax1x.com/2025/02/07/pEmPmHH.png)](https://imgse.com/i/pEmPmHH)

灵活的搜索规则，使用户能够快速锁定目标文件，而无需逐步缩小搜索范围。  

### **3️⃣ 频次排序 + 缓存搜索，常用文件一触即达**  
Aiverything 具备 **智能缓存** 机制，记录用户的高频搜索和访问文件，并在每次搜索时，优先显示常用文件，**让结果更符合使用习惯**。  
- 在搜索框中输入 `:`（冒号），可直接从缓存中搜索最近使用过的文件。  
- 结合 **智能索引**，搜索速度随使用时间优化，**越用越快**。  

### **4️⃣ 一键召唤，随时开启搜索**  
- **快捷键调用**：默认 `CTRL + SHIFT + ALT + A`，快速打开搜索框。  
- **系统托盘入口**：支持点击 **Tray（托盘）图标** 直接打开搜索，无需快捷键操作，适应不同使用习惯。  

[![pEmPuEd.png](https://s21.ax1x.com/2025/02/07/pEmPuEd.png)](https://imgse.com/i/pEmPuEd)

### **5️⃣ 插件系统，扩展无限可能**  
Aiverything 内置**插件系统**，允许用户加载不同插件，实现更多个性化功能。例如：  
- **Actions插件**：自定义任务，一键启动多个程序。  
- **Terminal插件**：一键运行CMD命令，以及快捷的连接SSH服务器。  

[![pEsoq9x.png](https://s21.ax1x.com/2025/03/31/pEsoq9x.png)](https://imgse.com/i/pEsoq9x)
---
##### 插件采用 **模块化设计**，不影响主程序功能，未来还将开放 **插件 SDK**，让开发者创建自定义插件，扩展 Aiverything 的搜索能力。

---  
Aiverything 希望借助**前沿技术提升搜索效率**，减少用户在海量文件中查找的时间成本。我们欢迎测试用户提供反馈，共同打磨更高效、智能的文件搜索工具。