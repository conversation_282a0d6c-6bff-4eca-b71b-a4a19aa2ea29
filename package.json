{"name": "aiverything", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite --mode build", "build": "tsc && vite build --mode build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.1.1", "@hello-pangea/dnd": "^17.0.0", "@hookform/resolvers": "^4.1.2", "@mui/icons-material": "^6.4.0", "@mui/material": "^6.4.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@reduxjs/toolkit": "^2.2.5", "@supabase/supabase-js": "^2.49.1", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-deep-link": "^2.2.1", "@tauri-apps/plugin-dialog": "^2.2.1", "@tauri-apps/plugin-global-shortcut": "^2.2.0", "@tauri-apps/plugin-notification": "^2.2.2", "@tauri-apps/plugin-os": "^2.2.1", "@tauri-apps/plugin-process": "^2.2.1", "@tauri-apps/plugin-shell": "^2.2.1", "@tauri-apps/plugin-updater": "^2.7.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.4.7", "i18next": "^24.2.1", "i18next-browser-languagedetector": "^8.0.2", "input-otp": "^1.4.2", "lucide-react": "^0.477.0", "next-themes": "^0.4.4", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "8.10.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.0", "react-icons": "^5.3.0", "react-markdown": "^10.0.0", "react-redux": "^9.1.2", "react-resizable-panels": "^2.1.7", "react-router-dom": "^6.22.3", "recharts": "^2.15.1", "redux": "^5.0.1", "remark-gfm": "^4.0.1", "semver": "^7.7.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@tauri-apps/cli": "^2.5.0", "@types/node": "^22.7.5", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "css-loader": "^7.1.1", "depcheck": "^1.4.7", "eslint": "^9.1.1", "postcss": "^8.4.47", "postcss-loader": "^8.1.1", "postcss-preset-env": "^9.5.11", "react-refresh": "^0.14.0", "tailwindcss": "^3.4.3", "typescript": "^5.4.5", "vite": "^5.3.1"}}