# File-Engine-Core 开发文档

## 1. 项目概述

File-Engine-Core 是一个高性能的文件索引和搜索后端引擎，使用 Java 21 编写，集成了 C++ 原生组件，提供了高效的文件搜索功能。该项目无UI界面，通过 HTTP API 提供服务。

### 1.1 核心特性

- **高性能文件搜索**：支持文件名、路径和内容搜索
- **实时文件监控**：监控文件系统变化并实时更新索引
- **GPU加速**：支持CUDA和OpenCL加速文件搜索
- **智能缓存**：内存和GPU双重缓存机制
- **AI集成**：支持LLM（如Ollama）进行智能搜索和文件内容摘要
- **多种搜索模式**：支持模糊匹配、正则表达式、内容搜索等

### 1.2 技术栈

- **Java 21**：主要开发语言，使用虚拟线程
- **C++**：原生组件，用于性能优化
- **SQLite**：文件索引数据库
- **Lucene**：全文搜索引擎
- **CUDA/OpenCL**：GPU加速
- **Javalin**：HTTP API框架
- **Maven**：构建工具

## 2. 架构设计

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           Controller Layer                                  │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                        Core Controller                              │   │
│  │                     (HTTP API - Javalin)                           │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
                                      │
                                      ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                            Service Layer                                    │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             │
│  │ Database Service│  │ Daemon Service  │  │Lucene Index     │             │
│  │                 │  │                 │  │Service          │             │
│  │ • File Indexing │  │ • System Mgmt   │  │                 │             │
│  │ • Search Engine │  │ • Process Mgmt  │  │ • Full Text     │             │
│  │ • Cache Mgmt    │  │ • Resource Mgmt │  │   Search        │             │
│  │ • Real-time     │  │ • Config Mgmt   │  │ • Content Index │             │
│  │   Monitoring    │  │                 │  │ • Document      │             │
│  └─────────────────┘  └─────────────────┘  │   Analysis      │             │
│                                            └─────────────────┘             │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────────────────────────────────────┐ │
│  │ Event Management│  │                LLM Integration                  │ │
│  │    Service      │  │  ┌─────────────────┐  ┌─────────────────┐      │ │
│  │                 │  │  │   Ollama LLM    │  │   LLM Factory   │      │ │
│  │ • Event Bus     │  │  │                 │  │                 │      │ │
│  │ • Message Queue │  │  │ • Smart Search  │  │ • Plugin Mgmt   │      │ │
│  │ • Event Routing │  │  │ • File Summary  │  │ • API Gateway   │      │ │
│  │ • Task Dispatch │  │  │ • NL Query      │  │ • Session Mgmt  │      │ │
│  │ • Service Comm  │  │  └─────────────────┘  └─────────────────┘      │ │
│  └─────────────────┘  └─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
                                      │
                                      ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                         Data Storage Layer                                  │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             │
│  │   SQLite DB     │  │  Lucene Index   │  │  Memory Cache   │             │
│  │                 │  │                 │  │                 │             │
│  │ • File Metadata │  │ • Inverted      │  │ • Hot Data      │             │
│  │ • Path Index    │  │   Index         │  │ • Frequent      │             │
│  │ • Priority Map  │  │ • Document      │  │   Results       │             │
│  │ • Statistics    │  │   Content       │  │ • GPU Cache     │             │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘             │
└─────────────────────────────────────────────────────────────────────────────┘
                                      │
                                      ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                       Native Components (C++)                               │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             │
│  │  GPU Accelerator│  │  Path Matcher   │  │  File Monitor   │             │
│  │                 │  │                 │  │                 │             │
│  │ • CUDA Support  │  │ • Fast Matching │  │ • USN Watcher   │             │
│  │ • OpenCL Support│  │ • Regex Engine  │  │ • Real-time     │             │
│  │ • Parallel Proc │  │ • Fuzzy Search  │  │   Detection     │             │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘             │
│                                                                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐             │
│  │  USN Searcher   │  │  Encryption     │  │  System Utils   │             │
│  │                 │  │                 │  │                 │             │
│  │ • MFT Scanner   │  │ • Data Security │  │ • Disk Check    │             │
│  │ • File Indexing │  │ • Key Mgmt      │  │ • Window Check  │             │
│  │ • Bulk Import   │  │                 │  │ • Known Folders │             │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2.2 核心模块

#### 2.2.1 Controller层
- **Core.java**：HTTP API控制器，基于Javalin框架，处理所有REST请求和路由

#### 2.2.2 Service层
- **DatabaseService.java**：核心数据库服务
  - 文件索引管理
  - 搜索引擎核心逻辑
  - 缓存管理
  - 实时文件监控集成
  
- **DaemonService.java**：守护进程服务
  - 系统管理和监控
  - 进程生命周期管理
  - 资源管理
  - 配置管理和热更新
  
- **LuceneIndexService.java**：全文搜索服务
  - 文档内容索引
  - 全文搜索功能
  - 内容分析和分词
  - 搜索结果排序

- **EventManagement.java**：事件处理服务
  - 事件总线管理
  - 消息队列处理
  - 事件路由和分发
  - 任务调度
  - 服务间通信协调

#### 2.2.3 LLM集成层
- **LLMFactory.java**：LLM工厂类，管理不同LLM提供商
- **OllamaLLM.java**：Ollama集成实现
- **LLMInterface.java**：LLM统一接口定义

#### 2.2.4 数据存储层
- **SQLite数据库**：文件元数据、路径索引、优先级映射
- **Lucene索引**：倒排索引、文档内容存储
- **内存缓存**：热点数据、常用结果、GPU缓存

#### 2.2.5 Native组件层
- **GPU加速器**：CUDA/OpenCL并行处理
- **路径匹配器**：高性能字符串匹配
- **文件监控器**：USN实时监控
- **USN搜索器**：MFT扫描和批量导入
- **加密组件**：数据安全和密钥管理
- **系统工具**：磁盘检查、窗口检测、已知文件夹

### 2.3 事件驱动架构

File-Engine-Core采用事件驱动架构，各服务之间通过事件进行解耦通信：

#### 2.3.1 事件处理机制
- **EventManagement**：核心事件管理器，负责事件的注册、分发和执行
- **Event**：事件基类，所有系统事件继承此类
- **EventRegister**：事件注册注解，标记事件处理方法
- **EventListener**：事件监听注解，标记事件监听方法

#### 2.3.2 主要事件类型
- **搜索事件**：PrepareSearchEvent、StartSearchEvent、StopSearchEvent
- **数据库事件**：UpdateDatabaseEvent、OptimizeDatabaseEvent、FlushFileChangesEvent
- **缓存事件**：AddToCacheEvent、DeleteFromCacheEvent、GPUClearCacheEvent
- **配置事件**：SetConfigsEvent、CheckConfigsEvent
- **系统事件**：BootSystemEvent、CloseEvent、StartMonitorDiskEvent

**事件处理示例**：
```java
@EventRegister(registerClass = StartSearchEvent.class)
private static void startSearchEvent(Event event) {
    // 处理搜索事件
    var startSearchEvent = (StartSearchEvent) event;
    // ... 业务逻辑
    event.setReturnValue(searchTask);
}

@EventListener(listenClass = ConfigsEvent.class)
private static void onConfigChange(Event event) {
    // 监听配置变化
    // ... 响应逻辑
}
```

## 3. 核心功能详解

### 3.1 文件索引

系统使用SQLite数据库存储文件索引，采用分表策略（list0-list40）：

```java
// 根据文件名ASCII值计算表索引
int asciiSum = StringUtf8SumUtil.getStringSum(fileName);
int listGroup = asciiSum / 100;
listGroup = Math.min(listGroup, Constants.MAX_TABLE_NUM);
```

### 3.2 搜索机制

支持多种搜索模式：

1. **普通搜索**：文件名模糊匹配
2. **路径搜索**：在关键字前加 `/` 
3. **正则表达式**：使用 `|p` 后缀
4. **内容搜索**：使用 `|c` 后缀
5. **文件类型过滤**：`|f`（仅文件）、`|d`（仅文件夹）
6. **大小写敏感**：使用 `|case` 后缀

### 3.3 缓存策略

系统采用多级缓存：

```java
// 内存缓存
private final ConcurrentHashMap<String, Cache> tableCache = new ConcurrentHashMap<>();

// GPU缓存
if (isEnableGPUAccelerate) {
    GPUAccelerator.INSTANCE.initCache(key, cachesList.toArray(new String[0]));
}
```

### 3.4 实时监控

使用Windows USN（Update Sequence Number）技术实现文件系统实时监控：

```java
// 文件变化监控
String addFilePath = FileMonitor.INSTANCE.pop_add_file();
String deleteFilePath = FileMonitor.INSTANCE.pop_del_file();
```
## 4. 配置说明

### 4.1 主要配置项

配置文件位置：`user/settings.json`

```json
{
  "cacheNumLimit": 1000,
  "updateTimeLimit": 5,
  "ignorePath": "",
  "priorityFolder": "",
  "disks": "C:\\,D:\\",
  "isEnableGpuAccelerate": false,
  "gpuDevice": "",
  "llm": "NONE",
  "llmConfigs": {
    "OLLAMA": {
      "address": "http://localhost:11434",
      "modelType": "qwen2"
    }
  },
  "dataTypeSuffixMap": {
    "Document": ["txt", "doc", "docx", "pdf"],
    "Image": ["jpg", "png", "gif", "bmp"],
    "Video": ["mp4", "avi", "mkv", "wmv"],
    "Audio": ["mp3", "wav", "flac", "aac"]
  },
  "isEnableFuzzyMatch": true,
  "advancedConfigs": {
    "waitForSearchTasksTimeoutInMills": 300000,
    "isDeleteUsnOnExit": false,
    "restartMonitorDiskThreadTimeoutInMills": 600000,
    "isReadPictureByLLM": false,
    "isEnableContentIndex": false,
    "minCacheBlockNumber": 100,
    "maxCacheBlockNumber": 5000,
    "minGpuCacheBlockNumber": 3000,
    "maxGpuCacheBlockNumber": 20000
  }
}
```

### 4.2 高级配置项说明

- **waitForSearchTasksTimeoutInMills**：搜索任务超时时间
- **isDeleteUsnOnExit**：退出时是否删除USN记录
- **isEnableContentIndex**：是否启用内容索引
- **minCacheBlockNumber/maxCacheBlockNumber**：内存缓存数量范围
- **minGpuCacheBlockNumber/maxGpuCacheBlockNumber**：GPU缓存数量范围

## 5. 开发环境搭建

### 5.1 环境要求

- **JDK 21**：支持虚拟线程
- **Visual Studio 2022**：编译C++组件
- **Maven 3.6.1+**：构建工具
- **CUDA Toolkit**：GPU加速（可选）
- **OpenCL SDK**：GPU加速（可选）

### 5.2 编译步骤

1. **克隆项目**
```bash
git clone https://github.com/AIVERYTHING/File-Engine-Core.git
cd File-Engine-Core
```

2. **编译Java项目**
```bash
mvn clean compile package
```

### 5.3 开发调试

启用调试模式：
```java
// 设置系统属性
System.setProperty("File_Engine_Debug", "true");
```
