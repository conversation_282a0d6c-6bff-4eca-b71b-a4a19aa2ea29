import React, { useState, useRef, useEffect } from "react";
import { Command } from "@tauri-apps/plugin-shell";
import { useTranslation } from "react-i18next";
import ComputerIcon from "@mui/icons-material/Computer";
import DeleteIcon from "@mui/icons-material/Delete";
import DownloadIcon from "@mui/icons-material/Download";
import SettingsIcon from "@mui/icons-material/Settings";
import ControlPanelIcon from "@mui/icons-material/Dashboard";
import SystemIcon from "@mui/icons-material/Laptop";
import EnvVarIcon from "@mui/icons-material/Code";
import UserIcon from "@mui/icons-material/AccountCircle";
import NetworkIcon from "@mui/icons-material/Wifi";
import PowerIcon from "@mui/icons-material/Power";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import StorageIcon from "@mui/icons-material/Storage";
import BuildIcon from "@mui/icons-material/Build";
import SecurityIcon from "@mui/icons-material/Security";
import EventIcon from "@mui/icons-material/Event";
import PolicyIcon from "@mui/icons-material/Policy";
import { WebviewWindow } from "@tauri-apps/api/webviewWindow";

interface ShortcutItemProps {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  color?: string;
}

const ShortcutItem: React.FC<ShortcutItemProps> = ({
  icon,
  label,
  onClick,
  color = "text-blue-500",
}) => {
  return (
    <div
      className="flex flex-col items-center justify-center p-1 cursor-pointer rounded-lg transition-all duration-200 transform hover:scale-105 hover:bg-gray-100/90 dark:hover:bg-gray-800/90 hover:shadow-sm relative group mx-0.5"
      onClick={onClick}
      style={{ minHeight: "42px" }}
    >
      <div
        className={`${color} mb-0.5 flex items-center justify-center transition-transform duration-300 group-hover:transform group-hover:scale-110 relative`}
        style={{ height: "24px", width: "24px" }}
      >
        {icon}
        <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-current transition-all duration-300 opacity-0 group-hover:w-full group-hover:opacity-100"></span>
      </div>
      <div
        className="text-xs font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap truncate transition-all duration-200 group-hover:text-gray-900 dark:group-hover:text-white"
        style={{ maxWidth: "95%" }}
      >
        {label}
      </div>
      <div className="absolute inset-0 rounded-lg bg-current opacity-0 group-hover:opacity-5 transition-opacity duration-300"></div>
    </div>
  );
};

export const QuickShortcuts: React.FC = () => {
  const { t } = useTranslation();
  const [currentPage, setCurrentPage] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const ITEMS_PER_PAGE = 6; // 每页显示的快捷方式数量
  const [slideDirection, setSlideDirection] = useState<"left" | "right" | null>(
    null
  );
  const [isAnimating, setIsAnimating] = useState(false);

  // 隐藏主窗口
  const hideMainWindow = async () => {
    WebviewWindow.getByLabel("main").then((window) => {
      window.hide();
    });
  };

  // 所有快捷方式合并为一个列表
  const allShortcuts = [
    {
      icon: <ComputerIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.thisPC", "此电脑"),
      onClick: () => {
        hideMainWindow();
        Command.create("openExplorer").execute();
      },
      color: "text-blue-500",
    },
    {
      icon: <DeleteIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.recycleBin", "回收站"),
      onClick: () => {
        hideMainWindow();
        Command.create("openRecycleBin").execute();
      },
      color: "text-red-500",
    },
    {
      icon: <DownloadIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.downloads", "下载"),
      onClick: () => {
        hideMainWindow();
        Command.create("openDownloads").execute();
      },
      color: "text-green-500",
    },
    {
      icon: <ControlPanelIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.controlPanel", "控制面板"),
      onClick: () => {
        hideMainWindow();
        Command.create("openControlPanel").execute();
      },
      color: "text-purple-500",
    },
    {
      icon: <SystemIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.systemSettings", "系统设置"),
      onClick: () => {
        hideMainWindow();
        Command.create("openSystemSettings").execute();
      },
      color: "text-indigo-500",
    },
    {
      icon: <EnvVarIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.environmentVars", "环境变量"),
      onClick: () => {
        hideMainWindow();
        try {
          // 先尝试使用 Windows 设置打开系统属性
          Command.create("openEnvVarSettings").execute();
        } catch (error) {
          console.error(`无法打开环境变量设置: ${error}`);
        }
      },
      color: "text-cyan-500",
    },
    {
      icon: <UserIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.userAccounts", "用户账户"),
      onClick: () => {
        hideMainWindow();
        Command.create("openUserAccounts").execute();
      },
      color: "text-pink-500",
    },
    {
      icon: <NetworkIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.networkSettings", "网络设置"),
      onClick: () => {
        hideMainWindow();
        Command.create("openNetworkSettings").execute();
      },
      color: "text-orange-500",
    },
    {
      icon: <PowerIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.powerOptions", "电源选项"),
      onClick: () => {
        hideMainWindow();
        Command.create("openPowerOptions").execute();
      },
      color: "text-yellow-500",
    },
    {
      icon: <BuildIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.registryEditor", "注册表编辑器"),
      onClick: () => {
        hideMainWindow();
        Command.create("openRegistryEditor").execute();
      },
      color: "text-red-600",
    },
    {
      icon: <StorageIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.deviceManager", "设备管理器"),
      onClick: () => {
        hideMainWindow();
        Command.create("openDeviceManager").execute();
      },
      color: "text-blue-600",
    },
    {
      icon: <StorageIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.diskManagement", "磁盘管理"),
      onClick: () => {
        hideMainWindow();
        Command.create("openDiskManagement").execute();
      },
      color: "text-green-600",
    },
    {
      icon: <SecurityIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.services", "服务"),
      onClick: () => {
        hideMainWindow();
        Command.create("openServices").execute();
      },
      color: "text-purple-600",
    },
    {
      icon: <SettingsIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.taskManager", "任务管理器"),
      onClick: () => {
        hideMainWindow();
        Command.create("openTaskManager").execute();
      },
      color: "text-indigo-600",
    },
    {
      icon: <EventIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.eventViewer", "事件查看器"),
      onClick: () => {
        hideMainWindow();
        Command.create("openEventViewer").execute();
      },
      color: "text-cyan-600",
    },
    {
      icon: <PolicyIcon style={{ fontSize: "1.35rem" }} />,
      label: t("shortcuts.groupPolicy", "组策略"),
      onClick: () => {
        hideMainWindow();
        Command.create("openGroupPolicy").execute();
      },
      color: "text-pink-600",
    },
  ];

  // 计算总页数
  const totalPages = Math.ceil(allShortcuts.length / ITEMS_PER_PAGE);

  // 获取当前页面应显示的快捷方式
  const currentPageShortcuts = allShortcuts.slice(
    currentPage * ITEMS_PER_PAGE,
    (currentPage + 1) * ITEMS_PER_PAGE
  );

  // 翻页函数
  const prevPage = () => {
    if (isAnimating) return;
    setSlideDirection("right");
    setIsAnimating(true);
    setTimeout(() => {
      setCurrentPage((prev) => (prev > 0 ? prev - 1 : totalPages - 1));
      setTimeout(() => {
        setIsAnimating(false);
      }, 100);
    }, 200);
  };

  const nextPage = () => {
    if (isAnimating) return;
    setSlideDirection("left");
    setIsAnimating(true);
    setTimeout(() => {
      setCurrentPage((prev) => (prev < totalPages - 1 ? prev + 1 : 0));
      setTimeout(() => {
        setIsAnimating(false);
      }, 100);
    }, 200);
  };

  // 添加鼠标滚轮事件处理
  useEffect(() => {
    const handleWheel = (event: WheelEvent) => {
      // 防止事件冒泡和默认行为
      event.preventDefault();

      // 检测滚轮方向，向下滚动是正值，向上滚动是负值
      if (event.deltaY > 0) {
        nextPage(); // 向下滚动，显示下一页
      } else {
        prevPage(); // 向上滚动，显示上一页
      }
    };

    // 获取容器元素并添加事件监听
    const container = containerRef.current;
    if (container) {
      container.addEventListener("wheel", handleWheel, { passive: false });

      // 清理函数
      return () => {
        container.removeEventListener("wheel", handleWheel);
      };
    }
  }, [currentPage, totalPages, isAnimating]); // 添加isAnimating作为依赖项

  return (
    <div
      className="w-full bg-transparent shadow-none animate-fadeIn flex flex-col justify-between items-center"
      style={{
        height: "9.5rem",
        display: "flex",
        flexDirection: "column",
        marginTop: "0.5rem",
        padding: "0.25rem 0.75rem 0.25rem 0.75rem",
      }}
      ref={containerRef}
    >
      <div
        className="relative w-full max-w-md mx-auto overflow-hidden flex-grow"
        style={{ height: "6.5rem" }}
      >
        <div
          className={`absolute w-full grid grid-cols-3 sm:grid-cols-6 gap-1 transition-all duration-250 ease-in-out px-1 ${
            isAnimating
              ? slideDirection === "left"
                ? "transform -translate-x-6 opacity-0"
                : "transform translate-x-6 opacity-0"
              : "transform translate-x-0 opacity-100"
          }`}
        >
          {currentPageShortcuts.map((shortcut, index) => (
            <ShortcutItem
              key={`${currentPage}-${index}`}
              icon={shortcut.icon}
              label={shortcut.label}
              onClick={shortcut.onClick}
              color={shortcut.color}
            />
          ))}
        </div>
      </div>

      {/* 翻页控制 */}
      <div
        className="flex justify-center items-center gap-4 h-5"
        style={{
          position: "relative",
          zIndex: 5,
          marginTop: "0.25rem",
          marginBottom: "0.125rem",
        }}
      >
        <button
          onClick={prevPage}
          disabled={isAnimating}
          className={`p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-500 dark:text-gray-400 transition-all duration-200 ${
            !isAnimating
              ? "hover:scale-110 hover:text-blue-500 active:scale-90"
              : ""
          }`}
          aria-label={t("shortcuts.previousPage", "上一页")}
          style={{
            height: "18px",
            width: "18px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <ChevronLeftIcon style={{ fontSize: "14px" }} />
        </button>

        <div className="flex gap-1.5 mx-1.5">
          {Array.from({ length: totalPages }).map((_, index) => (
            <div
              key={index}
              className={`transition-all duration-200 cursor-pointer ${
                currentPage === index
                  ? "w-3.5 h-1.5 bg-blue-500 rounded-full"
                  : "w-1.5 h-1.5 bg-gray-300 dark:bg-gray-600 rounded-full hover:bg-blue-400 dark:hover:bg-blue-400"
              }`}
              onClick={() => {
                if (isAnimating || index === currentPage) return;

                if (index > currentPage) {
                  setSlideDirection("left");
                } else {
                  setSlideDirection("right");
                }

                setIsAnimating(true);
                setTimeout(() => {
                  setCurrentPage(index);
                  setTimeout(() => {
                    setIsAnimating(false);
                  }, 100);
                }, 200);
              }}
              aria-label={t("shortcuts.goToPage", "前往第{{page}}页", {
                page: index + 1,
              })}
            />
          ))}
        </div>

        <button
          onClick={nextPage}
          disabled={isAnimating}
          className={`p-0.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-500 dark:text-gray-400 transition-all duration-200 ${
            !isAnimating
              ? "hover:scale-110 hover:text-blue-500 active:scale-90"
              : ""
          }`}
          aria-label={t("shortcuts.nextPage", "下一页")}
          style={{
            height: "18px",
            width: "18px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <ChevronRightIcon style={{ fontSize: "14px" }} />
        </button>
      </div>
    </div>
  );
};
