# File-Engine-Core API 详细文档

## 目录

1. [基础信息](#基础信息)
2. [搜索相关API](#搜索相关API)
3. [配置管理API](#配置管理API)
4. [缓存管理API](#缓存管理API)
5. [索引管理API](#索引管理API)
6. [AI功能API](#AI功能API)
7. [系统管理API](#系统管理API)
8. [响应格式](#响应格式)
9. [错误码说明](#错误码说明)

## 基础信息

- **Base URL**: `https://localhost:port`
- **Content-Type**: `application/json`
- **Authentication**: 无需认证
- **SSL**: 使用自签名证书

## 搜索相关API

### 1. 搜索文件（同步）

搜索文件并等待搜索完成返回结果。

**接口地址**: `POST /search`

**请求参数**:
- `searchText` (string, required): 搜索关键字
- `maxResultNum` (int, required): 最大结果数量

**搜索语法**:
- 普通搜索: `文件名`
- 路径搜索: `/文件夹路径`
- 正则表达式: `.*\.txt|p`
- 内容搜索: `关键字|c`
- 仅文件: `关键字|f`
- 仅文件夹: `关键字|d`
- 区分大小写: `关键字|case`
- 组合条件: `关键字|f;case`

**响应结果**:
```json
{
  "status": 200,
  "data": {
    "uuid": "search-task-uuid",
    "isDone": true,
    "data": [
      {
        "dataType": "Document",
        "results": [
          {
            "path": "C:\\Documents\\file.txt",
            "isPrioritizeResult": false,
            "isFuzzyMatched": false,
            "isContentMatched": false
          }
        ]
      }
    ]
  }
}
```

### 2. 异步搜索

发起异步搜索，立即返回任务ID。

**接口地址**: `POST /searchAsync`

**请求参数**:
- `searchText` (string, required): 搜索关键字
- `maxResultNum` (int, required): 最大结果数量

**响应结果**:
```json
{
  "status": 200,
  "data": "search-task-uuid"
}
```

### 3. 预搜索

预先准备搜索任务，提高搜索响应速度。

**接口地址**: `POST /prepareSearch`

**请求参数**:
- `searchText` (string, required): 搜索关键字
- `maxResultNum` (int, required): 最大结果数量

**响应结果**:
```json
{
  "status": 200,
  "data": "search-task-uuid"
}
```

### 4. 获取搜索结果

获取异步搜索或预搜索的结果。

**接口地址**: `GET /result`

**请求参数**:
- `uuid` (string, optional): 搜索任务UUID，不提供则返回最新任务结果

**响应结果**:
```json
{
  "status": 200,
  "data": {
    "uuid": "search-task-uuid",
    "isDone": true,
    "data": [
      {
        "dataType": "Document",
        "results": [
          {
            "path": "C:\\Documents\\file.txt",
            "isPrioritizeResult": false,
            "isFuzzyMatched": false,
            "isContentMatched": false
          }
        ]
      }
    ]
  }
}
```

### 5. 获取UWP应用搜索结果

获取UWP应用的搜索结果。**只有在调用了 `/prepareSearch` 才可以获取到uwp搜索结果。**

**接口地址**: `GET /uwpResult`

**请求参数**:
- `uuid` (string, required): 搜索任务UUID

**响应结果**:
```json
{
  "status": 200,
  "data": [
    {
      "displayName": "Calculator",
      "appUserModelId": "Microsoft.WindowsCalculator_8wekyb3d8bbwe!App",
      "packageName": "Microsoft.WindowsCalculator",
      "logoPath": "path/to/logo.png"
    }
  ]
}
```

### 6. 停止搜索

停止所有正在进行的搜索任务。

**接口地址**: `DELETE /search`

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

### 7. 删除搜索任务

删除指定的搜索任务。**删除搜索任务并不会停止该搜索任务，仅仅是从队列中移除防止内存泄露**

**接口地址**: `DELETE /result`

**请求参数**:
- `uuid` (string, required): 搜索任务UUID

**响应结果**:
```json
{
  "status": 200,
  "data": true
}
```

### 8. 获取常用文件

获取常用文件列表。

**接口地址**: `GET /frequentResult`

**请求参数**:
- `num` (int, required): 返回数量
- `searchText` (string, optional): 过滤关键字

**响应结果**:
```json
{
  "status": 200,
  "data": [
    {
      "dataType": "Document",
      "results": [
        {
          "path": "C:\\Documents\\常用文件.txt",
          "isPrioritizeResult": true,
          "isFuzzyMatched": false,
          "isContentMatched": false
        }
      ]
    }
  ]
}
```

## 配置管理API

### 1. 获取配置

获取当前系统配置。

**接口地址**: `GET /config`

**响应结果**:
```json
{
  "status": 200,
  "data": {
    "cacheNumLimit": 1000,
    "updateTimeLimit": 5,
    "ignorePath": "",
    "priorityFolder": "",
    "disks": "C:\\,D:\\",
    "isEnableGpuAccelerate": false,
    "gpuDevice": "",
    "llm": "NONE",
    "llmConfigs": {
      "OLLAMA": {
        "address": "http://localhost:11434",
        "modelType": "qwen2"
      }
    },
    "dataTypeSuffixMap": [
      {
        "dataType": "Document",
        "suffix": ["txt", "doc", "docx", "pdf"]
      }
    ],
    "isEnableFuzzyMatch": true,
    "advancedConfigs": {
      "waitForSearchTasksTimeoutInMills": 300000,
      "isDeleteUsnOnExit": false,
      "restartMonitorDiskThreadTimeoutInMills": 600000,
      "isReadPictureByLLM": false,
      "isEnableContentIndex": false,
      "minCacheBlockNumber": 100,
      "maxCacheBlockNumber": 5000,
      "minGpuCacheBlockNumber": 3000,
      "maxGpuCacheBlockNumber": 20000
    }
  }
}
```

### 2. 更新配置

更新系统配置。

**接口地址**: `POST /config`

**请求体**:
```json
{
  "cacheNumLimit": 1000,
  "updateTimeLimit": 5,
  "ignorePath": "",
  "priorityFolder": "",
  "disks": "C:\\,D:\\",
  "isEnableGpuAccelerate": false,
  "gpuDevice": "",
  "llm": "OLLAMA",
  "llmConfigs": {
    "OLLAMA": {
      "address": "http://localhost:11434",
      "modelType": "qwen2"
    }
  },
  "dataTypeSuffixMap": [
    {
      "dataType": "Document",
      "suffix": ["txt", "doc", "docx", "pdf"]
    }
  ],
  "isEnableFuzzyMatch": true,
  "advancedConfigs": {
    "waitForSearchTasksTimeoutInMills": 300000,
    "isDeleteUsnOnExit": false,
    "restartMonitorDiskThreadTimeoutInMills": 600000,
    "isReadPictureByLLM": false,
    "isEnableContentIndex": false,
    "minCacheBlockNumber": 100,
    "maxCacheBlockNumber": 5000,
    "minGpuCacheBlockNumber": 3000,
    "maxGpuCacheBlockNumber": 20000
  }
}
```

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

## 缓存管理API

### 1. 添加到缓存

将文件添加到常用缓存。

**接口地址**: `POST /cache`

**请求参数**:
- `path` (string, required): 文件路径

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

### 2. 获取缓存列表

获取所有缓存的文件路径。

**接口地址**: `GET /cache`

**响应结果**:
```json
{
  "status": 200,
  "data": [
    "C:\\Documents\\file1.txt",
    "C:\\Documents\\file2.txt"
  ]
}
```

### 3. 删除缓存

从缓存中删除指定文件。

**接口地址**: `DELETE /cache`

**请求参数**:
- `path` (string, required): 文件路径

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

## 索引管理API

### 1. 更新索引

更新文件索引数据库。

**接口地址**: `POST /update`

**请求参数**:
- `isDropPrevious` (boolean, required): 是否删除之前的索引

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

### 2. 优化数据库

执行数据库优化操作。

**接口地址**: `POST /optimize`

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

### 3. 刷新文件变化

立即刷新文件系统变化到数据库。

**接口地址**: `POST /flushFileChanges`

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

## AI功能API

### 1. AI搜索

使用AI进行智能搜索。

**接口地址**: `POST /searchAI`

**请求参数**:
- `maxResultNum` (int, optional): 最大结果数量

**请求体**: 用户的自然语言查询

**响应结果**:
```json
{
  "status": 200,
  "data": {
    "uuid": "search-task-uuid",
    "isDone": true,
    "data": [
      {
        "dataType": "Document",
        "results": [
          {
            "path": "C:\\Documents\\相关文件.txt",
            "isPrioritizeResult": false,
            "isFuzzyMatched": false,
            "isContentMatched": false
          }
        ]
      }
    ]
  }
}
```

### 2. 文件摘要

对指定文件进行AI摘要。

**接口地址**: `POST /summarizeAI`

**请求参数**:
- `file` (string, required): 文件路径

**响应结果**:
```json
{
  "status": 200,
  "data": "session-id"
}
```

### 3. 获取摘要结果

获取文件摘要的结果。

**接口地址**: `GET /summarizeAI`

**请求参数**:
- `sessionId` (string, required): 会话ID

**响应结果**:
```json
{
  "status": 200,
  "data": {
    "content": "文件摘要内容...",
    "isComplete": false
  }
}
```

### 4. 摘要流式接口

通过SSE获取实时摘要流。

**接口地址**: `GET /summaryStream`

**请求参数**:
- `sessionId` (string, required): 会话ID

**响应格式**: Server-Sent Events

### 5. 删除AI会话

删除指定的AI会话。

**接口地址**: `DELETE /summarizeAI`

**请求参数**:
- `sessionId` (string, required): 会话ID

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

## 系统管理API

### 1. 获取系统状态

获取数据库和系统状态。

**接口地址**: `GET /status`

**响应结果**:
```json
{
  "status": 200,
  "data": "NORMAL"
}
```

状态值说明：
- `NORMAL`: 正常运行
- `MANUAL_UPDATE`: 正在更新索引，未切换到临时数据库
- `VACUUM`: 数据库优化中
- `_TEMP`: 正在更新索引，已切换到临时数据库

### 2. 获取GPU设备

获取可用的GPU设备列表。

**接口地址**: `GET /gpu`

**响应结果**:
```json
{
  "status": 200,
  "data": {
    "cuda0": "NVIDIA GeForce RTX 3080",
    "opencl1": "Intel(R) UHD Graphics 630"
  }
}
```

### 3. 获取磁盘列表

获取可用的磁盘列表。

**接口地址**: `GET /disk`

**响应结果**:
```json
{
  "status": 200,
  "data": [
    "C:\\",
    "D:\\",
    "E:\\"
  ]
}
```

### 4. 获取文件图标

获取文件或应用的图标。

**接口地址**: `GET /icon`

**请求参数**:
- `path` (string, required): 文件路径
- `isUwp` (string, optional): 是否为UWP应用

**响应结果**: Base64编码的图片数据

### 5. 运行UWP应用

启动UWP应用。

**接口地址**: `POST /runUwp`

**请求参数**:
- `appUserModelId` (string, required): UWP应用ID

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

### 6. 关闭系统

关闭File-Engine-Core系统。

**接口地址**: `POST /close`

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

### 7. 获取版本信息

**接口地址**: `GET /version`

**响应结果**:
```json
{
  "status": 200,
  "data": "1.0.0"
}
```

**接口地址**: `GET /buildVersion`

**响应结果**:
```json
{
  "status": 200,
  "data": "20241201120000"
}
```

## 后缀优先级管理API（已弃用，后缀优先级会根据配置进行同步）

### 1. 获取后缀优先级

**接口地址**: `GET /suffixPriority`

**响应结果**:
```json
{
  "status": 200,
  "data": {
    "txt": 10,
    "doc": 9,
    "pdf": 8,
    "jpg": 5,
    "mp4": 3
  }
}
```

### 2. 添加后缀优先级

**接口地址**: `POST /suffixPriority`

**请求参数**:
- `suffix` (string, required): 文件后缀
- `priority` (int, required): 优先级

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

### 3. 更新后缀优先级

**接口地址**: `PUT /suffixPriority`

**请求参数**:
- `oldSuffix` (string, required): 原后缀
- `newSuffix` (string, required): 新后缀
- `priority` (int, required): 优先级

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

### 4. 删除后缀优先级

**接口地址**: `DELETE /suffixPriority`

**请求参数**:
- `suffix` (string, required): 文件后缀

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

### 5. 清空后缀优先级

**接口地址**: `DELETE /clearSuffixPriority`

**响应结果**:
```json
{
  "status": 200,
  "data": null
}
```

## 响应格式

所有API响应都遵循统一格式：

```json
{
  "status": 200,
  "data": "响应数据"
}
```

- `status`: HTTP状态码
- `data`: 响应数据，可以是字符串、对象、数组或null

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200    | 请求成功 |
| 400    | 请求参数错误 |
| 404    | 接口不存在 |
| 500    | 服务器内部错误 |

## 使用示例

### JavaScript

```javascript
// 搜索文件
async function searchFiles(searchText, maxResults = 100) {
  const response = await fetch(`https://localhost:port/search?searchText=${encodeURIComponent(searchText)}&maxResultNum=${maxResults}`, {
    method: 'POST'
  });
  return await response.json();
}

// 获取配置
async function getConfig() {
  const response = await fetch('https://localhost:port/config');
  return await response.json();
}

// AI搜索
async function aiSearch(query) {
  const response = await fetch('https://localhost:port/searchAI', {
    method: 'POST',
    headers: {
      'Content-Type': 'text/plain'
    },
    body: query
  });
  return await response.json();
}
```

## 注意事项

1. **SSL证书**: 系统使用自签名证书，客户端需要忽略证书验证或添加信任
2. **编码**: 所有字符串参数使用UTF-8编码
3. **超时**: 长时间运行的操作（如更新索引）可能需要较长时间完成
4. **并发**: 系统支持并发请求，但某些操作（如更新索引）是互斥的
5. **权限**: 文件监控功能需要管理员权限运行

---

更多详细信息请参考[开发文档](./开发文档.md)。 