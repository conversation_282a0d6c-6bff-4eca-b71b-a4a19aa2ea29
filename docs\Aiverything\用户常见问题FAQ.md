# Aiverything 用户常见问题 FAQ

## 📋 目录
- [安装相关问题](#安装相关问题)
- [启动和基础使用问题](#启动和基础使用问题)
- [搜索功能问题](#搜索功能问题)
- [GPU加速问题](#gpu加速问题)
- [AI功能问题](#ai功能问题)
- [插件系统问题](#插件系统问题)
- [快捷键问题](#快捷键问题)
- [性能优化问题](#性能优化问题)
- [其他常见问题](#其他常见问题)

---

## 🔧 安装相关问题

### Q1: 下载的安装包无法运行或提示病毒
**A:** 
1. **确保下载来源**：只从官方渠道下载
   - [Aiverything](https://aiverything.me)
   - [GitHub Release](https://github.com/panwangwin/aiverything-official-forum/releases)
2. **关闭杀毒软件**：部分杀毒软件可能误报，暂时关闭后重新安装
3. **检查系统兼容性**：确保系统为 Windows 10/11 64位

### Q2: 安装后程序无法启动
**A:**
1. **检查系统环境**：
   - 确保已安装 Visual C++ Redistributable
   - 更新 Windows 系统到最新版本
2. **以管理员权限运行**：右键程序图标选择"以管理员身份运行"
3. **检查防火墙设置**：将 Aiverything 添加到防火墙白名单
4. **查看错误日志**：检查程序安装目录下的日志文件，`File-Engine-Core.log`以及`Plugin-Service.log`

### Q3: 安装后系统托盘没有图标
**A:**
1. **检查系统托盘设置**：
   - 右键任务栏 → 任务栏设置 → 选择哪些图标显示在任务栏上
   - 确保 Aiverything 设置为"开启"
2. **重启程序**：完全退出后重新启动
3. **检查启动项**：在设置中确认"开机启动"已开启

---

## 🚀 启动和基础使用问题

### Q4: 程序启动很慢，首次索引时间过长
**A:**
1. **正常现象**：首次启动需要建立文件索引，时间取决于文件数量
2. **优化建议**：
   - 在索引设置中排除不必要的磁盘
   - 设置忽略目录（如系统文件夹、临时文件夹）
   - 使用 SSD 硬盘可显著提升索引速度

### Q5: 搜索窗口无法打开
**A:**
1. **检查程序状态**：确认 Aiverything 正在运行（系统托盘有图标）
2. **尝试不同启动方式**：
   - 全局快捷键：`Ctrl + Shift + Alt + A`
   - 双击 Ctrl 键
   - 系统托盘左键单击
3. **重启程序**：完全退出后重新启动

---

## 🔍 搜索功能问题

### Q6: 搜索结果不完整或过时
**A:**
1. **检查索引设置**：
   - 确认目标磁盘已包含在索引范围内
   - 检查忽略目录设置是否排除了目标文件夹
2. **手动更新索引**：
   - 进入设置 → 索引设置 → 点击"更新索引"
   - 等待索引更新完成

### Q7: 搜索速度很慢
**A:**
1. **启用GPU加速**：
   - 在搜索设置中选择合适的GPU设备
   - 确保显卡驱动为最新版本
2. **优化索引范围**：
   - 只索引必要的磁盘和文件夹
   - 排除不常用的大型目录
3. **调整缓存设置**：
   - 增加缓存大小（如果内存充足）
   - 使用 SSD 存储索引文件

### Q9: 内容搜索不工作
**A:**
1. **启用内容索引**：
   - 进入设置 → 高级设置
   - 开启"内容索引"功能
2. **重建索引**：内容索引需要重新建立
3. **使用正确语法**：搜索时添加 `|c` 过滤器

---

## 🎮 GPU加速问题

### Q10: GPU加速不工作
**A:**
1. **检查硬件支持**：
   - 确认显卡支持 CUDA（NVIDIA）或 OpenCL（AMD）
   - 查看显卡型号和规格
2. **更新驱动程序**：
   - 下载并安装最新的显卡驱动
   - 重启计算机
3. **选择正确设备**：
   - 进入设置 → 搜索设置
   - 在GPU设备中选择正确的显卡
4. **检查GPU状态**：
   - 使用GPU-Z等工具检查显卡状态
   - 确认显卡没有被其他程序占用

### Q11: 启用GPU加速后程序崩溃
**A:**
1. **降低GPU使用强度**：在高级设置中调整GPU缓存参数
2. **检查显卡温度**：确保显卡散热正常
3. **回退到CPU模式**：临时禁用GPU加速
4. **更新显卡驱动**：尝试不同版本的驱动程序

### Q12: 非NVIDIA显卡下显存占用过高
**A:**
这是一个已知的技术限制问题：

1. **技术原因**：
   - OpenCL不支持核函数中使用 `size_t` 类型进行动态寻址
   - 字符串必须设置为固定长度，无法像CUDA那样进行动态内存优化
   - 导致OpenCL模式下内存预分配较多，占用量偏高
   - **程序内部会进行显存占用监控，如果其他软件占用超过50%，则会主动释放GPU缓存，无须担心会影响别的软件的性能问题**

2. **解决方案**：
   - **优先使用CUDA**：如果有NVIDIA显卡，建议选择CUDA模式
   - **调整缓存参数**：在高级设置中降低GPU缓存块大小
   - **减少索引范围**：限制同时处理的文件数量
   - **增加系统内存**：考虑升级到更大内存

---

## 🤖 AI功能问题

### Q13: AI功能无法使用
**A:**
1. **检查网络连接**：
   - 确保网络连接正常
   - 检查防火墙是否阻止了程序联网
2. **配置Ollama**：
   - 确认Ollama已正确安装和配置
   - 检查Ollama服务是否正在运行
3. **检查AI设置**：
   - 进入设置 → 高级设置 → 大语言模型
   - 确认AI相关配置正确

### Q14: AI搜索结果不准确
**A:**
1. **改进搜索描述**：使用更准确的自然语言描述
2. **检查模型配置**：确认使用的AI模型适合搜索
3. **更新模型**：尝试使用更新版本的语言模型

### Q15: AI文件摘要功能不可用
**A:**
1. **检查文件格式**：确认文件格式受支持（PDF、Word、Excel等）
2. **检查文件大小**：过大的文件可能需要更长处理时间
3. **网络问题**：检查AI服务的网络连接

---

## 🔌 插件系统问题

### Q16: 插件无法加载
**A:**
1. **检查Java环境**：
   - 确保`core/jre`文件夹下的Java运行环境（JRE）完整
   - 确保使用Java 21或更高版本
2. **检查插件兼容性**：
   - 确认插件版本与当前Aiverything版本兼容
   - 查看插件说明文档
3. **查看错误日志**：
   - 检查插件错误日志获取详细信息
   - 根据错误信息进行排查

### Q17: 插件模式无法进入
**A:**
1. **检查输入格式**：确保输入 `>` 开头的命令
2. **重启程序**：完全退出后重新启动
3. **检查插件服务**：确认插件服务正常运行

### Q18: 插件界面显示异常
**A:**
1. **检查浏览器内核**：确保系统WebView2组件正常
2. **清除插件缓存**：在插件设置中清除缓存
3. **重新安装插件**：卸载后重新安装问题插件

---

## ⌨️ 快捷键问题

### Q19: 全局快捷键不响应
**A:**
1. **检查快捷键冲突**：
   - 确认是否有其他程序占用了相同快捷键
   - 使用工具检查快捷键占用情况
2. **以管理员权限运行**：
   - 右键程序图标选择"以管理员身份运行"
   - 部分快捷键需要管理员权限
3. **重新设置快捷键**：
   - 进入设置 → 快捷键设置
   - 修改为其他组合键
4. **检查键盘状态**：确认键盘按键正常工作

### Q20: 双击Ctrl不工作
**A:**
1. **检查功能开关**：
   - 进入设置 → 快捷键设置
   - 确认"双击Ctrl"功能已开启
2. **键盘兼容性**：部分键盘可能不支持此功能

### Q21: 文件操作快捷键异常
**A:**
1. **检查自定义设置**：
   - 进入设置 → 快捷键设置
   - 确认文件操作快捷键配置正确
2. **检查文件权限**：确保对目标文件有相应操作权限

---

## ⚡ 性能优化问题

### Q22: 程序占用内存过高
**A:**
1. **调整缓存大小**：
   - 进入设置 → 高级设置 → 缓存块大小设置
   - 根据实际内存情况降低缓存块大小
2. **减少索引范围**：
   - 只索引必要的磁盘和文件夹
   - 排除不常用的大型目录

### Q23: 程序启动后系统变慢
**A:**
1. **分时段索引**：避免在系统繁忙时进行大量索引
2. **关闭不必要功能**：暂时关闭内容索引、AI功能等

### Q24: 硬盘占用空间过高
**A:**
1. **重新创建索引**：在设置中重新创建索引
2. **调整索引设置**：减少索引的文件类型和范围

---

## 🔧 其他常见问题

### Q25: 程序界面显示异常
**A:**
1. **检查显示设置**：
   - 确认系统显示缩放设置正常
   - 尝试调整显示分辨率
2. **更新显卡驱动**：确保显卡驱动为最新版本

### Q26: 程序无法正常退出
**A:**
1. **使用任务管理器**：强制结束进程
2. **检查后台服务**：确认相关服务已停止
3. **重启计算机**：彻底清理残留进程

### Q27: 设置无法保存
**A:**
1. **检查文件权限**：确保对配置文件`./core/user/settings.json`以及`./config.json`有写入权限
2. **以管理员运行**：使用管理员权限运行程序
3. **检查磁盘空间**：确保有足够的存储空间

### Q28: 程序频繁崩溃
**A:**
1. **查看崩溃日志**：检查程序目录下的错误日志
2. **更新程序版本**：升级到最新版本
3. **检查系统环境**：
   - 运行系统文件检查器：`sfc /scannow`
   - 检查内存是否有问题
4. **重新安装**：完全卸载后重新安装

### Q29: 无法连接到插件市场
**A:**
1. **检查网络连接**：确保网络连接正常
2. **检查防火墙**：将程序添加到防火墙白名单
3. **检查代理设置**：如果使用代理，确认代理配置正确
4. **更换DNS**：尝试使用不同的DNS服务器

---

## 📞 获取更多帮助

如果以上解决方案无法解决您的问题，请通过以下方式联系我们：

### 官方支持渠道
- **GitHub Issues**: [问题反馈](https://github.com/panwangwin/aiverything-official-forum/issues)
- **QQ交流群**: 893463594
- **官方网站**: [https://aiverything.me/](https://aiverything.me/)

### 反馈信息准备
为了更好地帮助您解决问题，请准备以下信息：
1. **系统信息**：Windows版本、硬件配置
2. **程序版本**：Aiverything版本号
3. **问题描述**：详细的问题现象和重现步骤
4. **错误日志**：相关的错误日志文件
5. **屏幕截图**：问题界面的屏幕截图

