import { createSlice } from "@reduxjs/toolkit";
import { RootState } from "../store";

const rightPanelSlice = createSlice({
  name: "rightPanel",
  initialState: {
    isExpanded: false,
  },
  reducers: {
    toggleRightPanel: (state) => {
      state.isExpanded = !state.isExpanded;
    },
    setRightPanelExpanded: (state, action) => {
      state.isExpanded = action.payload;
    },
  },
});

export const { toggleRightPanel, setRightPanelExpanded } = rightPanelSlice.actions;
export const selectRightPanelExpanded = (state: RootState) => state.rightPanel.isExpanded;
export default rightPanelSlice.reducer; 