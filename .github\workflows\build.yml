name: build-aiverything-release

on:
  push:
    branches:
      - master
      - ci/createGithubRelease
  workflow_dispatch:

jobs:
  build:
    runs-on: windows-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up JDK 21
        uses: actions/setup-java@v3
        with:
          java-version: '21'
          distribution: 'temurin'

      - name: Set up Maven
        uses: stCarolas/setup-maven@v4.5
        with:
          maven-version: '3.9.5'

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable

      - name: Install NASM
        run: |
          choco install nasm -y
          # Set the AWS_LC_SYS_PREBUILT_NASM environment variable
          echo "AWS_LC_SYS_PREBUILT_NASM=true" | Out-File -FilePath $env:GITHUB_ENV -Append

      - name: Create src/core directory if it doesn't exist
        run: mkdir -p src-tauri/core
        shell: bash
      
      - name: Build Aiverything Core
        run: |
          git clone https://${{ secrets.PAT }}@github.com/XUANXUQAQ/AIVERYTHING-Core.git
          cd AIVERYTHING-Core
          python build.py --jdk-home "$env:JAVA_HOME" --additional-module jdk.crypto.ec jdk.unsupported java.base java.compiler java.datatransfer java.desktop java.instrument java.logging java.management java.management.rmi java.naming java.net.http java.prefs java.rmi java.scripting java.se java.security.jgss java.security.sasl java.smartcardio java.sql java.sql.rowset java.transaction.xa java.xml java.xml.crypto jdk.charsets

      - name: Build Plugin Service
        run: |
          git clone https://${{ secrets.PAT }}@github.com/XUANXUQAQ/win-platform.git
          cd win-platform/file-manager/
          mvn clean compile package -DskipTests

      - name: Copy Java artifacts to src/core
        run: |
          # Copy specific outputs from Java repos to src/core
          # Modify paths to match your specific artifacts
          cp -r AIVERYTHING-Core/build/* ./src-tauri/core/
          # Filter jar file contains proguard keyword
          Get-ChildItem -Path "win-platform/file-manager/plugin-service/target/" -Filter "*.jar" | Where-Object { $_ -notmatch "proguard" } | Sort-Object -Property Name | Select-Object -First 1 | Copy-Item -Destination "./src-tauri/core/Plugin-Service.jar"

      - name: Install dependencies
        run: |  
          yarn cache clean
          yarn config delete proxy
          yarn config delete https-proxy
          yarn config delete registry
          yarn --network-timeout 1000000

      - name: Setup Environment Variables
        run: |
          # Create .env.build file from GitHub Secrets
          @"
          VITE_SUPABASE_URL=${{ secrets.VITE_SUPABASE_URL }}
          VITE_SUPABASE_ANON_KEY=${{ secrets.VITE_SUPABASE_ANON_KEY }}
          # Add all your required environment variables
          "@ | Out-File -FilePath .env.build -Encoding utf8
          
          # Verify file was build (optional)
          if (Test-Path .env.build) {
            echo ".env.build file created successfully"
          } else {
            echo "Error creating .env.build file"
            exit 1
          }   

      - name: Build current repository
        run: |
          $env:TAURI_SIGNING_PRIVATE_KEY="dW50cnVzdGVkIGNvbW1lbnQ6IHJzaWduIGVuY3J5cHRlZCBzZWNyZXQga2V5ClJXUlRZMEl5emZ1c3ZoVjVFNVd1aTNjMXZnYlY0RXNxN0tHL0dZc1U3UGY3aWVaVHBoa0FBQkFBQUFBQUFBQUFBQUlBQUFBQS9DaGpSa1BUOWhDMXJYbFpydy9HSFhYQk5rVjV5WUpVNmdNRlRwYmdxcmhOencySDhvR3A0bWlXdHgxcVRLWDVET0EwVGMzVEpGam12SUpNRWdwdjlsVU44RlFoTWthZ3NUbGcrNm1wSE1zWWFGSm1lT0NXVlJoZnhqS25HTUJBM1ZsK2t6dnhJTWM9Cg=="
          $env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD="aiverything"
          yarn tauri build

      - name: Create Release
        id: create_release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v${{ github.run_number }}
          name: Release ${{ github.run_number }}
          draft: false
          prerelease: false
          files: |
            src-tauri/target/release/bundle/nsis/*.exe
            src-tauri/target/release/bundle/nsis/*.exe.sig
        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}