LangString addOrReinstall ${LANG_JAPANESE} "コンポーネントの追加・再インストール"
LangString alreadyInstalled ${LANG_JAPANESE} "既にインストールされています"
LangString alreadyInstalledLong ${LANG_JAPANESE} "${PRODUCTNAME} ${VERSION} は既にインストールされています。実行したい操作を選択し、「次へ」をクリックして続行します。"
LangString appRunning ${LANG_JAPANESE} "${PRODUCTNAME} は動作中です。動作中のプログラムを終了し、もう一度やり直してください。"
LangString appRunningOkKill ${LANG_JAPANESE} "${PRODUCTNAME} は動作中です。$\n「OK」を押すと動作中のプログラムを終了します。"
LangString chooseMaintenanceOption ${LANG_JAPANESE} "メンテナンスオプションを選択して実行します。"
LangString choowHowToInstall ${LANG_JAPANESE} "${PRODUCTNAME} のインストール方法を選択してください。"
LangString createDesktop ${LANG_JAPANESE} "デスクトップショートカットを作成する"
LangString dontUninstall ${LANG_JAPANESE} "アンインストールしない"
LangString dontUninstallDowngrade ${LANG_JAPANESE} "アンインストールしない (このインストーラーでは、アンインストールをせずにダウングレードすることはできません)"
LangString failedToKillApp ${LANG_JAPANESE} "${PRODUCTNAME} の終了に失敗しました。動作中のプログラムを終了し、もう一度やり直してください。"
LangString installingWebview2 ${LANG_JAPANESE} "WebView2 をインストール中です..."
LangString newerVersionInstalled ${LANG_JAPANESE} "既に新しいバージョンの ${PRODUCTNAME} がインストールされています。古いバージョンをインストールすることは推奨されません。どうしてもこの旧バージョンをインストールしたい場合は、先に現行バージョンをアンインストールしておく方がよいでしょう。実行したい操作を選択し、「次へ」をクリックして続行します。"
LangString older ${LANG_JAPANESE} "旧"
LangString olderOrUnknownVersionInstalled ${LANG_JAPANESE} "お使いのシステムには、 ${PRODUCTNAME} のバージョン $R4 がインストールされています。インストールする前に、現在のバージョンをアンインストールすることをお勧めします。実行したい操作を選択し、「次へ」をクリックして続行します。"
LangString silentDowngrades ${LANG_JAPANESE} "このインストーラーではダウングレードはできません。サイレントインストーラーを続行できないので、代わりにグラフィカルインターフェースインストーラーを使用してください。$\n"
LangString unableToUninstall ${LANG_JAPANESE} "アンインストールできません。"
LangString uninstallApp ${LANG_JAPANESE} "${PRODUCTNAME} をアンインストールする"
LangString uninstallBeforeInstalling ${LANG_JAPANESE} "インストールする前にアンインストールする"
LangString unknown ${LANG_JAPANESE} "不明"
LangString webview2AbortError ${LANG_JAPANESE} "WebView2 のインストールに失敗しました。 WebView2 がないとアプリは実行できません。インストーラーを再起動してください。"
LangString webview2DownloadError ${LANG_JAPANESE} "エラー: WebView2 のダウンロードに失敗しました - $0"
LangString webview2DownloadSuccess ${LANG_JAPANESE} "WebView2 ブートストラップ が正常にダウンロードされました"
LangString webview2Downloading ${LANG_JAPANESE} "WebView2 ブートストラップ をダウンロード中です..."
LangString webview2InstallError ${LANG_JAPANESE} "エラー: WebView2 のインストールは終了コード $1 で失敗しました。"
LangString webview2InstallSuccess ${LANG_JAPANESE} "WebView2 が正常にインストールされました"
LangString deleteAppData ${LANG_JAPANESE} "アプリケーションデータを削除する"
