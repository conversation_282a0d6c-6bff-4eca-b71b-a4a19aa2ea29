use crate::api::hotkey_listener;

#[tauri::command]
pub fn is_ctrl_key_double_clicked() -> bool {
    match hotkey_listener::is_ctrl_key_double_clicked() {
        Ok(ret) => ret > 0,
        Err(e) => {
            eprintln!("Failed to check ctrl status, Error: {}", e.to_string());
            return false;
        }
    }
}

#[tauri::command]
pub fn is_shift_key_double_clicked() -> bool {
    match hotkey_listener::is_shift_key_double_clicked() {
        Ok(ret) => ret > 0,
        Err(e) => {
            eprintln!("Failed to check shift status, Error: {}", e.to_string());
            return false;
        }
    }
}

#[tauri::command]
pub fn get_async_key_state(key: i32) -> i16 {
    match hotkey_listener::get_async_key_state(key) {
        Ok(ret) => ret,
        Err(e) => {
            eprintln!("Failed to get async key state, Error: {}", e.to_string());
            return 0;
        }
    }
}

#[tauri::command]
pub fn get_swap_button_state() -> i32 {
    match hotkey_listener::get_swap_button_state() {
        Ok(ret) => ret,
        Err(e) => {
            eprintln!("Failed to get swap button state, Error: {}", e.to_string());
            return 0;
        }
    }
}
