import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// 序列化后的Data类型（用于Redux状态）
export interface SerializedData {
  key: string;
  name: string;
  icon: string;
  type: string;
  extension: string;
  size: { number: number };
  path: string;
  lastModified?: string; // 序列化为ISO字符串
  createdAt?: string; // 序列化为ISO字符串
  metaData: any;
  appUserModelId?: string;
  highlightPath?: string;
  highlightFileName?: string;
}

// 序列化后的QueryResponse类型（用于Redux状态）
export interface SerializedQueryResponse {
  datatype: string;
  data: SerializedData[];
}

export interface QueryResponse {
  datatype: string;
  data: any[];
}

interface SearchResultState {
  queryData: SerializedQueryResponse[];
  availableDataTypes: string[];
  totalResultCount: number;
  selectedCategory: string;
  flatMode: boolean;
  sortBy: string;
  sortOrder: "asc" | "desc";
}

const initialState: SearchResultState = {
  queryData: [],
  availableDataTypes: [],
  totalResultCount: 0,
  selectedCategory: "all",
  flatMode: false,
  sortBy: "relevance",
  sortOrder: "asc",
};

const searchResultSlice = createSlice({
  name: "searchResult",
  initialState,
  reducers: {
    setQueryData: (state, action: PayloadAction<SerializedQueryResponse[]>) => {
      state.queryData = action.payload;
      state.availableDataTypes = action.payload.map((item) => item.datatype);
      state.totalResultCount = action.payload.reduce(
        (total, item) => total + item.data.length,
        0
      );
    },
    clearSearchResults: (state) => {
      state.queryData = [];
      state.availableDataTypes = [];
      state.totalResultCount = 0;
      state.selectedCategory = "all";
      state.flatMode = false;
      state.sortBy = "relevance";
      state.sortOrder = "asc";
    },
    setSelectedCategory: (state, action: PayloadAction<string>) => {
      state.selectedCategory = action.payload;
    },
    setFlatMode: (state, action: PayloadAction<boolean>) => {
      state.flatMode = action.payload;
    },
    setSortBy: (state, action: PayloadAction<string>) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action: PayloadAction<"asc" | "desc">) => {
      state.sortOrder = action.payload;
    },
    setSortSettings: (
      state,
      action: PayloadAction<{ sortBy: string; sortOrder: "asc" | "desc" }>
    ) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
  },
});

export const {
  setQueryData,
  clearSearchResults,
  setSelectedCategory,
  setFlatMode,
  setSortBy,
  setSortOrder,
  setSortSettings,
} = searchResultSlice.actions;

export const selectQueryData = (state: { searchResult: SearchResultState }) =>
  state.searchResult.queryData;
export const selectAvailableDataTypes = (state: {
  searchResult: SearchResultState;
}) => state.searchResult.availableDataTypes;
export const selectTotalResultCount = (state: {
  searchResult: SearchResultState;
}) => state.searchResult.totalResultCount;
export const selectSelectedCategory = (state: {
  searchResult: SearchResultState;
}) => state.searchResult.selectedCategory;
export const selectFlatMode = (state: { searchResult: SearchResultState }) =>
  state.searchResult.flatMode;
export const selectSortBy = (state: { searchResult: SearchResultState }) =>
  state.searchResult.sortBy;
export const selectSortOrder = (state: { searchResult: SearchResultState }) =>
  state.searchResult.sortOrder;

export default searchResultSlice.reducer;
