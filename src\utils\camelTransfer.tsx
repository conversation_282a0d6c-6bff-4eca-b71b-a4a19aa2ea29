type SnakeToCamelCase<S extends string> = S extends `${infer T}_${infer U}`
  ? `${T}${Capitalize<SnakeToCamelCase<U>>}`
  : S;

type SnakeToCamelObject<T> = {
  [K in keyof T as SnakeToCamelCase<string & K>]: T[K] extends object
    ? SnakeToCamelObject<T[K]>
    : T[K];
};

export const snakeToCamel = (str: string): string => 
  str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());

export const convertToCamelCase = <T extends Record<string, any>>(obj: T): SnakeToCamelObject<T> => {
  const newObj: any = {};
  
  Object.keys(obj).forEach((key) => {
    const camelKey = snakeToCamel(key);
    const value = obj[key];
    
    newObj[camelKey] = value && typeof value === 'object' && !Array.isArray(value)
      ? convertToCamelCase(value)
      : value;
  });
  
  return newObj;
};