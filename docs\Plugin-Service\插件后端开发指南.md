# Aiverything 插件后端开发指南

## 概述

本文档专注于 Aiverything 插件的后端开发，包括插件基类、配置管理、命令注册、生命周期管理等核心功能的实现。

## 目录结构

```
plugin-template/
├── pom.xml                                    # Maven 项目配置文件
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/platform/plugin/template/
│   │   │       └── TestPlugin.java           # 主插件类
│   │   └── resources/
│   │       ├── plugin.json                   # 插件元数据配置
│   │       ├── settings.json                 # 插件设置配置
│   │       └── i18n/                         # 国际化资源
│   │           ├── Plugin_en_US.properties   # 英文语言包
│   │           └── Plugin_zh_CN.properties   # 中文语言包
│   └── test/
│       └── java/                             # 测试代码
└── target/                                   # 编译输出目录
```

## 开发环境要求

- **Java 版本**: JDK 21 或更高版本
- **构建工具**: Maven 3.6+
- **IDE**: 推荐使用 IntelliJ IDEA

## 核心概念

### 1. 插件基类 (Plugin)

所有插件都必须继承 `com.platform.plugin.sdk.Plugin` 基类，并实现以下核心方法：

```java
public class TestPlugin extends Plugin {
    @Override
    public void load(Map<String, Object> config) {
        // 插件加载时调用，config 是 settings.json 的配置值
        System.out.println("插件加载，配置: " + config);
    }

    @Override
    public void afterLoaded() {
        // 所有插件加载完成后调用
        System.out.println("插件加载完成");
    }

    @Override
    public void unload() {
        // 插件卸载时调用，清理资源
        System.out.println("插件卸载");
    }

    @Override
    public boolean pluginEnter() {
        // 插件激活时调用，返回 true 表示将会直接显示插件嵌入界面，否则在输入文字前将会只显示搜索框
        System.out.println("插件激活");
        return true;
    }

    @Override
    public void pluginExit() {
        // 插件退出时调用
        System.out.println("插件退出");
    }

    @Override
    public void configsChanged(Map<String, Object> config) {
        // 配置变更时调用
        System.out.println("配置变更: " + config);
    }

    @Override
    public boolean isHotSwappable() {
        // 是否支持热插拔
        return true;
    }
}
```

### 2. 插件生命周期

#### 加载阶段
1. **load()** - 插件初始化，读取配置
2. **afterLoaded()** - 所有插件加载完成后的后置处理

#### 激活阶段
1. **pluginEnter()** - 插件被用户激活时调用
2. **pluginExit()** - 插件退出时调用

#### 卸载阶段
1. **unload()** - 插件卸载，清理资源

#### 配置变更
1. **configsChanged()** - 配置发生变更时调用

### 3. 命令注册机制

插件可以注册命令处理器，供外部调用：

```java
public class TestPlugin extends Plugin {
    @Override
    public void load(Map<String, Object> config) {
        // 注册命令处理器
        registerCommandHandler("testCommand", paramsMap -> {
            System.out.println("命令被调用，参数: " + paramsMap);
            
            // 处理业务逻辑
            String result = processCommand(paramsMap);
            
            return result;
        });
        
        // 注册多个命令
        registerCommandHandler("getUserInfo", this::getUserInfo);
        registerCommandHandler("saveData", this::saveData);
    }
    
    private String processCommand(Map<String, Object> params) {
        // 实现具体的命令处理逻辑
        return "处理完成";
    }
    
    private Object getUserInfo(Map<String, Object> params) {
        // 获取用户信息
        return Map.of("name", "用户", "id", 123);
    }
    
    private Object saveData(Map<String, Object> params) {
        // 保存数据
        return Map.of("success", true, "message", "保存成功");
    }
}
```

## 配置管理

### 1. 读取配置

```java
// 获取配置值
Optional<Object> config = getConfig("configName");
if (config.isPresent()) {
    System.out.println("配置值: " + config.get());
}

// 获取嵌套配置
Optional<Map<String, Object>> nestedConfig = getConfig("configName4.configName5");

// 获取特定类型的配置
Optional<String> stringConfig = getConfig("stringConfig");
Optional<Integer> intConfig = getConfig("intConfig");
Optional<Boolean> boolConfig = getConfig("boolConfig");
```

### 2. 设置配置

```java
// 设置配置值
ConfigEntity.ConfigComponent newConfig = generateConfigObject(
    "string",
    "新配置项",
    "默认值",
    null,
    null,
    null
);
setConfig("configName", newConfig);

// 设置嵌套配置
setConfig("configName4.configName5", newConfig);

// 设置复杂对象
Map<String, ConfigEntity.ConfigComponent> complexConfig = new HashMap<>();
ConfigEntity.ConfigComponent value1 = generateConfigObject(
    "string",
    "value1 config",
    "value1",
    "value1",
    null,
    null
);
ConfigEntity.ConfigComponent value123 = generateConfigObject(
    "number",
    "value2 config",
    "123",
    "123",
    null,
    null
);
complexConfig.put("key1", value1);
complexConfig.put("key2", value123);
ConfigEntity.ConfigComponent valueObject = generateConfigObject(
    "object",
    "object config",
    complexConfig,
    complexConfig,
    null,
    null
);
setConfig("complexConfig", valueObject);
```

### 3. 添加配置

```java
// 添加新的配置项
ConfigEntity.ConfigComponent newConfig = generateConfigObject(
    "string",
    "新配置项",
    "默认值",
    "当前值",
    null,
    null
);

addConfig("configName4.configName5", "newKey", newConfig);
```

### 4. 删除配置

```java
// 删除配置项
removeConfig("configName4.configName5.key");

// 删除数组中的元素
removeConfig("configName4.configName5.array[0]");
```

## 配置文件详解

### 1. plugin.json - 插件元数据

```json
{
  "name": "Example Plugin",
  "identifier": "example",
  "version": "1.0.0",
  "className": "com.platform.plugin.template.TestPlugin",
  "author": "Aiverything Official",
  "description": "{{description}}",
  "detailedDescription": "{{detailedDescription}}",
  "categories": ["Example"],
  "keywords": ["example"],
  "icon": "react.png",
  "preview": false,
  "dependencies": ["anotherPluginIdentifier:semverVersion"],
  "entryPage": "entry/index.html",
  "embeddedSupport": true,
  "embeddedPage": "embedded/index.html"
}
```

### 2. settings.json - 插件设置配置

```json
{
  "title": "example",
  "config": {
    "configName1": {
      "type": "string",
      "description": "配置描述",
      "defaultValue": "默认值"
    },
    "configName2": {
      "type": "number",
      "description": "数字配置",
      "defaultValue": 1,
      "enumValues": [1, 2, 3, 4, 5],
      "enumDescription": ["desc1", "desc2", "desc3", "desc4", "desc5"]
    },
    "configName3": {
      "type": "boolean",
      "description": "布尔配置",
      "defaultValue": true
    },
    "configName4": {
      "type": "object",
      "description": "对象配置",
      "defaultValue": {
        "nestedKey": "nestedValue"
      }
    },
    "configName5": {
      "type": "array",
      "description": "数组配置",
      "defaultValue": ["item1", "item2"]
    }
  }
}
```

## 高级配置管理

### 1. 配置对象生成

```java
// 创建字符串类型配置
ConfigEntity.ConfigComponent stringConfig = generateConfigObject(
    "string",           // 类型
    "字符串配置",        // 描述
    "默认值",           // 默认值
    "当前值",           // 当前值
    null,              // 枚举值列表
    null               // 枚举描述列表
);

// 创建数字类型配置
ArrayList<Object> enumValues = new ArrayList<>();
enumValues.add(1);
enumValues.add(2);
enumValues.add(3);

ArrayList<String> enumDesc = new ArrayList<>();
enumDesc.add("选项1");
enumDesc.add("选项2");
enumDesc.add("选项3");

ConfigEntity.ConfigComponent numberConfig = generateConfigObject(
    "number",
    "数字配置",
    1,
    2,
    enumValues,
    enumDesc
);

// 创建布尔类型配置
ArrayList<Object> boolEnum = new ArrayList<>();
boolEnum.add(true);
boolEnum.add(false);

ArrayList<String> boolDesc = new ArrayList<>();
boolDesc.add("启用");
boolDesc.add("禁用");

ConfigEntity.ConfigComponent boolConfig = generateConfigObject(
    "boolean",
    "布尔配置",
    true,
    false,
    boolEnum,
    boolDesc
);
```

### 2. 复杂配置结构

```java
// 创建对象类型配置
Map<String, ConfigEntity.ConfigComponent> objectValue = new LinkedHashMap<>();
objectValue.put("stringField", stringConfig);
objectValue.put("numberField", numberConfig);
objectValue.put("boolField", boolConfig);

ConfigEntity.ConfigComponent objectConfig = generateConfigObject(
    "object",
    "对象配置",
    objectValue,
    null,
    null,
    null
);

// 创建数组类型配置
ArrayList<ConfigEntity.ConfigComponent> arrayValue = new ArrayList<>();
arrayValue.add(stringConfig);
arrayValue.add(numberConfig);

ConfigEntity.ConfigComponent arrayConfig = generateConfigObject(
    "array",
    "数组配置",
    arrayValue,
    null,
    null,
    null
);
```

## 国际化支持

### 1. 语言包配置

**Plugin_zh_CN.properties** (中文)
```properties
description=这是插件的介绍
detailedDescription=这是一个更详细的插件介绍
configName1.description=这是一个配置介绍
error.loading=插件加载失败
success.operation=操作成功
```

**Plugin_en_US.properties** (英文)
```properties
description=This is the plugin description
detailedDescription=This is a more detailed plugin description
configName1.description=This is a config description
error.loading=Plugin loading failed
success.operation=Operation successful
```

### 2. 国际化资源使用

**在settings.json和plugin.json中使用`{{value}}`引用起来，在解析的时候将会自动根据系统语言在语言包中寻找对应`value`的翻译，然后进行替换。**

## 错误处理与日志

### 1. 错误处理

```java
@Override
public void load(Map<String, Object> config) {
    try {
        // 插件初始化逻辑
        initializePlugin(config);
    } catch (ConfigurationException e) {
        log.error("配置错误: " + e.getMessage(), e);
        throw new RuntimeException("插件配置无效", e);
    } catch (Exception e) {
        log.error("插件加载失败: " + e.getMessage(), e);
        throw new RuntimeException("插件加载失败", e);
    }
}

@Override
public boolean pluginEnter() {
    try {
        // 插件激活逻辑
        return activatePlugin();
    } catch (Exception e) {
        log.error("插件激活失败: " + e.getMessage(), e);
        return false;
    }
}
```

### 2. 日志记录

**插件SDK已经添加Slf4j框架以及lombok，可以使用`@Slf4j`注解来使用log进行日志打印**

```java
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TestPlugin extends Plugin {
    
    @Override
    public void load(Map<String, Object> config) {
        log.info("[{}] 插件开始加载，配置: {}", getPluginName(), config);
        
        try {
            // 业务逻辑
            log.debug("[{}] 执行业务逻辑", getPluginName());
            
        } catch (Exception e) {
            log.error("[{}] 插件加载失败: {}", getPluginName(), e.getMessage(), e);
            throw e;
        }
        
        log.info("[{}] 插件加载完成", getPluginName());
    }
    
    // 结构化日志
    public void logOperation(String operation, Map<String, Object> params, Object result) {
        log.info("[{}] 操作: {}, 参数: {}, 结果: {}", 
                getPluginName(), operation, params, result);
    }
    
    // 性能日志
    public void logPerformance(String operation, long startTime) {
        long duration = System.currentTimeMillis() - startTime;
        log.info("[{}] 操作 {} 耗时: {}ms", getPluginName(), operation, duration);
    }
}
```

## 测试与调试

### 远程调试配置

#### 启用调试模式
1. 在Aiverything中启用调试模式
2. 配置JDK路径和Java Agent路径
3. 重启Aiverything，插件服务将在35005端口开启远程调试

#### IDE调试配置
```xml
<!-- IntelliJ IDEA Remote Debug Configuration -->
<configuration>
    <option name="TRANSPORT" value="0" />
    <option name="HOST" value="localhost" />
    <option name="PORT" value="35005" />
    <option name="SUSPEND" value="n" />
</configuration>
```

## 部署与发布

### 1. 构建插件

```bash
# 编译插件
mvn clean compile

# 运行测试
mvn test

# 打包插件
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests
```

### 2. 插件部署

```java
// 插件部署配置
public class DeploymentConfig {
    
    // 插件目录
    public static final String PLUGIN_DIR = "core/plugins";
    
    // 部署插件
    public static void deployPlugin(String pluginPath) {
        try {
            Path source = Paths.get(pluginPath);
            Path target = Paths.get(PLUGIN_DIR).resolve(source.getFileName());
            
            Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING);
            System.out.println("插件部署成功: " + target);
            
        } catch (IOException e) {
            System.err.println("插件部署失败: " + e.getMessage());
        }
    }
}
```
## 常见问题

### 1. 插件加载失败

**原因及解决方案：**
- 检查 Java 版本是否为 21+
- 确认 `plugin.json` 中的 `className` 是否正确
- 检查依赖是否正确配置
- 验证插件JAR文件完整性

### 2. 配置无法保存

**原因及解决方案：**
- 确认配置对象使用 `generateConfigObject` 方法生成
- 检查配置路径是否正确
- 验证配置类型是否匹配
- 确认配置文件写入权限

### 3. 命令处理器不响应

**原因及解决方案：**
- 确认命令已正确注册
- 检查命令名称是否匹配
- 验证参数格式是否正确
- 查看日志获取详细错误信息

### 4. 远程调试连接失败

**原因及解决方案：**
- 确认35005端口没有被占用
- 检查JDK路径配置是否正确
- 验证Java Agent路径设置
- 确认防火墙允许端口通信

## 技术支持

如需技术支持，请联系开发团队或查看项目的 GitHub 仓库。

---

*本文档专注于插件后端开发，关于前端页面开发请参考相应的前端开发指南。* 