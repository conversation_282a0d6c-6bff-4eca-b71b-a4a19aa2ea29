import React, { useState, useEffect, useCallback } from "react";
import "../i18n/i18n";
import { MainInput } from "../Component/MainInput";
import { MainAnswer } from "../Component/MainAnwser";
import { status as dbStatusApi } from "../api/aiverything";
import {
  isPermissionGranted,
  requestPermission,
  sendNotification,
} from "@tauri-apps/plugin-notification";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import { selectWindowVibrancy } from "../slices/windowVibrancySlice";

const MainWindow: React.FC = () => {
  const [coreStatus, setCoreStatus] = useState<string>("");
  const vibrancyState = useSelector(selectWindowVibrancy);

  const { t } = useTranslation();

  const sendSystemNotification = async (title, body) => {
    let permissionGranted = await isPermissionGranted();

    if (!permissionGranted) {
      const permission = await requestPermission();
      permissionGranted = permission === "granted";
    }
    if (permissionGranted) {
      sendNotification({ title, body });
    }
  };

  const checkCoreStatus = useCallback(() => {
    dbStatusApi()
      .then((res) => {
        if (res.data !== coreStatus) {
          if (res.data === "NORMAL") {
            if (coreStatus === "MANUAL_UPDATE" || coreStatus === "_TEMP") {
              sendSystemNotification(
                "Aiverything",
                t("core.updateFileIndexDone")
              );
            } else if (coreStatus === "VACUUM") {
              sendSystemNotification(
                "Aiverything",
                t("core.optimizeDatabaseDone")
              );
            }
          } else if (res.data === "MANUAL_UPDATE" || res.data === "_TEMP") {
            if (coreStatus === "NORMAL") {
              sendSystemNotification("Aiverything", t("core.updateFileIndex"));
            }
          } else if (res.data === "VACUUM") {
            sendSystemNotification("Aiverything", t("core.optimizeDatabase"));
          }
        }
        setCoreStatus(res.data);
      })
      .catch((err) => {
        console.error(err);
      });
  }, [coreStatus, t]);

  useEffect(() => {
    checkCoreStatus();

    const timer = setInterval(checkCoreStatus, 1000);

    return () => clearInterval(timer);
  }, [checkCoreStatus]);

  return (
    <div
      className={`w-full h-full ${
        vibrancyState ? "bg-transparent" : "bg-main-query"
      }`}
    >
      <MainInput />
      <MainAnswer />
    </div>
  );
};

export default MainWindow;
