// store.ts
import { configureStore } from "@reduxjs/toolkit";
import inputValueReducer from "./slices/inputValueSlice";
import pluginInfoReducer from "./slices/pluginInfoSlice";
import aiModeReducer from "./slices/aiModeSlice";
import windowVibrancyReducer from "./slices/windowVibrancySlice";
import rightPanelReducer from "./slices/rightPanelSlice";
import searchResultReducer from "./slices/searchResultSlice";

const store = configureStore({
  reducer: {
    inputValue: inputValueReducer,
    pluginInfo: pluginInfoReducer,
    aiMode: aiModeReducer,
    windowVibrancy: windowVibrancyReducer,
    rightPanel: rightPanelReducer,
    searchResult: searchResultReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;

export default store;
