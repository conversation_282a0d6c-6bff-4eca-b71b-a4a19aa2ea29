# Aiverything 基础使用指南

## 📋 目录
- [启动搜索](#启动搜索)
- [基本搜索操作](#基本搜索操作)
- [搜索结果操作](#搜索结果操作)
- [快速访问功能](#快速访问功能)
- [高级搜索技巧](#高级搜索技巧)
- [AI智能搜索](#ai智能搜索)
- [插件系统基础](#插件系统基础)
- [快捷键大全](#快捷键大全)
- [实用技巧](#实用技巧)

---

## 🚀 启动搜索

### 启动方式概览

Aiverything 提供多种便捷的启动方式，您可以根据使用习惯选择最适合的方式：

| 启动方式 | 操作方法 | 适用场景 | 特点 |
|----------|----------|----------|------|
| 全局快捷键 | `Ctrl + Shift + Alt + A` | 任何时候 | 最快速 |
| 双击Ctrl | 快速连按两次Ctrl | 单手操作 | 需要设置开启 |
| 系统托盘 | 左键点击托盘图标 | 鼠标操作 | 直观简单 |

### 方式一：全局快捷键（推荐）

#### 默认快捷键
- **组合键**: `Ctrl + Shift + Alt + A`
- **功能**: 打开/隐藏搜索窗口
- **特点**: 随时随地快速呼出

#### 使用方法
1. 同时按下四个键：`Ctrl + Shift + Alt + A`
2. 搜索窗口会立即弹出并获得焦点
3. 光标自动定位到搜索框，可直接输入
4. 再次按下组合键可隐藏搜索窗口

#### 快捷键自定义
1. 右键点击系统托盘中的 Aiverything 图标
2. 选择"设置"
3. 进入"快捷键设置"页面
4. 点击"全局快捷键"输入框
5. 按下新的快捷键组合
6. 点击"应用"保存设置

### 方式二：双击Ctrl键

#### 功能说明
- **操作**: 快速连续按两次 Ctrl 键
- **默认状态**: 禁用（需要手动开启）
- **时间间隔**: 两次按键间隔需在500毫秒内

#### 开启方法
1. 打开 Aiverything 设置界面
2. 进入"快捷键设置"
3. 勾选"双击Ctrl"选项
4. 点击"应用"保存

![double ctrl](./pictures/shorcut%20key%20settings.png)

#### 使用技巧
- 确保两次按键间隔不超过500毫秒
- 避免与其他程序的双击Ctrl功能冲突
- 某些键盘可能不支持此功能

### 方式三：系统托盘

#### 托盘图标位置
- 位于系统托盘区域（通常在屏幕右下角）
- 图标为 Aiverything 的标志性图标
- 如果看不到，可能被隐藏在托盘展开区域

#### 操作方式
- **左键单击**: 直接打开搜索窗口
- **右键单击**: 显示上下文菜单

#### 右键菜单选项
- **搜索**: 打开搜索窗口
- **设置**: 打开设置界面
- **插件设置**: 打开插件配置
- **关于**: 查看程序版本信息
- **退出**: 完全关闭程序

---

## 🔍 基本搜索操作

### 搜索界面概览

#### 界面组成
- **搜索框**: 输入搜索关键词的主要区域
- **搜索结果列表**: 显示匹配的文件和文件夹
- **文件详情面板**: 显示选中文件的详细信息
- **状态栏**: 显示搜索状态和结果统计

#### 界面特点
- **实时搜索**: 输入即搜索，无需按回车
- **智能匹配**: 支持模糊匹配和智能排序
- **现代设计**: 简洁美观的界面设计
- **响应式布局**: 支持窗口大小调整

### 基本搜索方法

#### 简单搜索
```
直接输入文件名或关键词：
document
report
image
```

#### 文件扩展名搜索
```
搜索特定类型的文件：
.pdf        # 所有PDF文件
.jpg        # 所有JPG图片
.docx       # 所有Word文档
.txt        # 所有文本文件
```

#### 路径搜索
```
搜索特定路径下的文件（关键字以`/`或`\`开头）：
/Desktop
/Downloads/project
/C:\Users\<USER>\d+\.txt|p` | 使用正则表达式匹配 |
| `c` | 内容搜索 | `function|c` | 搜索文件内容 |

#### 组合过滤器
```
# 搜索包含"test"的文本文件
test;.txt|f

# 区分大小写搜索包含"Class"的Java文件
Class;.java|case

# 使用正则表达式搜索以数字开头的PDF文件
\d.*\.pdf|p

# 搜索内容包含"password"的配置文件
password;.config|f;c

# 全匹配搜索名为"data"的文件夹
data|full;d
```

### 缓存搜索

#### 功能说明
缓存搜索是搜索最近访问或使用频率高的文件的快速方式。

#### 使用方法
```
# 输入冒号开头进行缓存搜索
:report        # 从缓存中搜索包含"report"的文件
:project       # 搜索最近使用的项目文件
:image         # 搜索最近访问的图片文件
```

#### 缓存特点
- **快速响应**: 缓存搜索响应速度更快
- **智能排序**: 根据使用频率和时间排序
- **自动更新**: 缓存会根据使用情况自动更新

### 网络搜索

#### 触发条件
当本地搜索没有找到相关文件时，会显示网络搜索选项。

#### 支持的搜索引擎
- **Google**: 全球最大的搜索引擎
- **Bing**: 微软搜索引擎
- **百度**: 中文搜索引擎
- **DuckDuckGo**: 注重隐私的搜索引擎

#### 设置默认搜索引擎
1. 打开 Aiverything 设置
2. 进入"常规设置"
3. 在"搜索引擎"下拉菜单中选择默认引擎
4. 保存设置

---

## 🤖 AI智能搜索

### 启用AI搜索

#### 开启方法
1. 点击搜索框右侧的AI图标
2. 或在设置中启用AI搜索功能
3. 确保网络连接正常

#### AI图标状态
- **灰色**: AI功能未启用
- **蓝色**: AI功能已启用

### 自然语言搜索

#### 基本使用
```
# 自然语言描述需求
"帮我找一下上周的工作报告"
"查找包含销售数据的Excel文件"
"最近修改的图片文件"
"桌面上的PDF文档"
```

### AI搜索特性

#### 语义理解
- **意图识别**: 理解用户的真实搜索意图
- **上下文分析**: 结合历史搜索记录分析
- **智能推荐**: 基于内容相关性推荐文件

#### 智能文件分析
- **内容摘要**: 自动生成文档内容摘要
- **关键词提取**: 识别文档核心关键词

### AI文件摘要

#### 支持的文件格式
- **PDF文档**: 研究报告、说明书、电子书等
- **Word文档**: 各种文档、报告、信件等
- **Excel文件**: 数据表格、财务报表等
- **PowerPoint**: 演示文稿、培训材料等
- **文本文件**: 代码文件、配置文件等

#### 使用方法
1. 在搜索结果中选择文件
2. 在文件详情面板中查看AI摘要
3. 如果没有摘要，点击"生成摘要"按钮
4. 等待AI分析完成

#### 摘要内容
- **文档主题**: 文档的主要内容主题
- **核心观点**: 文档中的关键信息
- **关键数据**: 重要的数字和统计信息
- **结论总结**: 文档的主要结论

---

## 🔌 插件系统基础

### 进入插件模式

#### 激活方法
在搜索框中输入 `>` 符号，即可进入插件模式。

#### 界面变化
- 搜索框显示插件搜索提示
- 结果列表显示可用插件
- 左侧显示插件分类

### 插件浏览

#### 插件分类
- **系统工具**: 系统管理和配置插件
- **文件处理**: 文件操作和管理插件
- **开发工具**: 编程和开发相关插件
- **娱乐应用**: 娱乐和多媒体插件
- **效率工具**: 提高工作效率的插件

#### 插件搜索
```
# 搜索特定插件
>calculator     # 搜索计算器插件
>translate      # 搜索翻译插件
```

### 插件操作

#### 安装插件
1. 访问 Aiverything 官网（官方插件市场）
2. 浏览或搜索需要的插件
3. 下载插件文件到本地
4. 在 Aiverything 中打开插件设置
5. 选择"安装插件"并选择下载的插件文件
6. 等待安装完成

#### 使用插件
- **嵌入式插件**: 直接在搜索界面中显示和使用
- **独立窗口插件**: 打开单独的窗口运行

#### 管理插件
1. 进入插件设置界面
2. 查看已安装的插件列表
3. 可以启用、禁用或卸载插件
4. 查看插件详细信息和设置

---

## ⌨️ 快捷键大全

### 全局快捷键

#### 主要全局快捷键
| 快捷键 | 功能 | 默认设置 | 可自定义 |
|--------|------|----------|----------|
| `Ctrl + Shift + Alt + A` | 显示/隐藏搜索窗口 | ✅ | ✅ |
| `双击 Ctrl` | 快速打开搜索窗口 | ❌ | ✅ |
| `双击 Shift` | 附加模式搜索 | ❌ | ✅ |

#### 自定义全局快捷键
1. 右键系统托盘图标选择"设置"
2. 进入"快捷键设置"页面
3. 点击要修改的快捷键输入框
4. 按下新的快捷键组合
5. 点击"应用"保存

### 搜索界面快捷键

#### 基本导航
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `↑` | 选择上一个结果 | 向上移动选择 |
| `↓` | 选择下一个结果 | 向下移动选择 |
| `Page Up` | 向上翻页 | 快速浏览 |
| `Page Down` | 向下翻页 | 快速浏览 |
| `Home` | 选择第一个结果 | 跳转到顶部 |
| `End` | 选择最后一个结果 | 跳转到底部 |

#### 搜索操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl + A` | 全选搜索框文本 | 快速清除搜索内容 |

### 文件操作快捷键

#### 打开操作
| 快捷键 | 功能 | 适用场景 |
|--------|------|----------|
| `Enter` | 打开文件 | 使用默认程序 |
| `Shift + Enter` | 管理员权限打开 | 需要特殊权限的程序 |
| `Ctrl + Enter` | 打开文件位置 | 在资源管理器中定位 |
| `Alt + Enter` | 复制文件路径 | 获取完整路径 |

---

### 性能优化技巧

#### 搜索性能
- **使用具体关键词**: 避免过于宽泛的搜索词，使用多个关键词搜索
- **善用过滤器**: 使用文件类型过滤器缩小范围

#### 系统性能
- **合理设置索引范围**: 只索引必要的磁盘和文件夹
- **调整缓存大小**: 根据内存情况调整缓存设置
- **启用GPU加速**: 有独立显卡时启用GPU加速

### 个性化设置技巧

#### 功能定制
- **自定义快捷键**: 设置符合个人习惯的快捷键
- **配置搜索引擎**: 选择常用的网络搜索引擎
- **设置文件类型**: 添加自定义文件类型映射

### 故障排除技巧

#### 搜索问题
```
# 搜索不到文件时的检查步骤：
1. 确认文件确实存在
2. 检查文件是否在索引范围内
3. 尝试更新索引
4. 检查文件权限
5. 使用完整文件名搜索
```

#### 性能问题
```
# 搜索速度慢时的优化方法：
1. 检查索引是否完成
2. 启用GPU加速
3. 增加系统内存
4. 优化索引设置
5. 关闭其他占用资源的程序
```

---

## 📞 获取更多帮助

### 内置帮助
- **提示系统**: 界面中的操作提示
- **状态信息**: 底部状态栏的操作提示

### 在线资源
- **官方文档**: 完整的使用说明和教程
- **视频教程**: 操作演示视频
- **常见问题**: FAQ答疑解惑

### 社区支持
- **QQ交流群**: 893463594
- **GitHub**: 问题反馈和功能建议
- **官方论坛**: 用户经验分享
