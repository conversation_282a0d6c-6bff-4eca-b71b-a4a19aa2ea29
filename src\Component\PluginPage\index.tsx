import { useEffect, useRef, useState } from "react";
import { setInputValue } from "../../slices/inputValueSlice";
import { useDispatch } from "react-redux";
import { WebviewWindow } from "@tauri-apps/api/webviewWindow";

interface PluginEventType {
  type: string;
  value: string;
}

const PluginPage = ({ url, inputValue }) => {
  const iframeRef = useRef(null);
  const dispatch = useDispatch();
  const [isIframeLoaded, setIsIframeLoaded] = useState(false);

  const sendInputChangedMessage = () => {
    if (iframeRef.current) {
      const inputEvent: PluginEventType = {
        type: "inputChanged",
        value: inputValue,
      };
      iframeRef.current.contentWindow.postMessage(inputEvent, "*");
    }
  };

  useEffect(() => {
    // 接收从插件页面来的message事件
    const handleInputChangeMessage = (event: MessageEvent) => {
      if (event.data.type === "changeInput") {
        dispatch(setInputValue(event.data.value));
      } else if (event.data.type === "hideWindow") {
        const windowLabel = event.data.value;
        WebviewWindow.getByLabel(windowLabel)
          .then((window) => {
            window.hide();
          })
          .catch((err) => {
            console.error(err);
          });
      }
    };
    window.addEventListener("message", handleInputChangeMessage);

    return () => {
      window.removeEventListener("message", handleInputChangeMessage);
    };
  }, []);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Alt") {
        event.preventDefault();
      }
      const downEvent: PluginEventType = {
        type: "keydown",
        value: event.key,
      };
      if (isIframeLoaded) {
        iframeRef.current?.contentWindow.postMessage(downEvent, "*");
      }
    };
    window.addEventListener("keydown", handleKeyDown);

    // 清理函数
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [isIframeLoaded]);

  useEffect(() => {
    const handleKeyUp = (event: KeyboardEvent) => {
      const upEvent: PluginEventType = {
        type: "keyup",
        value: event.key,
      };
      if (isIframeLoaded) {
        iframeRef.current?.contentWindow.postMessage(upEvent, "*");
      }
    };
    window.addEventListener("keyup", handleKeyUp);

    return () => {
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, [isIframeLoaded]);

  useEffect(() => {
    if (isIframeLoaded) {
      sendInputChangedMessage();
    }
  }, [inputValue, isIframeLoaded]);

  const handleIframeLoad = () => {
    setIsIframeLoaded(true);
  };

  return (
    <div
      style={{
        background: "transparent",
      }}
      className="w-full h-answer p-4 bg-main-query transition-opacity overflow-y-auto dark:bg-gray-800"
    >
      <iframe
        style={{
          background: "transparent",
        }}
        className="w-full h-full"
        ref={iframeRef}
        src={url}
        onLoad={handleIframeLoad}
        title="Plugin page"
      />
    </div>
  );
};

export default PluginPage;
