{"$schema": "https://schema.tauri.app/config/2.0.1", "build": {"beforeDevCommand": "yarn dev", "beforeBuildCommand": "yarn build", "frontendDist": "../dist", "devUrl": "http://localhost:1420"}, "bundle": {"active": true, "targets": "nsis", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["core/**/*", "getHandle.dll", "hotkeyListener.dll"], "windows": {"nsis": {"installMode": "perMachine", "template": "./nsis/installer.nsi", "displayLanguageSelector": true, "languages": ["English", "SimpChinese", "Japanese"], "headerImage": "./nsis/resources/images/header.bmp", "installerIcon": "./nsis/resources/images/installer.ico"}}, "createUpdaterArtifacts": true}, "productName": "aiverything", "mainBinaryName": "aiverything", "version": "0.2.4", "identifier": "com.aiverything.app", "plugins": {"deep-link": {"desktop": {"schemes": ["aiverything"]}}, "updater": {"pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IENFQURBNzQ2OUFDNkUxOUIKUldTYjRjYWFScWV0enNWWitYMXI0WUVscFZUdnl3bnRjVmhMZkZ5dW9vV0xtNGdpTDZ2RHlOQ3MK", "endpoints": ["https://api.aiverything.me/storage/v1/object/sign/aiverything-release-json/release.json?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cmwiOiJhaXZlcnl0aGluZy1yZWxlYXNlLWpzb24vcmVsZWFzZS5qc29uIiwiaWF0IjoxNzQzMDY5NjEyLCJleHAiOjIwNTg0Mjk2MTJ9.hVs_W2r4LZ2Xxm5LGtsvdRnbWuGU12PGVq7SsNJXzJQ"]}}, "app": {"security": {"csp": null, "capabilities": ["main-capability"]}, "windows": [{"title": "aiverything", "url": "index.html", "visible": false, "width": 600, "height": 500, "decorations": false, "transparent": true, "shadow": false, "center": true, "resizable": false, "skipTaskbar": true, "alwaysOnTop": true}]}}