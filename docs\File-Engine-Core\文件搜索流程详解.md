# File-Engine-Core 文件搜索流程详解

## 1. 概述

File-Engine-Core 采用了事件驱动架构，搜索功能通过 `PrepareSearchEvent` 和 `StartSearchEvent` 两个核心事件来实现。本文档详细介绍从接收搜索请求到返回搜索结果的完整流程。

## 2. 搜索事件概览

### 2.1 搜索事件类型

- **PrepareSearchEvent**: 预搜索事件，创建搜索任务但不立即执行
- **StartSearchEvent**: 开始搜索事件，立即执行搜索任务
- **StopSearchEvent**: 停止搜索事件，终止正在进行的搜索

### 2.2 事件继承关系

```java
PrepareSearchEvent extends StartSearchEvent extends Event
```

## 3. 搜索流程详解

### 3.1 HTTP API 接口层

#### 3.1.1 同步搜索
```http
POST /search
```
- 直接发送 `StartSearchEvent`
- 等待搜索完成并返回完整结果

#### 3.1.2 异步搜索
```http
POST /searchAsync
```
- 发送 `StartSearchEvent`
- 立即返回搜索任务UUID

#### 3.1.3 预搜索
```http
GET /prepareSearch
```
- 发送 `PrepareSearchEvent`
- 创建搜索任务并返回UUID

### 3.2 搜索关键字处理流程

#### 3.2.1 关键字预处理
```java
private static SearchInfo prepareSearchKeywords(
    Supplier<String> searchTextSupplier,
    Supplier<String[]> searchCaseSupplier,
    Supplier<String[]> keywordsSupplier,
    int maxResultNumber)
```

**处理步骤**：
1. **验证搜索文本长度**：检查是否超过最大长度限制
2. **处理搜索模式**：解析搜索case（文件、目录、内容、正则等）
3. **关键词分词**：
   - 路径关键词：以 `/` 开头的关键词会被识别为路径搜索
   - 内容分词：如果启用内容索引，使用IK分词器对关键词分词
   - 大小写处理：根据配置决定是否忽略大小写

#### 3.2.2 SearchInfo 创建
创建包含以下信息的 `SearchInfo` 对象：
- `searchCase`: 搜索类型数组
- `isIgnoreCase`: 是否忽略大小写
- `searchText`: 原始搜索文本
- `keywords`: 关键词数组
- `keywordsLowerCase`: 小写关键词数组
- `isKeywordPath`: 每个关键词是否为路径搜索
- `maxResultNum`: 最大结果数量

### 3.3 PrepareSearchEvent 处理流程

#### 3.3.1 事件处理器
```java
@EventRegister(registerClass = PrepareSearchEvent.class)
private static void prepareSearchEvent(Event event)
```

**处理步骤**：
1. **输入验证**：
   - 检查搜索文本长度
   - 验证正则表达式语法（如果是正则搜索）

2. **等待数据库状态**：
   - 等待数据库状态为 `NORMAL`
   - 超时时间：3秒

3. **搜索任务管理**：
   - 检查 `prepareTasksMap` 中是否已存在相同的搜索任务
   - 如果存在，更新任务使用时间
   - 如果不存在，创建新的搜索任务

#### 3.3.2 预搜索任务创建
```java
private static SearchTask prepareSearch(SearchInfo searchInfo)
```

**核心步骤**：

1. **准备原生搜索信息**：
   ```java
   long preparedSearchInfo = PathMatcher.INSTANCE.prepareSearchInfo(
       searchInfo.searchCase,
       searchInfo.isIgnoreCase,
       searchInfo.searchText,
       searchInfo.keywords,
       searchInfo.keywordsLowerCase,
       searchInfo.isKeywordPath,
       AllConfigs.getInstance().getConfigEntity().isEnableFuzzyMatch());
   ```

2. **创建搜索任务**：
   ```java
   var searchTask = new SearchTask(searchInfo, preparedSearchInfo);
   ```

3. **准备结果容器**：
   ```java
   databaseService.prepareResultContainer(searchTask);
   ```

#### 3.3.3 数据库搜索任务生成详解

**SQL生成流程**：

1. **生成未格式化SQL结构**：
   ```java
   private ArrayList<List<SearchTableAndPriority>> getNonFormattedSqlFromTableQueue(SearchInfo searchInfo)
   ```

   **生成策略**：
   - **目录搜索**：如果搜索case包含`DIRECTORY`，只搜索priority为-1的目录记录
   - **后缀优化**：检查关键词中是否包含文件后缀，如果有则只搜索对应优先级
   - **全量搜索**：如果没有特定后缀，按所有优先级生成搜索任务

2. **SearchTableAndPriority结构**：
   ```java
   private record SearchTableAndPriority(String table, Integer priority, String otherCondition) {
       @Override
       public String toString() {
           return "SELECT %s FROM " + this.table +
                  " INNER JOIN folder ON folder.ID = " + this.table + ".FOLDER_ID" +
                  " WHERE PRIORITY=" + this.priority;
       }
   }
   ```

   **生成的SQL模板**：
   ```sql
   SELECT folder.PATH AS PARENT_PATH, NAME, FILE_SIZE, MODIFY_DATE 
   FROM list{N} 
   INNER JOIN folder ON folder.ID = list{N}.FOLDER_ID 
   WHERE PRIORITY={priority}
   ```

3. **任务分配策略**：
   ```java
   private void addSearchTasks(ArrayList<List<SearchTableAndPriority>> nonFormattedSql, SearchTask searchTask)
   ```

   - **按磁盘分组**：每个磁盘分配独立的任务队列
   - **数据库连接管理**：为每个磁盘打开SQLite连接
   - **任务队列构建**：将SQL任务按优先级和表权重分配到队列

#### 3.3.4 数据库搜索任务执行

**任务执行逻辑**：
```java
private void addTaskForDatabase0(String diskChar,
                                ConcurrentLinkedQueue<Runnable> tasks,
                                List<SearchTableAndPriority> sqlToExecute,
                                long preparedSearchInfoId,
                                SearchTask searchTask)
```

**执行步骤**：

1. **缓存优先策略**：
   ```java
   Cache cache = tableCache.get(key);
   if (cache != null && cache.isCacheValid()) {
       // 从内存缓存中并行搜索
       matchedNum = cache.getCacheDataView().parallelStream()
           .filter(s -> checkIsMatchedAndAddToList(false, s.fileName(), parentPath, searchTask))
           .count();
   }
   ```

2. **SQL合并优化**：
   ```java
   // 将多个表的查询合并为UNION ALL语句
   String join = String.join(" UNION ALL ", unionSqlList);
   ```

   **合并后的SQL示例**：
   ```sql
   SELECT folder.PATH AS PARENT_PATH, NAME, FILE_SIZE, MODIFY_DATE 
   FROM list0 INNER JOIN folder ON folder.ID = list0.FOLDER_ID WHERE PRIORITY=10
   UNION ALL
   SELECT folder.PATH AS PARENT_PATH, NAME, FILE_SIZE, MODIFY_DATE 
   FROM list1 INNER JOIN folder ON folder.ID = list1.FOLDER_ID WHERE PRIORITY=10
   -- ... 继续到list40
   ```

3. **Native函数调用**：
   ```java
   PathMatcher.INSTANCE.match(querySql,
       SQLiteUtil.getDbAbsolutePath(diskStr),
       preparedSearchInfoId,
       searchTask.searchInfo.maxResultNum,
       searchResult -> {
           // 处理搜索结果回调
           addSearchResultToContainer(searchTask.tempResults, searchResult);
       });
   ```

#### 3.3.5 PathMatcher Native实现详解

**C++端搜索信息准备**：
```cpp
JNIEXPORT jlong JNICALL Java_file_engine_dllInterface_PathMatcher_prepareSearchInfo
(JNIEnv* env, jobject, jobjectArray search_case, jboolean is_ignore_case, 
 jstring search_text, jobjectArray keywords, jobjectArray keywords_lower, 
 jbooleanArray is_keyword_path, jboolean enable_fuzzy_match)
```

**关键数据结构**：
1. **搜索信息缓存**：
   ```cpp
   emhash6::HashMap<long, search_info*> search_info_map;
   std::atomic_long search_info_id_generator = 1;
   ```

2. **数据库连接池**：
   ```cpp
   emhash6::HashMap<std::string, sqlite3*> connection_map;
   ```

3. **线程池**：
   ```cpp
   BS::thread_pool search_thread_pool;
   ```

**搜索执行流程**：
```cpp
JNIEXPORT void JNICALL Java_file_engine_dllInterface_PathMatcher_match
(JNIEnv* env, jobject, jstring sql, jstring db_path, jlong prepared_search_info_id, 
 jint max_results, jobject result_consumer)
```

**执行步骤**：

1. **获取搜索信息**：
   ```cpp
   search_info* info_pointer = search_info_map.at(search_info_id);
   ```

2. **数据库查询**：
   ```cpp
   sqlite3_stmt* query_stmt = nullptr;
   int rc = sqlite3_prepare_v2(db, sql_str, -1, &query_stmt, nullptr);
   ```

3. **双缓冲区处理**：
   ```cpp
   std::vector<path_match_struct> path_vector_buffer[2];
   BYTE active_buffer = 0;
   BS::multi_future<void> buffer_future[2];
   ```

   **处理逻辑**：
   - 使用两个缓冲区交替处理查询结果
   - 当一个缓冲区填满时，切换到另一个缓冲区
   - 并行处理路径匹配任务

4. **并行路径匹配**：
   ```cpp
   BS::multi_future<void> submit_search_task(const unsigned int path_vector_count,
       const std::vector<path_match_struct>& path_vector,
       search_info* search_info_ptr,
       Concurrency::concurrent_vector<search_result*>& matched_path_vec)
   ```

   **匹配算法**：
   ```cpp
   void is_path_matched_keywords(const path_match_struct& path_info,
       search_info* search_info,
       Concurrency::concurrent_vector<search_result*>& matched_path_vec)
   {
       const int match_type = match_func(parent_path_var, file_name_var, search_info);
       if (match_type == 0 || match_type == 1) {
           // match_type == 0: 完全匹配
           // match_type == 1: 模糊匹配
           search_result* result = new search_result();
           result->path = std::string(parent_path_var) + file_name_var;
           result->fuzzy_matched = match_type == 1;
           matched_path_vec.push_back(result);
       }
   }
   ```

5. **结果回调**：
   ```cpp
   // 创建Java SearchResult对象
   const auto result_obj = env->NewObject(g_search_result_class, g_search_result_constructor,
       path_jstring, time_since_epoch, file_size, fuzzy_matched);
   
   // 调用Java回调函数
   env->CallVoidMethod(result_consumer, g_search_result_accept_func, result_obj);
   ```

**性能优化特性**：

1. **智能缓冲区管理**：
   - 动态调整缓冲区大小：`max(processor_count, 4u) * 50u`
   - 双缓冲区避免等待时间

2. **并行处理**：
   - 使用CPU核心数量确定线程池大小
   - 路径匹配任务并行执行

3. **内存优化**：
   - 使用高效的哈希表实现（emhash6）
   - 并发向量避免锁竞争

4. **数据库优化**：
   - 连接池复用数据库连接
   - SQLite PRAGMA优化设置

### 3.4 并行搜索执行

#### 3.4.1 搜索范围
预搜索阶段会并行启动以下搜索任务：

1. **缓存搜索**：
   ```java
   databaseService.searchCache(searchTask);
   ```
   - 搜索频繁使用的文件缓存
   - 优先返回用户常用文件

2. **优先文件夹搜索**：
   ```java
   databaseService.searchFolder(priorityFolder, searchTask);
   ```
   - 搜索用户配置的优先文件夹

3. **系统文件夹搜索**：
   - **开始菜单**：搜索Windows开始菜单
   - **桌面**：搜索用户桌面和公共桌面
   - **UWP应用**：搜索Windows商店应用

4. **数据库搜索任务准备**：
   ```java
   databaseService.prepareSearchTasks(searchTask);
   ```
   - 为每个磁盘和数据表生成SQL搜索任务

5. **GPU加速搜索**（可选）：
   - 如果启用GPU加速且不是正则匹配
   - 使用CUDA或OpenCL加速搜索

### 3.5 StartSearchEvent 处理流程

#### 3.5.1 事件处理器
```java
@EventRegister(registerClass = StartSearchEvent.class)
private static void startSearchEvent(Event event)
```

**处理步骤**：
1. **输入验证**：与 PrepareSearchEvent 相同
2. **清理过期任务**：清理 `prepareTasksMap` 中的过期任务
3. **任务复用或创建**：
   - 优先使用预搜索任务
   - 如果没有预搜索任务，创建新任务
4. **启动搜索**：
   ```java
   databaseService.startSearchInThreadPool(searchTask);
   ```

#### 3.5.2 搜索任务执行
```java
private void startSearch(SearchTask searchTask)
```

**执行流程**：

1. **任务分发**：
   ```java
   Consumer<ConcurrentLinkedQueue<Runnable>> taskHandler = (taskQueue) -> {
       while (!taskQueue.isEmpty() && eventManagement.notMainExit()) {
           var runnable = taskQueue.poll();
           // 执行搜索任务
           runnable.run();
       }
   };
   ```

2. **多线程执行**：
   - 为每个磁盘分配一个搜索线程
   - 使用 `CountDownLatch` 等待所有线程完成

3. **搜索完成处理**：
   ```java
   waitForTasks(searchTask, countDownLatch);
   ```

### 3.6 数据库搜索详解

#### 3.6.1 表结构设计
- **文件表**：`list0` 到 `list40`（共41个表）
- **文件夹表**：`folder`表存储文件夹路径
- **分表策略**：根据文件名的UTF-8字符和取模分配到不同表

#### 3.6.2 搜索任务生成
```java
private void addSearchTasks(ArrayList<List<SearchTableAndPriority>> nonFormattedSql, SearchTask searchTask)
```

**生成策略**：
1. **按磁盘分组**：每个磁盘一个任务队列
2. **按优先级排序**：根据文件后缀优先级和表权重排序
3. **SQL生成**：为每个表+优先级组合生成SQL查询

#### 3.6.3 搜索执行优化

**缓存优先**：
```java
Cache cache = tableCache.get(key);
if (cache != null && cache.isCacheValid()) {
    // 从内存缓存中搜索
    matchedNum = cache.getCacheDataView().parallelStream()
        .filter(s -> checkIsMatchedAndAddToList(...))
        .count();
}
```

**数据库回退**：
```java
if (!unionSqlList.isEmpty()) {
    String join = String.join(" UNION ALL ", unionSqlList);
    int matchedResultCount = fallbackToSearchDatabase(searchTask, diskStr, join, preparedSearchInfoId);
}
```

#### 3.6.4 表权重动态调整机制

**权重更新策略**：
```java
private void updateTableWeight(String tableName, long weight) {
    TableNameWeightInfo origin = getInfoByName(tableName);
    origin.weight.addAndGet(weight);
    // 更新数据库中的权重记录
    String format = String.format("UPDATE weight SET TABLE_WEIGHT=%d WHERE TABLE_NAME='%s'", 
                                   origin.weight.get(), tableName);
    addToCommandQueue(new SQLWithTaskId(format, SqlTaskIds.UPDATE_WEIGHT, "weight"));
}
```

**权重计算逻辑**：
1. **搜索命中权重**：
   ```java
   final long weight = Math.min(matchedNum, 5); // 最大权重限制为5
   if (weight != 0L) {
       updateTableWeight(tableName, weight);
   }
   ```

2. **平均权重分配**：
   ```java
   int avgWeight = matchedResultCount / unionTableList.size();
   if (avgWeight != 0) {
       for (var eachTable : unionTableList) {
           updateTableWeight(eachTable, Math.min(avgWeight, 5));
       }
   }
   ```

**表优先级排序**：
```java
private ConcurrentLinkedQueue<String> initTableQueueByPriority() {
    ArrayList<TableNameWeightInfo> tmpCommandList = new ArrayList<>(tableSet);
    // 按权重降序排列，权重高的表优先搜索
    tmpCommandList.sort((o1, o2) -> Long.compare(o2.weight.get(), o1.weight.get()));
    
    ConcurrentLinkedQueue<String> tableQueue = new ConcurrentLinkedQueue<>();
    for (TableNameWeightInfo each : tmpCommandList) {
        tableQueue.add(each.tableName);
    }
    return tableQueue;
}
```

**权重数据结构**：
```java
private static class TableNameWeightInfo {
    private final String tableName;
    private final AtomicLong weight; // 使用原子长整型保证线程安全
    
    private TableNameWeightInfo(String tableName, int weight) {
        this.tableName = tableName;
        this.weight = new AtomicLong(weight);
    }
}
```

**权重持久化**：
- 权重信息存储在`weight`数据库表中
- 每次搜索后异步更新权重
- 系统启动时从数据库加载历史权重
- 权重过大时自动减半防止溢出

### 3.7 GPU加速搜索

#### 3.7.1 启用条件
- 配置启用GPU加速
- 非正则表达式搜索
- 搜索任务未停止

#### 3.7.2 执行流程
```java
GPUAccelerator.INSTANCE.match(
    searchInfo.searchCase,
    searchInfo.isIgnoreCase,
    searchInfo.searchText,
    searchInfo.keywords,
    searchInfo.keywordsLowerCase,
    searchInfo.isKeywordPath,
    searchInfo.maxResultNum,
    collectThreadNumber,
    enableFuzzyMatch,
    (key, resultMatched) -> {
        // 处理GPU搜索结果
    });
```

### 3.8 全文搜索（Lucene）

#### 3.8.1 触发条件
- 启用内容索引
- 非目录搜索

#### 3.8.2 执行流程
```java
@EventListener(listenClass = StartSearchEvent.class)
private static void startSearch(Event event) {
    // 为每个磁盘分配搜索线程
    CountDownLatch countDownLatch = new CountDownLatch(luceneIndexMap.size());
    luceneIndexMap.forEach((diskChar, luceneIndexUtil) ->
        ThreadPoolUtil.INSTANCE.executeTask(() -> {
            List<Document> search = luceneIndexUtil.search(searchText, maxResultNumber);
            // 将结果添加到搜索任务
            searchTask.addResultsToTempList(list, true);
        }));
}
```

### 3.9 结果收集和返回

#### 3.9.1 结果容器
```java
public class SearchTask {
    private final ConcurrentHashMap<String, ConcurrentLinkedQueue<SearchResult>> tempResults;
    private final ConcurrentLinkedQueue<UwpResult> uwpResults;
    private final Set<String> tempResultsSet; // 去重
    private final AtomicInteger resultCounter;
}
```

#### 3.9.2 结果分类
- **按后缀优先级分类**：不同文件类型有不同优先级
- **去重处理**：使用 `tempResultsSet` 避免重复结果
- **结果限制**：达到最大结果数量时停止搜索

#### 3.9.3 结果构建
```java
private void addSearchResultToContainer(
    Map<String, ConcurrentLinkedQueue<SearchResult>> searchResultContainer,
    SearchResult searchResult) {
    
    String suffixByPath = getSuffixByPath(searchResult.getPath());
    ConcurrentLinkedQueue<SearchResult> container = searchResultContainer.get(suffixByPath);
    
    if (container != null) {
        container.add(searchResult);
    } else {
        // 分配到默认容器或目录容器
    }
}
```

#### 3.9.4 搜索结果去重和优先级处理

**去重机制**：
```java
public static class SearchTask {
    private final Set<String> tempResultsSet = ConcurrentHashMap.newKeySet();
    private final AtomicInteger resultCounter = new AtomicInteger();
    
    // 添加结果时进行去重检查
    if (searchTask.tempResultsSet.add(searchResult.getPath())) {
        searchTask.resultCounter.getAndIncrement();
        addSearchResultToContainer(searchTask.tempResults, searchResult);
    }
}
```

**结果容器分类**：
```java
private void prepareResultContainer(SearchTask searchTask) {
    // 为每个后缀优先级创建独立容器
    priorityMap.forEach(suffixPriorityPair -> 
        searchTask.tempResults.put(suffixPriorityPair.suffix(), new ConcurrentLinkedQueue<>()));
}
```

**优先级分配逻辑**：
```java
private void addSearchResultToContainer(
    Map<String, ConcurrentLinkedQueue<SearchResult>> searchResultContainer,
    SearchResult searchResult) {
    
    String suffixByPath = getSuffixByPath(searchResult.getPath());
    ConcurrentLinkedQueue<SearchResult> container = searchResultContainer.get(suffixByPath);
    
    if (container != null) {
        // 按文件后缀分配到对应优先级容器
        container.add(searchResult);
    } else {
        if (FileUtil.isDir(searchResult.getPath())) {
            // 目录分配到目录优先级容器
            container = searchResultContainer.get("dirPriority");
        } else {
            // 未知后缀分配到默认优先级容器
            container = searchResultContainer.get("defaultPriority");
        }
        if (container != null) {
            container.add(searchResult);
        }
    }
    
    // 异步更新文件信息
    ThreadPoolUtil.INSTANCE.executeTask(() -> 
        addUpdateFileModifyDateAndFileSizeSql(searchResult.getPath()));
}
```

**后缀优先级映射**：
```java
private record SuffixPriorityPair(String suffix, int priority) {}

// 优先级示例：
// txt -> 10 (高优先级)
// exe -> 9
// jpg -> 8
// defaultPriority -> 0 (默认优先级)
// dirPriority -> -1 (目录优先级)
```

**SearchResult构建**：
```java
public static Optional<SearchResult> buildFromPath(boolean prioritizeResult, 
                                                   String path, 
                                                   boolean contentMatch, 
                                                   boolean fuzzyMatched) {
    try {
        SearchResult searchResult = new SearchResult();
        searchResult.setPath(path);
        searchResult.setPrioritizeResult(prioritizeResult);
        searchResult.setContentMatch(contentMatch);
        searchResult.setFuzzyMatched(fuzzyMatched);
        
        // 设置文件大小和修改时间
        if (Files.exists(Path.of(path))) {
            searchResult.setFileSize(Files.size(Path.of(path)));
            searchResult.setLastModified(Files.getLastModifiedTime(Path.of(path)).toMillis());
        }
        
        return Optional.of(searchResult);
    } catch (Exception e) {
        return Optional.empty();
    }
}
```

**结果数量控制**：
```java
private boolean shouldStopSearch() {
    return resultCounter.get() > searchInfo.maxResultNum || shouldStopSearchFlag;
}

// 在每次添加结果时检查
if (searchTask.shouldStopSearch()) {
    return; // 停止搜索
}
```

**UWP应用结果处理**：
```java
private void checkIsMatchedAndAddToUwpList(UwpResult uwpResult, SearchTask searchTask) {
    Constants.Enums.PathMatchType matchType = PathMatchUtil.check(
        uwpResult.getDisplayName(),
        "",
        searchTask.searchInfo.searchCase,
        searchTask.searchInfo.isIgnoreCase,
        searchTask.searchInfo.searchText,
        searchTask.searchInfo.keywords,
        searchTask.searchInfo.keywordsLowerCase,
        searchTask.searchInfo.isKeywordPath,
        enableFuzzyMatch);
        
    if (matchType == Constants.Enums.PathMatchType.FULL_MATCHED ||
        matchType == Constants.Enums.PathMatchType.FUZZY_MATCHED) {
        searchTask.resultCounter.getAndIncrement();
        if (!searchTask.uwpResults.contains(uwpResult)) {
            searchTask.uwpResults.add(uwpResult);
        }
    }
}
```

## 4. 搜索状态管理

### 4.1 搜索任务状态
```java
public class SearchTask {
    private volatile boolean searchDoneFlag = false;
    private volatile boolean fullTextSearchDone = false;
    private volatile boolean shouldStopSearchFlag = false;
    
    public boolean isSearchDone() {
        return searchDoneFlag && fullTextSearchDone;
    }
}
```

### 4.2 任务生命周期管理
- **任务创建**：分配UUID和创建时间
- **任务复用**：10秒内相同搜索可复用任务
- **任务清理**：定期清理过期的预搜索任务

## 5. 性能优化策略

### 5.1 多级缓存
1. **内存缓存**：频繁访问的文件路径
2. **GPU缓存**：大容量显存缓存
3. **数据库缓存**：用户常用文件统计

### 5.2 并行搜索
1. **磁盘并行**：每个磁盘独立搜索线程
2. **任务并行**：缓存、文件夹、数据库、GPU同时搜索
3. **表并行**：数据库多表同时查询

### 5.3 智能优化
1. **表权重**：根据搜索频率调整表优先级
2. **结果优先级**：按文件类型和使用频率排序
3. **搜索停止**：达到结果数量限制时自动停止

## 6. 错误处理

### 6.1 超时处理
- 数据库状态等待超时：3秒
- 搜索任务执行超时：60秒
- GPU加速等待超时：3秒

### 6.2 异常处理
- 正则表达式语法错误
- 数据库连接失败
- 文件访问权限错误
- GPU加速初始化失败

## 7. 搜索语法支持

### 7.1 基础搜索
- `文件名`：普通文件名搜索
- `/路径`：路径搜索
- `关键字|f`：仅搜索文件
- `关键字|d`：仅搜索目录

### 7.2 高级搜索
- `关键字|c`：内容搜索
- `关键字|case`：区分大小写
- `.*\.txt|p`：正则表达式搜索
- `关键字|full`：全路径匹配

### 7.3 组合搜索
- `关键字|f;case`：仅搜索文件且区分大小写
- `关键字|d;full`：仅搜索目录且全路径匹配

## 8. 搜索流程完整总结

### 8.1 核心设计理念

File-Engine-Core 的搜索系统采用了**预搜索 + 并行执行**的设计模式，通过以下核心机制实现高性能搜索：

1. **事件驱动架构**：使用 `PrepareSearchEvent` 和 `StartSearchEvent` 解耦搜索准备和执行
2. **多级缓存策略**：内存缓存 → GPU缓存 → 数据库缓存的三级缓存体系
3. **智能任务调度**：基于表权重的动态优先级调整
4. **原生性能优化**：C++实现的PathMatcher提供高性能路径匹配

### 8.2 搜索执行流水线

```mermaid
graph TD
    A[HTTP API请求] --> B[PrepareSearchEvent]
    B --> C[关键字预处理]
    C --> D[创建SearchInfo]
    D --> E[准备原生搜索信息]
    E --> F[创建SearchTask]
    F --> G[并行预搜索启动]
    
    G --> H1[缓存搜索]
    G --> H2[优先文件夹搜索]
    G --> H3[系统文件夹搜索]
    G --> H4[数据库任务准备]
    G --> H5[GPU加速搜索]
    
    H4 --> I[SQL生成和优化]
    I --> J[按磁盘分组任务]
    J --> K[PathMatcher Native执行]
    K --> L[结果去重和分类]
    
    H1 --> L
    H2 --> L
    H3 --> L
    H5 --> L
    
    L --> M[StartSearchEvent]
    M --> N[搜索任务执行]
    N --> O[等待所有任务完成]
    O --> P[返回搜索结果]
```

### 8.3 性能优化亮点

#### 8.3.1 数据库层面优化
- **分表策略**：41个数据表（list0-list40）基于文件名哈希分布
- **索引优化**：PRIORITY和FOLDER_ID的复合索引
- **SQL合并**：UNION ALL语句减少数据库连接开销
- **连接池**：每个磁盘维护独立的SQLite连接

#### 8.3.2 并发处理优化
- **磁盘并行**：每个磁盘分配独立搜索线程
- **任务并行**：缓存、文件夹、数据库、GPU同时搜索
- **双缓冲区**：C++端使用双缓冲区避免等待
- **无锁设计**：大量使用ConcurrentHashMap和原子操作

#### 8.3.3 内存管理优化
- **SoftReference**：缓存使用软引用避免内存溢出
- **对象池**：复用SearchResult对象减少GC压力
- **分批处理**：大结果集分批处理避免内存峰值

#### 8.3.4 智能算法优化
- **权重学习**：表权重根据搜索命中率动态调整
- **后缀识别**：智能识别搜索关键词中的文件后缀
- **模糊匹配**：支持拼音和模糊搜索算法
- **提前终止**：达到结果数量限制时智能停止搜索

### 8.4 架构优势

1. **高度模块化**：每个搜索源（缓存、数据库、GPU）独立实现
2. **容错性强**：单个搜索源失败不影响其他搜索源
3. **扩展性好**：新的搜索源可以轻松集成到现有架构
4. **性能可调**：丰富的配置参数支持性能调优
5. **跨平台**：Java + C++混合架构支持多平台部署

### 8.5 技术创新点

1. **预搜索机制**：提前准备搜索任务，减少用户等待时间
2. **混合架构**：Java业务逻辑 + C++性能关键路径
3. **GPU通用计算**：支持CUDA和OpenCL的文件搜索加速
4. **实时监控**：基于NTFS USN Journal的文件系统实时监控
5. **智能缓存**：多级缓存 + 动态失效机制

## 9. 总结
 
File-Engine-Core 的搜索流程设计精巧，通过事件驱动架构实现了高度的并行化和模块化。预搜索机制提供了更好的用户体验，而多级缓存和GPU加速确保了搜索性能。整个流程从HTTP请求到结果返回，充分利用了现代计算机的多核CPU、大容量内存和GPU资源，实现了毫秒级的文件搜索响应。
