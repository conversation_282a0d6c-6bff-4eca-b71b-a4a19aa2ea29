# Aiverything 插件独立页面开发指南

## 概述

本文档专注于 Aiverything 插件的独立页面前端开发，包括HTML页面开发、JavaScript应用架构、状态管理等前端技术的实现。

## 独立页面特点

独立页面在单独的浏览器窗口中运行，拥有完整的应用程序功能，适合复杂的管理界面、数据处理工具等场景。

## 配置设置

在 `plugin.json` 中启用独立页面支持：

```json
{
  "entryPage": "index.html"
}
```

**注意**：独立页面在新窗口中打开，直接使用浏览器进行开发即可。开发时需要使用传统的构建部署方式。

### 页面路径说明

**重要：** `entryPage` 的路径是相对于项目 `src/main/resources/static/` 文件夹的相对路径。

#### 路径配置示例：

- **配置为 `"index.html"`**：
  - 实际文件位置：`src/main/resources/static/index.html`
  - 这是最简单的配置，页面文件直接放在 static 根目录

- **配置为 `"entry/index.html"`**：
  - 实际文件位置：`src/main/resources/static/entry/index.html`
  - 推荐的组织方式，将独立页面放在专门的文件夹中

- **配置为 `"pages/entry/main.html"`**：
  - 实际文件位置：`src/main/resources/static/pages/entry/main.html`
  - 更复杂的目录结构，适合大型插件项目

#### 推荐的目录结构：

```
src/main/resources/static/
├── entry/                      # 独立页面目录
│   ├── index.html              # 独立页面主文件
│   ├── style.css               # 独立页面样式
│   └── script.js               # 独立页面脚本
├── embedded/                   # 嵌入页面目录（如果有）
│   ├── index.html
│   └── app.js
└── icon.png                    # 插件图标
```

#### plugin.json 配置示例：

```json
{
  "name": "My Independent Plugin",
  "identifier": "my-independent-plugin",
  "version": "1.0.0",
  "className": "com.platform.plugin.MyPlugin",
  "entryPage": "entry/index.html",
  "embeddedSupport": true,
  "embeddedPage": "embedded/index.html",
  "icon": "icon.png"
}
```

## 前端开发方式

### 推荐开发框架

**推荐使用 Vite 框架**进行开发，支持Vue、React等多种前端技术栈。Vite是一种新型前端构建工具，能够显著提升前端开发体验：

1. **Vite + Vue**：现代化开发体验，热更新快速
2. **Vite + React**：优秀的构建性能和开发体验  
3. **原生HTML/CSS/JavaScript**：仅适合非常简单的功能实现

### Vite开发配置

**推荐使用Vite框架**，配置简单，构建快速。根据[Vite官方文档](https://vite.dev/guide/)，Vite提供了丰富的内建功能和快速的模块热替换（HMR）。将构建后的 `dist` 文件夹内容放到对应的 `entryPage` 路径下。

#### Vite配置要求

在 `vite.config.js` 中配置 `base` 路径为：`/plugin/static/{pluginIdentifier}/{static文件夹相对路径}`

**Vite配置示例（vite.config.js）**：
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue' // 如果使用Vue
// import react from '@vitejs/plugin-react' // 如果使用React

export default defineConfig({
  plugins: [vue()], // 或 [react()]
  
  // 独立页面配置
  base: '/plugin/static/my-plugin-identifier/entry',
})
```

#### 路径配置说明

- `{pluginIdentifier}`：替换为你的插件标识符（plugin.json中的identifier字段）
- `{static文件夹相对路径}`：
  - 独立页面：`entry`
  - 嵌入页面：`embedded`
  - 具体取决于您在项目中static文件夹中创建的文件夹名

**配置示例**：
```javascript
// 如果插件identifier为 "my-calculator"
// src/main/resources/static中存在两个文件夹，一个embedded，一个entry
// 独立页面配置
base: '/plugin/static/my-calculator/entry'

// 嵌入页面配置  
base: '/plugin/static/my-calculator/embedded'
```

#### 快速创建项目

使用Vite官方命令创建项目：

```bash
npm create vite@latest

# 进入项目目录并安装依赖
cd my-plugin-frontend
npm install
```

#### 部署流程

1. **开发阶段**：在Vite项目中正常开发
2. **构建阶段**：使用正确的 base 路径配置进行构建
3. **部署阶段**：将 `dist` 文件夹内容复制到插件项目的对应路径

```bash
# 构建项目
npm run build

# 构建完成后的文件部署
cp -r vite-project/dist/* src/main/resources/static/entry/
```

### 原生HTML开发示例

对于简单功能，仍可使用原生HTML开发：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>独立插件应用</title>
    <!-- 引用同目录下的CSS文件 -->
    <link rel="stylesheet" href="style.css">
    <!-- 或引用其他目录的CSS文件 -->
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body>
    <div id="app">
        <header class="app-header">
            <h1>插件标题</h1>
            <nav class="app-nav">
                <button onclick="showSection('dashboard')">仪表板</button>
                <button onclick="showSection('settings')">设置</button>
                <button onclick="showSection('about')">关于</button>
            </nav>
        </header>
        
        <main class="app-main">
            <section id="dashboard" class="section active">
                <h2>仪表板</h2>
                <div class="dashboard-content">
                    <!-- 仪表板内容 -->
                    <img src="../assets/images/logo.png" alt="插件Logo">
                </div>
            </section>
            
            <section id="settings" class="section">
                <h2>设置</h2>
                <div class="settings-content">
                    <!-- 设置内容 -->
                </div>
            </section>
        </main>
        
        <footer class="app-footer">
            <p>&copy; 2024 插件开发者</p>
        </footer>
    </div>
    
    <!-- 引用同目录下的JS文件 -->
    <script src="script.js"></script>
    <!-- 或引用其他目录的JS文件 -->
    <script src="../assets/utils.js"></script>
</body>
</html>
```

### Vite + Vue开发示例

#### 创建Vite Vue项目
```bash
npm create vite@latest my-plugin-frontend -- --template vue
cd my-plugin-frontend
npm install
```

#### Vue组件结构

```vue
<template>
  <div id="app">
    <header class="app-header">
      <h1>{{ title }}</h1>
      <nav class="app-nav">
        <button @click="activeSection = 'dashboard'" :class="{ active: activeSection === 'dashboard' }">
          仪表板
        </button>
        <button @click="activeSection = 'settings'" :class="{ active: activeSection === 'settings' }">
          设置
        </button>
      </nav>
    </header>
    
    <main class="app-main">
      <section v-if="activeSection === 'dashboard'" class="section">
        <h2>仪表板</h2>
        <div class="dashboard-content">
          <div class="data-card" v-for="item in dashboardData" :key="item.id">
            <h3>{{ item.title }}</h3>
            <p>{{ item.value }}</p>
          </div>
        </div>
      </section>
      
      <section v-if="activeSection === 'settings'" class="section">
        <h2>设置</h2>
        <div class="settings-content">
          <form @submit.prevent="saveSettings">
            <label>
              配置项：
              <input v-model="settings.configValue" type="text" />
            </label>
            <button type="submit">保存设置</button>
          </form>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const title = ref('独立插件应用')
const activeSection = ref('dashboard')
const dashboardData = ref([])
const settings = ref({
  configValue: ''
})

const loadDashboardData = async () => {
  try {
    // 调用后端API获取数据
    const response = await fetch('/api/dashboard-data')
    dashboardData.value = await response.json()
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

const saveSettings = async () => {
  try {
    // 保存设置到后端
    await fetch('/api/save-settings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(settings.value)
    })
    alert('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>
```

### Vite + React开发示例

#### 创建Vite React项目
```bash
npm create vite@latest my-plugin-frontend -- --template react
cd my-plugin-frontend
npm install
```

#### React组件结构

```jsx
import React, { useState, useEffect } from 'react'

function IndependentApp() {
  const [activeSection, setActiveSection] = useState('dashboard')
  const [dashboardData, setDashboardData] = useState([])
  const [settings, setSettings] = useState({
    configValue: ''
  })

  const loadDashboardData = async () => {
    try {
      // 调用后端API获取数据
      const response = await fetch('/api/dashboard-data')
      const data = await response.json()
      setDashboardData(data)
    } catch (error) {
      console.error('加载数据失败:', error)
    }
  }

  const saveSettings = async (e) => {
    e.preventDefault()
    try {
      // 保存设置到后端
      await fetch('/api/save-settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
      })
      alert('设置保存成功')
    } catch (error) {
      console.error('保存设置失败:', error)
    }
  }

  useEffect(() => {
    loadDashboardData()
  }, [])

  return (
    <div id="app">
      <header className="app-header">
        <h1>独立插件应用</h1>
        <nav className="app-nav">
          <button 
            onClick={() => setActiveSection('dashboard')}
            className={activeSection === 'dashboard' ? 'active' : ''}
          >
            仪表板
          </button>
          <button 
            onClick={() => setActiveSection('settings')}
            className={activeSection === 'settings' ? 'active' : ''}
          >
            设置
          </button>
        </nav>
      </header>
      
      <main className="app-main">
        {activeSection === 'dashboard' && (
          <section className="section">
            <h2>仪表板</h2>
            <div className="dashboard-content">
              {dashboardData.map(item => (
                <div key={item.id} className="data-card">
                  <h3>{item.title}</h3>
                  <p>{item.value}</p>
                </div>
              ))}
            </div>
          </section>
        )}
        
        {activeSection === 'settings' && (
          <section className="section">
            <h2>设置</h2>
            <div className="settings-content">
              <form onSubmit={saveSettings}>
                <label>
                  配置项：
                  <input 
                    type="text"
                    value={settings.configValue}
                    onChange={(e) => setSettings({
                      ...settings,
                      configValue: e.target.value
                    })}
                  />
                </label>
                <button type="submit">保存设置</button>
              </form>
            </div>
          </section>
        )}
      </main>
    </div>
  )
}

export default IndependentApp
```

### 资源文件引用说明

在独立页面中引用其他资源文件时，路径是相对于当前HTML文件的相对路径：

#### 如果 entryPage 配置为 `"entry/index.html"`：

- **当前页面位置**：`static/entry/index.html`
- **引用同目录文件**：`href="style.css"` → `static/entry/style.css`
- **引用上级目录文件**：`href="../icon.png"` → `static/icon.png`
- **引用其他目录文件**：`href="../assets/common.css"` → `static/assets/common.css`

#### 常见的资源引用示例：

```html
<!-- 样式文件 -->
<link rel="stylesheet" href="style.css">                    <!-- 同目录 -->
<link rel="stylesheet" href="../assets/theme.css">          <!-- 共享样式 -->

<!-- 脚本文件 -->
<script src="script.js"></script>                           <!-- 同目录 -->
<script src="../assets/utils.js"></script>                  <!-- 共享工具 -->

<!-- 图片文件 -->
<img src="icon.png" alt="图标">                             <!-- 同目录 -->
<img src="../assets/images/logo.png" alt="Logo">            <!-- 共享图片 -->

<!-- 字体文件 -->
<style>
@font-face {
    font-family: 'CustomFont';
    src: url('../assets/fonts/custom.woff2') format('woff2');
}
</style>
```

## 前端调用后端API

前端可以通过HTTP POST请求调用后端注册的命令处理器。

### 后端命令注册

首先在Java后端注册命令处理器：

```java
public class TestPlugin extends Plugin {
    @Override
    public void load(Map<String, Object> config) {
        // 注册测试命令 - 对应前端调用的 "testCommand"
        registerCommandHandler("testCommand", paramsMap -> {
            String param1 = (String) paramsMap.get("param1");
            String param2 = (String) paramsMap.get("param2");
            
            // 处理业务逻辑
            String result = "处理完成: " + param1 + ", " + param2;
            
            return Map.of("result", result, "success", true);
        });
    }
}
```

### API调用配置

```javascript
// API基础配置
const baseUrl = process.env.NODE_ENV === "development"
  ? "https://localhost:38884/plugin/invoke"  // 开发环境
  : `${window.location.origin}/plugin/invoke`; // 生产环境

const pluginIdentifier = "your-plugin-identifier"; // 替换为实际的插件标识符
```

### 调用后端命令

```javascript
// 通用API调用函数
function callPluginCommand(command, params = {}) {
  const queryParams = new URLSearchParams({
    pluginIdentifier: pluginIdentifier,
    command: command,
  }).toString();
  
  const fullUrl = `${baseUrl}?${queryParams}`;
  
  return fetch(fullUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(params),
  });
}

// 使用示例
async function executeCommand() {
  try {
    const response = await callPluginCommand("testCommand", {
      param1: "value1",  // 对应后端 paramsMap.get("param1")
      param2: "value2"   // 对应后端 paramsMap.get("param2")
    });
    
    if (response.ok) {
      const result = await response.json();
      // result.result 和 result.success 对应后端返回的 Map.of("result", result, "success", true)
      console.log("命令执行结果:", result.result);
      console.log("执行状态:", result.success);
      return result;
    } else {
      throw new Error(`HTTP错误: ${response.status}`);
    }
  } catch (error) {
    console.error("调用失败:", error);
    throw error;
  }
}
```

## 性能优化

### 事件防抖

```javascript
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

const debouncedSearch = debounce(handleSearchInput, 300);
```

## 最佳实践

### 安全性
- 验证消息来源
- 净化用户输入
- 避免XSS攻击

### 用户体验
- 提供加载状态
- 优雅的错误处理
- 响应式设计

### 代码质量
- 模块化代码组织
- 详细的错误日志
- 全面的测试覆盖

## Vite开发完整工作流程

### Vite + Vue项目示例

#### 1. 创建Vite Vue项目
```bash
npm create vite@latest my-plugin-frontend -- --template vue
cd my-plugin-frontend
npm install
```

#### 2. 配置vite.config.js
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  
  // 配置base路径，替换为实际的插件标识符
  base: '/plugin/static/my-calculator/entry/',
  
  // 开发服务器配置
  server: {
    port: 5173,
    open: true
  }
})
```

### Vite + React项目示例

#### 1. 创建Vite React项目
```bash
npm create vite@latest my-plugin-frontend -- --template react
cd my-plugin-frontend
npm install
```

#### 2. 配置vite.config.js
```javascript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  
  // 配置base路径，替换为实际的插件标识符
  base: '/plugin/static/my-calculator/entry/',
  
  server: {
    port: 5173,
    open: true
  }
})
```
---

*本文档专注于独立页面开发，关于嵌入页面和后端开发请参考相应文档。* 