import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";
const aiModeSlice = createSlice({
  name: "aiMode",
  initialState: {
    value: false,
    enterKeyState: false,
  },
  reducers: {
    toggleAIMode: (state) => {
      state.value = !state.value;
    },
    setEnterKeyState: (state, action: PayloadAction<boolean>) => {
      state.enterKeyState = action.payload;
    },
  },
});

export const { toggleAIMode, setEnterKeyState } = aiModeSlice.actions;
export const selectAIMode = (state: RootState) => state.aiMode.value;
export const selectEnterKeyState = (state: RootState) =>
  state.aiMode.enterKeyState;
export default aiModeSlice.reducer;
