struct GetHandleLib {
    start_func: libloading::Symbol<'static, unsafe extern "C" fn()>,
    stop_func: libloading::Symbol<'static, unsafe extern "C" fn()>,
    change_to_attach_func: libloading::Symbol<'static, unsafe extern "C" fn() -> u32>,
    change_to_normal_func: libloading::Symbol<'static, unsafe extern "C" fn() -> u32>,
    get_explorer_x_func: libloading::Symbol<'static, unsafe extern "C" fn() -> u64>,
    get_explorer_y_func: libloading::Symbol<'static, unsafe extern "C" fn() -> u64>,
    get_explorer_width_func: libloading::Symbol<'static, unsafe extern "C" fn() -> u64>,
    get_explorer_height_func: libloading::Symbol<'static, unsafe extern "C" fn() -> u64>,
    get_explorer_path_func:
        libloading::Symbol<'static, unsafe extern "C" fn(*mut std::ffi::c_char, u64)>,
    is_dialog_window_func: libloading::Symbol<'static, unsafe extern "C" fn() -> u32>,
    set_edit_path_func: libloading::Symbol<
        'static,
        unsafe extern "C" fn(*const std::ffi::c_char, *const std::ffi::c_char) -> u32,
    >,
    bring_window_to_top_func: libloading::Symbol<'static, unsafe extern "C" fn() -> u32>,
}

impl GetHandleLib {
    pub fn new(lib_name: &str) -> Result<Self, Box<dyn std::error::Error>> {
        unsafe {
            let lib = libloading::Library::new(lib_name)?;
            let lib = Box::leak(Box::new(lib));

            Ok(Self {
                start_func: lib.get(b"start")?,
                stop_func: lib.get(b"stop")?,
                change_to_attach_func: lib.get(b"changeToAttach")?,
                change_to_normal_func: lib.get(b"changeToNormal")?,
                get_explorer_x_func: lib.get(b"getExplorerX")?,
                get_explorer_y_func: lib.get(b"getExplorerY")?,
                get_explorer_width_func: lib.get(b"getExplorerWidth")?,
                get_explorer_height_func: lib.get(b"getExplorerHeight")?,
                get_explorer_path_func: lib.get(b"getExplorerPath")?,
                is_dialog_window_func: lib.get(b"isDialogWindow")?,
                set_edit_path_func: lib.get(b"setEditPath")?,
                bring_window_to_top_func: lib.get(b"bringWindowToTop")?,
            })
        }
    }
}

const GET_HANDLE_LIB_NAME: &str = "getHandle.dll";

lazy_static::lazy_static! {
    static ref get_handle_lib: GetHandleLib = GetHandleLib::new(GET_HANDLE_LIB_NAME)
    .expect("Failed to create API instance");
}

pub fn start() -> Result<(), Box<dyn std::error::Error>> {
    unsafe {
        (get_handle_lib.start_func)();
        Ok(())
    }
}

pub fn stop() -> Result<(), Box<dyn std::error::Error>> {
    unsafe {
        (get_handle_lib.stop_func)();
        Ok(())
    }
}

pub fn change_to_attach() -> Result<u32, Box<dyn std::error::Error>> {
    unsafe { Ok((get_handle_lib.change_to_attach_func)()) }
}

pub fn change_to_normal() -> Result<u32, Box<dyn std::error::Error>> {
    unsafe { Ok((get_handle_lib.change_to_normal_func)()) }
}

pub fn get_explorer_x() -> Result<u64, Box<dyn std::error::Error>> {
    unsafe { Ok((get_handle_lib.get_explorer_x_func)()) }
}

pub fn get_explorer_y() -> Result<u64, Box<dyn std::error::Error>> {
    unsafe { Ok((get_handle_lib.get_explorer_y_func)()) }
}

pub fn get_explorer_width() -> Result<u64, Box<dyn std::error::Error>> {
    unsafe { Ok((get_handle_lib.get_explorer_width_func)()) }
}

pub fn get_explorer_height() -> Result<u64, Box<dyn std::error::Error>> {
    unsafe { Ok((get_handle_lib.get_explorer_height_func)()) }
}

pub fn get_explorer_path(
    output: *mut std::ffi::c_char,
    len: u64,
) -> Result<(), Box<dyn std::error::Error>> {
    unsafe {
        (get_handle_lib.get_explorer_path_func)(output, len);
        Ok(())
    }
}

pub fn is_dialog_window() -> Result<u32, Box<dyn std::error::Error>> {
    unsafe { Ok((get_handle_lib.is_dialog_window_func)()) }
}

pub fn set_edit_path(
    path: *const std::ffi::c_char,
    file_name: *const std::ffi::c_char,
) -> Result<u32, Box<dyn std::error::Error>> {
    unsafe { Ok((get_handle_lib.set_edit_path_func)(path, file_name)) }
}

pub fn bring_window_to_top() -> Result<u32, Box<dyn std::error::Error>> {
    unsafe { Ok((get_handle_lib.bring_window_to_top_func)()) }
}
