import { Provider } from "react-redux";
import store from "./store";
import MainWindow from "./MainWindow";
import {
  getCurrentWindow,
  PhysicalPosition,
  currentMonitor,
} from "@tauri-apps/api/window";
import { TrayIcon } from "@tauri-apps/api/tray";
import { Menu } from "@tauri-apps/api/menu";
import { useTranslation } from "react-i18next";
import { WebviewWindow } from "@tauri-apps/api/webviewWindow";
import { defaultWindowIcon } from "@tauri-apps/api/app";
import { preExit } from "./api/aiverything";
import { exit, relaunch } from "@tauri-apps/plugin-process";
import { open } from "@tauri-apps/plugin-shell";
import { useEffect, useRef, useState } from "react";
import { onOpenUrl } from "@tauri-apps/plugin-deep-link";
import { check } from "@tauri-apps/plugin-updater";
import {
  sendNotification,
  requestPermission,
  isPermissionGranted,
} from "@tauri-apps/plugin-notification";
export const MAIN_QUERY_WIDTH = 600;

function App() {
  const [menuInitialized, setMenuInitialized] = useState<boolean>(false);
  const trayObjRef = useRef<TrayIcon | null>(null);

  const { t } = useTranslation();

  const sendSystemNotification = async (title, body) => {
    // Do you have permission to send a notification?
    let permissionGranted = await isPermissionGranted();

    // If not we need to request it
    if (!permissionGranted) {
      const permission = await requestPermission();
      permissionGranted = permission === "granted";
    }
    // Once permission has been granted we can send the notification
    if (permissionGranted) {
      sendNotification({ title, body });
    }
  };

  // 检查更新
  const checkForUpdates = async () => {
    try {
      // 获取更新信息
      const update = await check();

      if (update?.available) {
        // 发送通知
        await sendSystemNotification(
          t("update.available"),
          t("update.newVersionAvailable", { version: update.version })
        );

        console.log(`New version available: ${update.version}`);
      }
    } catch (error) {
      console.error("Error checking for updates:", error);
    }
  };

  const openPluginSettings = () => {
    const webview = new WebviewWindow("pluginSettings", {
      url: "pluginSettings.html",
      width: 800,
      height: 600,
      center: true,
      title: t("pluginSettings.title"),
    });
    webview.once("tauri://created", function () {});
    webview.once("tauri://error", function (e) {
      console.error(e);
    });
  };

  const openSettings = () => {
    WebviewWindow.getByLabel("settings")
      .then((window) => {
        window.unminimize();
        window.setFocus();
      })
      .catch((e) => {
        // Create new window if none exists
        const webview = new WebviewWindow("settings", {
          url: "settings.html",
          width: 800,
          height: 600,
          center: true,
          title: t("settings.title"),
        });

        webview.once("tauri://created", function () {
          // webview window successfully created
        });
        webview.once("tauri://error", function (e) {
          console.error("Error creating settings window:", e);
        });
      });
  };

  const createTray = async () => {
    if (trayObjRef.current) {
      trayObjRef.current.close();
      trayObjRef.current = null;
    }
    const menu = await Menu.new({
      items: [
        {
          id: "settings",
          text: t("tray.settings"),
          action: () => {
            openSettings();
          },
        },
        {
          id: "pluginSettings",
          text: t("tray.pluginSettings"),
          action: () => {
            openPluginSettings();
          },
        },
        {
          id: "pluginStore",
          text: t("tray.pluginStore"),
          action: () => {
            open("https://aiverything.me/market");
          },
        },
        {
          id: "separator",
          item: "Separator",
        },
        {
          id: "restart",
          text: t("tray.restart"),
          action: () => {
            preExit().then(() => {
              relaunch();
            });
          },
        },
        {
          id: "quit",
          text: t("tray.quit"),
          action: () => {
            preExit().then(() => {
              exit(0);
            });
          },
        },
      ],
    });

    const defaultIcon = await defaultWindowIcon();

    const options = {
      menu,
      menuOnLeftClick: false,
      tooltip: "Aiverything",
      icon: defaultIcon,
      action: (event) => {
        switch (event.type) {
          case "Click":
          case "DoubleClick":
            if (event.button === "Left" && event.buttonState === "Down") {
              const mainWindow = WebviewWindow.getByLabel("main");
              mainWindow.then((searchBarWindow) => {
                searchBarWindow.show();
                searchBarWindow.setFocus();
              });
            }
            break;
        }
      },
    };
    const trayObject = await TrayIcon.new(options);
    trayObjRef.current = trayObject;
  };

  useEffect(() => {
    onOpenUrl((url) => {
      openSettings();
    });
  }, []);

  useEffect(() => {
    if (!menuInitialized) {
      createTray().then(() => {
        setMenuInitialized(true);
      });
    }
  }, [menuInitialized]);

  // 添加启动时检查更新的逻辑
  useEffect(() => {
    // 应用启动时检查更新
    checkForUpdates();
  }, []);

  const putToCenter = () => {
    const appWindow = getCurrentWindow();
    currentMonitor().then((monitor) => {
      appWindow.scaleFactor().then((dpiFactor) => {
        const monitorSize = monitor?.size;
        const padding = (monitorSize.width - MAIN_QUERY_WIDTH * dpiFactor) / 2;
        const newX = monitor?.position.x + padding;

        appWindow.setPosition(
          new PhysicalPosition(
            newX,
            monitor?.position.y + monitorSize.height / 2 - 200 * dpiFactor
          )
        );
      });
    });
  };

  putToCenter();

  const setTheme = (isDark) => {
    document.documentElement.classList.remove("dark");
    if (isDark) {
      document.documentElement.classList.add("dark");
    }
  };

  setTheme(window.matchMedia("(prefers-color-scheme: dark)").matches);

  window
    .matchMedia("(prefers-color-scheme: dark)")
    .addEventListener("change", (e) => {
      const newIsDark = e.matches;
      setTheme(newIsDark);
    });

  return (
    <Provider store={store}>
      <MainWindow />
    </Provider>
  );
}

export default App;
