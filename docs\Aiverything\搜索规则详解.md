# Aiverything 搜索规则详解

## 📋 目录
- [基础搜索规则](#基础搜索规则)
- [多关键词搜索](#多关键词搜索)
- [搜索过滤器](#搜索过滤器)
- [通配符搜索](#通配符搜索)
- [正则表达式搜索](#正则表达式搜索)
- [缓存搜索](#缓存搜索)
- [内容搜索](#内容搜索)
- [文件属性搜索](#文件属性搜索)
- [路径搜索](#路径搜索)
- [组合搜索技巧](#组合搜索技巧)
- [AI搜索规则](#ai搜索规则)
- [插件搜索](#插件搜索)
- [搜索优化建议](#搜索优化建议)

---

## 🔍 基础搜索规则

### 简单文件名搜索

#### 基本语法
```
直接输入文件名或关键词
```

#### 使用示例
```
document          # 搜索包含"document"的文件
report            # 搜索包含"report"的文件
image             # 搜索包含"image"的文件
2024              # 搜索包含"2024"的文件
```

#### 搜索特性
- **实时搜索**: 输入即搜索，无需按回车键
- **模糊匹配**: 支持部分匹配和智能纠错
- **智能排序**: 根据文件访问频率和相关性排序
- **大小写不敏感**: 默认不区分大小写

### 文件扩展名搜索

#### 基本语法
```
.扩展名           # 搜索特定类型的文件
```

#### 常用示例
```
.pdf              # 所有PDF文件
.docx             # 所有Word文档
.xlsx             # 所有Excel文件
.pptx             # 所有PowerPoint文件
.jpg              # 所有JPG图片
.png              # 所有PNG图片
.mp4              # 所有MP4视频
.txt              # 所有文本文件
.zip              # 所有压缩文件
```

---

## 🔗 多关键词搜索

### 基本语法
使用分号 `;` 分隔多个关键词：

```
关键词1;关键词2;关键词3
```

### AND逻辑搜索

#### 语法说明
分号分隔的关键词使用AND逻辑，即搜索同时包含所有关键词的文件。

#### 使用示例
```
project;report              # 同时包含"project"和"report"
sales;2024;excel           # 同时包含三个关键词的文件
photo;vacation;summer      # 假期夏天的照片
work;document;important    # 重要的工作文档
```

#### 复杂组合
```
# 文件名和扩展名组合
project;report;.pdf        # 项目报告PDF文件
meeting;notes;.docx        # 会议记录Word文档
data;analysis;.xlsx        # 数据分析Excel文件

# 路径和文件名组合
Desktop;work;file          # 桌面工作文件夹中的文件
Downloads;software;.exe    # 下载文件夹中的软件
```

### OR逻辑搜索

#### 语法说明
使用`?`分隔关键词实现OR逻辑：

```
?关键词1;?关键词2;?关键词3
```

#### 使用示例
```
?report;?summary             # 包含"report"或"summary"的文件
?.jpg;?.png;?.gif            # 图片文件（JPG或PNG或GIF）
?project;?task;?work         # 工作相关文件
```

---

## 🔧 搜索过滤器

### 过滤器语法
使用竖线 `|` 添加搜索条件：

```
搜索词|过滤器
```

### 基础过滤器

#### 文件类型过滤

| 过滤器 | 说明 | 示例 | 效果 |
|--------|------|------|------|
| `f` | 仅搜索文件 | `photo|f` | 只显示文件，不显示文件夹 |
| `d` | 仅搜索目录 | `project|d` | 只显示文件夹，不显示文件 |

**使用示例**：
```
document|f               # 只搜索名称包含"document"的文件
work|d                   # 只搜索名称包含"work"的文件夹
temp;.txt|f             # 只搜索包含"temp"的txt文件
backup|d                # 只搜索备份文件夹
```

#### 匹配模式过滤

| 过滤器 | 说明 | 示例 | 效果 |
|--------|------|------|------|
| `full` | 全匹配 | `readme.txt|full` | 完全匹配文件名 |
| `case` | 区分大小写 | `Test|case` | 严格区分大小写 |

**使用示例**：
```
config.json|full         # 精确匹配"config.json"文件
README|case             # 区分大小写搜索"README"
index.html|full         # 精确匹配"index.html"
License|case;f          # 区分大小写搜索文件
```

### 高级过滤器

#### 正则表达式过滤

| 过滤器 | 说明 | 示例 | 效果 |
|--------|------|------|------|
| `p` | 正则表达式 | `\d+\.txt|p` | 使用正则表达式匹配 |

**使用示例**：
```
\d+\.txt|p              # 以数字开头的txt文件
[A-Z].*\.pdf|p          # 以大写字母开头的PDF文件
.*_backup_\d+.*|p       # 包含备份标识的文件
\w+\.(jpg|png|gif)|p    # 图片文件（正则方式）
```

#### 内容搜索过滤

| 过滤器 | 说明 | 示例 | 效果 |
|--------|------|------|------|
| `c` | 内容搜索 | `function|c` | 搜索文件内容 |

**使用示例**：
```
password|c              # 搜索内容包含"password"的文件
TODO|c                  # 搜索内容包含"TODO"的文件
import;.py|c;f          # Python文件中包含"import"的
class;.java|c;f         # Java文件中包含"class"的
```

### 过滤器组合

#### 多过滤器组合
```
搜索词;过滤器1;过滤器2;过滤器3
```

#### 组合示例
```
test|f;case             # 区分大小写搜索文件中的"test"
config|full;f           # 精确匹配名为"config"的文件
data|d;case             # 区分大小写搜索文件夹
\d+|p;f                 # 使用正则搜索包含数字的文件
```

---

## 🔤 正则表达式搜索

### 正则表达式基础

#### 启用正则表达式
```
搜索模式|p
```

#### 基本元字符

| 元字符 | 说明 | 示例 | 匹配结果 |
|--------|------|------|----------|
| `.` | 任意单个字符 | `file.txt|p` | file1.txt, fileA.txt |
| `\d` | 数字字符 | `\d+\.txt|p` | 123.txt, 45.txt |
| `\w` | 字母数字下划线 | `\w+_backup|p` | file_backup, data_backup |
| `\s` | 空白字符 | `file\s+name|p` | "file name", "file  name" |
| `^` | 行开始 | `^README|p` | 以README开头 |
| `$` | 行结束 | `\.txt$|p` | 以.txt结尾 |

#### 数量限定符

| 限定符 | 说明 | 示例 | 匹配结果 |
|--------|------|------|----------|
| `*` | 0次或多次 | `file.*\.txt|p` | file.txt, file123.txt |
| `+` | 1次或多次 | `\d+|p` | 1, 123, 4567 |
| `?` | 0次或1次 | `files?|p` | file, files |
| `{n}` | 恰好n次 | `\d{4}|p` | 2024, 1999 |
| `{n,m}` | n到m次 | `\d{2,4}|p` | 12, 123, 1234 |

### 实用正则表达式

#### 日期格式匹配
```
\d{4}-\d{2}-\d{2}|p     # 2024-01-15格式
\d{1,2}/\d{1,2}/\d{4}|p # 1/15/2024格式
\d{4}_\d{2}_\d{2}|p     # 2024_01_15格式
```

#### 文件版本匹配
```
v\d+\.\d+\.\d+|p        # v1.2.3版本格式
_v\d+|p                 # _v1, _v2版本后缀
\(\d+\)|p               # (1), (2)版本括号
```

#### 特殊文件名模式
```
[A-Z]{2,}_\d+|p         # 大写字母+数字（如：IMG_1234）
\d{8}_\d{6}|p           # 时间戳格式（20240115_143052）
[a-f0-9]{32}|p          # MD5哈希值格式
[0-9a-fA-F-]{36}|p      # UUID格式
```

#### 编程文件匹配
```
.*\.(js|ts|jsx|tsx)|p   # JavaScript/TypeScript文件
.*\.(py|pyw)|p          # Python文件
.*\.(java|class)|p      # Java文件
.*\.(c|cpp|h|hpp)|p     # C/C++文件
```

### 高级正则表达式

#### 字符类和分组
```
[0-9a-fA-F]+|p          # 十六进制字符
(jpg|jpeg|png|gif)|p    # 图片扩展名
(?i)readme.*|p          # 忽略大小写的readme文件
(?=.*backup)(?=.*2024)|p # 同时包含backup和2024
```

#### 负向匹配
```
^(?!temp).*|p           # 不以temp开头的文件
.*(?<!\.tmp)$|p         # 不以.tmp结尾的文件
```

---

## 💾 缓存搜索

### 缓存搜索基础

#### 基本语法
```
:搜索词
```

#### 功能说明
缓存搜索是在最近使用的文件中进行快速搜索，响应速度更快。

### 使用示例

#### 基本缓存搜索
```
:report                 # 从缓存中搜索包含"report"的文件
:project                # 搜索最近使用的项目文件
:image                  # 搜索最近访问的图片文件
:document               # 搜索最近的文档文件
```

### 缓存特性

#### 智能排序
- 按最近访问时间排序
- 按使用频率排序

#### 缓存更新
- 自动更新最近使用的文件
- 清理长时间未访问的缓存
- 手动清理缓存选项

### 缓存管理

#### 查看缓存
```
# 在设置中查看缓存列表
设置 → 缓存设置 → 缓存列表
```

---

## 📄 内容搜索

### 基本语法
```
搜索词|c
```

### 支持的文件格式

#### 文本文件
- `.txt` - 纯文本文件
- `.log` - 日志文件
- `.cfg` - 配置文件
- `.ini` - 初始化文件
- `.xml` - XML文件
- `.json` - JSON文件
- `.csv` - CSV数据文件

#### 文档文件
- `.pdf` - PDF文档
- `.doc/.docx` - Word文档
- `.xls/.xlsx` - Excel表格
- `.ppt/.pptx` - PowerPoint演示文稿
- `.rtf` - 富文本文件

#### 代码文件
- `.html/.htm` - HTML文件
- `.css` - 样式表文件
- `.js` - JavaScript文件
- `.py` - Python文件
- `.java` - Java文件
- `.cpp/.c` - C/C++文件

### 内容搜索示例

#### 基本内容搜索
```
password|c              # 搜索内容包含"password"的文件
function|c              # 搜索包含"function"的代码文件
TODO|c                  # 搜索包含"TODO"的文件
error|c                 # 搜索包含"error"的日志文件
```

#### 组合内容搜索
```
import;.py|c;f          # Python文件中包含"import"的
class;.java|c;f         # Java文件中包含"class"的
SELECT;.sql|c;f         # SQL文件中包含"SELECT"的
console.log;.js|c;f     # JavaScript文件中包含"console.log"的
```

---

## 📁 路径搜索（关键字前方加上`/`或者`\`）

### 绝对路径搜索

#### 完整路径
```
/C:\Users\<USER>\Documents\        # 特定文件夹
/D:\Projects\WebApp\                 # 项目文件夹
```

#### 路径组件搜索
```
\Documents\                         # 任意Documents文件夹
\Desktop\                          # 任意Desktop文件夹
\temp\                             # 任意temp文件夹
```

---

## 🤖 AI搜索规则

### 启用AI搜索

#### 激活方法
```
# 点击搜索框右侧的AI图标
# 或在搜索框中使用自然语言
```

### 自然语言搜索

#### 基本语法
使用自然语言直接描述搜索需求：

```
"帮我找一下上周的工作报告"
"查找包含销售数据的Excel文件"
"最近修改的图片文件"
"桌面上的PDF文档"
```

### AI搜索优化

#### 搜索提示
- 使用具体的描述词汇
- 包含时间、大小、类型等限定词
- 避免过于模糊的表达
- 可以使用问句形式

#### 语言支持
- 支持中文自然语言搜索
- 支持英文自然语言搜索
- 支持混合语言搜索
- 理解上下文和语义

---

## 🔌 插件搜索

### 进入插件模式

#### 基本语法
```
>插件名称
```

#### 插件搜索示例
```
>calculator             # 搜索计算器插件
>weather                # 搜索天气插件
>translator             # 搜索翻译插件
>notepad                # 搜索记事本插件
>system                 # 搜索系统工具插件
```

### 插件操作

#### 安装和使用
```
# 1. 搜索插件
>plugin_name

# 2. 选择插件按Enter安装
```

---

## ⚡ 搜索优化建议

### 性能优化

#### 索引优化
```
# 1. 合理设置索引范围
只索引必要的磁盘和文件夹

# 2. 排除不必要的文件
设置 → 索引设置 → 忽略目录
- 系统临时文件夹
- 回收站
- 缓存目录

# 3. 定期更新索引
设置 → 索引设置 → 更新索引
```

#### 搜索技巧
```
# 1. 使用具体关键词
避免过于宽泛的搜索词如"file"、"document"

# 2. 善用过滤器
使用文件类型过滤器缩小搜索范围
```

### 常见问题解决

#### 搜索结果太多
```
# 解决方案：
1. 添加更多关键词
2. 使用过滤器限制类型
3. 使用全匹配搜索
4. 指定路径范围
```

#### 搜索结果太少
```
# 解决方案：
1. 减少关键词数量
2. 检查拼写错误
3. 移除过滤器
```

#### 找不到已知文件
```
# 检查步骤：
1. 确认文件确实存在
2. 检查文件是否在索引范围内
3. 更新文件索引
4. 检查忽略列表设置
5. 尝试使用完整文件名
```

---

## 📚 搜索规则总结

### 语法快速参考

| 功能 | 语法 | 示例 |
|------|------|------|
| 基本搜索 | `关键词` | `document` |
| 多关键词 | `词1;词2` | `project;report` |
| 文件过滤 | `关键词|f` | `photo|f` |
| 目录过滤 | `关键词|d` | `project|d` |
| 全匹配 | `关键词|full` | `readme.txt|full` |
| 区分大小写 | `关键词|case` | `Test|case` |
| 正则表达式 | `模式|p` | `\d+\.txt|p` |
| 内容搜索 | `关键词|c` | `function|c` |
| 缓存搜索 | `:关键词` | `:report` |
| AI搜索 | `自然语言` | `"找工作报告"` |
| 插件搜索 | `>插件名` | `>calculator` |

### 最佳实践

#### 新手建议
1. **从简单开始**: 先尝试基本的关键词搜索
2. **逐步学习**: 掌握一种技巧后再学习下一种
3. **多加练习**: 在日常使用中练习各种搜索技巧
4. **记录模板**: 记录常用的搜索组合

#### 进阶技巧
1. **组合使用**: 灵活组合多种搜索技巧
2. **建立模板**: 为常见需求建立搜索模板
3. **优化索引**: 根据使用习惯优化索引设置
4. **使用AI**: 充分利用AI搜索的自然语言能力

