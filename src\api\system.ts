import { Command } from "@tauri-apps/plugin-shell";
import { invoke } from "@tauri-apps/api/core";

export const showItemInFolder = (path: string) => {
  return Command.create("showItemInFolder", ["/select,", path]).execute();
};

export const openTerminalInPath = (path: string) => {
  //todo for file open in path for folder open in folder
  const directoryPath = `${path.substring(0, path.lastIndexOf("\\"))}`;
  return Command.create("openTerminalAtPath", ["/d", directoryPath]).execute();
};

export const openFileWithoutAdmin = (path: string) => {
  return invoke<any>("start_new_program_without_admin", {
    path: path,
  });
};

export const openFileWithAdmin = (path: string) => {
  return invoke<any>("start_new_program_with_admin", {
    path: path,
  });
};