import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import enUS from "./locales/en-US";
import zhCN from "./locales/zh-CN";
import jaJP from "./locales/ja-JP";

const resources = {
  "en-US": enUS,
  "zh-CN": zhCN,
  "ja-JP": jaJP,
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    debug: true,
    resources,
    fallbackLng: "en-US",
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;
