import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { setInputValue, selectInputValue } from "../../slices/inputValueSlice";
import { setPluginInfo, selectPluginInfo } from "../../slices/pluginInfoSlice";
import {
  selectAvailableDataTypes,
  selectQueryData,
  selectSelectedCategory,
  setSelectedCategory,
  selectFlatMode,
  setFlatMode,
  selectSortBy,
  selectSortOrder,
  setSortSettings,
} from "../../slices/searchResultSlice";
import { listen } from "@tauri-apps/api/event";
import "../../index.css";
import logo from "../../assets/logo.png";
import { selectAIMode, setEnterKeyState } from "../../slices/aiModeSlice";
import { useTranslation } from "react-i18next";
import KeyboardReturnIcon from "@mui/icons-material/KeyboardReturn";
import { pluginExit } from "../../api/plugin";
import { getCurrentWindow } from "@tauri-apps/api/window";
import {
  applyWindowAcrylic,
  clearWindowAcrylic,
  getAsyncKeyState,
  getSwapButtonState,
} from "../../api/aiverything";
import { setWindowVibrancy } from "../../slices/windowVibrancySlice";
import { platform, version } from "@tauri-apps/plugin-os";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "../ui/dropdown-menu";
import { Button } from "../ui/button";

export const MainInput: React.FC = () => {
  const dispatch = useDispatch();
  const inputValue = useSelector(selectInputValue);
  const pluginInfo = useSelector(selectPluginInfo);
  const availableDataTypes = useSelector(selectAvailableDataTypes);
  const queryData = useSelector(selectQueryData);
  const selectedCategory = useSelector(selectSelectedCategory);
  const flatMode = useSelector(selectFlatMode);
  const sortBy = useSelector(selectSortBy);
  const sortOrder = useSelector(selectSortOrder);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const aiMode = useSelector(selectAIMode);
  const { t } = useTranslation();

  // shadcn dropdown 会自动处理打开/关闭状态，不需要手动管理

  // 添加插件退出处理函数
  const handlePluginExit = async (pluginIdentifier: string) => {
    try {
      await pluginExit(pluginIdentifier);
    } catch (error) {
      console.error("Plugin exit failed:", error);
    }
  };

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Alt") {
        event.preventDefault();
      }
      if (event.key === "Backspace") {
        if (!inputValue) {
          if (pluginInfo) {
            console.log("Exit plugin: ", pluginInfo.identifier);
            handlePluginExit(pluginInfo.identifier);
          }
          dispatch(setPluginInfo(null));
        }
      } else if (event.key === "ArrowUp" || event.key === "ArrowDown") {
        // 阻止上下箭头按键的默认行为
        event.preventDefault();
        return;
      }
    };
    window.addEventListener("keydown", handleKeyDown);

    // 清理函数
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [inputValue, pluginInfo]); // 在这里添加键盘事件监听

  // 在渲染进程中
  useEffect(() => {
    console.log(inputRef.current);
    const unlisten = listen("tauri://focus", () => {
      inputRef.current?.focus();
      const inputElement = inputRef.current;
      if (!inputElement) return;
      inputElement.selectionStart = 0;
      inputElement.selectionEnd = inputElement.value.length;
    });
    return () => {
      unlisten.then((f) => f());
    };
  }, []);

  const handleRightClick = (event: React.MouseEvent) => {
    event.preventDefault(); // 阻止默认右键菜单
    // 在这里添加自定义右键菜单逻辑
    console.log("右键菜单被触发");
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && aiMode) {
      event.preventDefault();
      dispatch(setEnterKeyState(true));
    }
  };

  const handleKeyUp = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && aiMode) {
      event.preventDefault();
      dispatch(setEnterKeyState(false));
    }
  };

  // 防抖函数
  const debounce = (func: () => void, delay: number) => {
    let timeout: NodeJS.Timeout;
    return () => {
      clearTimeout(timeout);
      timeout = setTimeout(func, delay);
    };
  };

  useEffect(() => {
    const appWindow = getCurrentWindow();
    // 监听窗口移动事件
    const unlisten = appWindow.onMoved(restoreWindowVibrancy);
    return () => {
      unlisten.then((f) => f());
    };
  }, []);

  const restoreWindowVibrancy = debounce(() => {
    applyWindowAcrylic().then(() => {
      dispatch(setWindowVibrancy(true));
    });
  }, 500);

  // 添加检查 Windows 版本的函数
  const hasAcrylicPerformanceIssueVersion = () => {
    const plat = platform();
    const ver = version();

    if (plat !== "windows") return false;

    // 解析版本号
    const [major, minor, build] = ver.split(".").map(Number);

    // https://learn.microsoft.com/en-us/windows/release-health/windows11-release-information
    // Windows 10 v1903+ (build 18362+) 到 Windows 10 最后一个版本
    const isWin10WithIssue =
      major === 10 && minor === 0 && build >= 18362 && build < 22000;

    // Windows 11 build 22000
    const isWin11WithIssue = major === 10 && minor === 0 && build === 22000;

    return isWin10WithIssue || isWin11WithIssue;
  };

  // 修改拖拽处理函数
  const handleDrag = async (e: React.MouseEvent) => {
    if (e.button === 0) {
      const hasPerformanceIssue = hasAcrylicPerformanceIssueVersion();
      if (hasPerformanceIssue) {
        dispatch(setWindowVibrancy(false));
        clearWindowAcrylic();
      }

      const appWindow = getCurrentWindow();
      await appWindow.startDragging();

      if (hasPerformanceIssue) {
        // 每100ms检查一次鼠标左键状态，如果为放开状态，则恢复窗口模糊
        const interval = setInterval(() => {
          getSwapButtonState().then((state) => {
            if (state) {
              getAsyncKeyState(0x02).then((mouseState) => {
                if (mouseState >= 0) {
                  restoreWindowVibrancy();
                  // 停止检查
                  clearInterval(interval);
                }
              });
            } else {
              getAsyncKeyState(0x01).then((mouseState) => {
                if (mouseState >= 0) {
                  restoreWindowVibrancy();
                  // 停止检查
                  clearInterval(interval);
                }
              });
            }
          });
        }, 100);
      }
    }
  };

  // 数据类型映射
  const dataTypeMapping = {
    Search: { label: t("dataTypes.Search") || "Search", icon: "🔍" },
    Shortcut: { label: t("dataTypes.Shortcut") || "Shortcut", icon: "⚡" },
    Apps: { label: t("dataTypes.Apps") || "Apps", icon: "🎯" },
    "UWP Apps": { label: t("dataTypes.UWP Apps") || "UWP Apps", icon: "📱" },
    Folders: { label: t("dataTypes.Folders") || "Folders", icon: "📁" },
    Documents: { label: t("dataTypes.Documents") || "Documents", icon: "📄" },
    Sheets: { label: t("dataTypes.Sheets") || "Sheets", icon: "📊" },
    Slides: { label: t("dataTypes.Slides") || "Slides", icon: "📈" },
    Pictures: { label: t("dataTypes.Pictures") || "Pictures", icon: "🖼️" },
    Videos: { label: t("dataTypes.Videos") || "Videos", icon: "🎬" },
    Audios: { label: t("dataTypes.Audios") || "Audios", icon: "🎵" },
    Developer: { label: t("dataTypes.Developer") || "Developer", icon: "💻" },
    Others: { label: t("dataTypes.Others") || "Others", icon: "📦" },
  };

  // 基于搜索结果动态生成文件分类选项
  const categoryOptions = React.useMemo(() => {
    if (availableDataTypes.length === 0) return [];

    const options = [
      { value: "all", label: t("category.all") || "All", icon: "📋" },
      ...availableDataTypes.map((dataType) => ({
        value: dataType,
        label: dataTypeMapping[dataType]?.label || dataType,
        icon: dataTypeMapping[dataType]?.icon || "📄",
      })),
    ];

    return options;
  }, [availableDataTypes]);

  // 基于搜索结果动态生成文件排序选项
  const sortOptions = React.useMemo(() => {
    const baseSortOptions = [
      {
        value: "relevance",
        label: t("searchBar.relevance"),
        icon: "🎯",
        canSort: false,
      },
    ];

    if (queryData.length > 0) {
      // 检查搜索结果中是否有文件数据，如果有则添加更多排序选项
      const hasFileData = queryData.some((item) =>
        item.data.some((file) => file.lastModified || file.size || file.name)
      );

      if (hasFileData) {
        baseSortOptions.push(
          {
            value: "lastModified",
            label: t("searchBar.lastModifiedTime"),
            icon: "🕐",
            canSort: true,
          },
          {
            value: "name",
            label: t("searchBar.name"),
            icon: "📝",
            canSort: true,
          },
          {
            value: "type",
            label: t("searchBar.type"),
            icon: "📂",
            canSort: true,
          },
          {
            value: "fileSize",
            label: t("searchBar.fileSize"),
            icon: "📊",
            canSort: true,
          }
        );
      }
    }

    return baseSortOptions;
  }, [queryData]);

  const handleCategorySelect = (value: string) => {
    console.log("Selected category:", value);
    dispatch(setSelectedCategory(value));

    // 触发滚动到对应的section并选择第一个文件
    if (value !== "all") {
      // 找到对应的section索引
      const sectionIndex = queryData.findIndex(
        (section) => section.datatype === value
      );
      if (sectionIndex !== -1) {
        // 派发一个自定义事件，让FileList组件监听并滚动到对应section且选择第一个文件
        const event = new CustomEvent("scrollToSection", {
          detail: { sectionIndex, selectFirstFile: true },
        });
        window.dispatchEvent(event);
      }
    } else {
      // 如果选择"all"，找到第一个非Search类型的section
      let targetSectionIndex = 0;
      for (let i = 0; i < queryData.length; i++) {
        if (
          queryData[i].datatype !== "Search" &&
          queryData[i].data.length > 0
        ) {
          targetSectionIndex = i;
          break;
        }
      }

      const event = new CustomEvent("scrollToSection", {
        detail: { sectionIndex: targetSectionIndex, selectFirstFile: true },
      });
      window.dispatchEvent(event);
    }
  };

  // 处理分组模式切换
  const handleGroupingToggle = () => {
    const newFlatMode = !flatMode;
    dispatch(setFlatMode(newFlatMode));

    // 如果从平铺模式切换到分组模式，设置默认选中分类为"all"
    if (!newFlatMode) {
      dispatch(setSelectedCategory("all"));
    }
  };

  const handleSortSelect = (value: string) => {
    if (value === "relevance") {
      // 相关性排序始终是默认状态
      dispatch(setSortSettings({ sortBy: "relevance", sortOrder: "asc" }));
    } else {
      // 其他排序类型的状态轮转
      if (sortBy === value) {
        if (sortOrder === "desc") {
          // 当前是倒序，切换到正序
          dispatch(setSortSettings({ sortBy: value, sortOrder: "asc" }));
        } else {
          // 当前是正序，取消排序（回到相关性）
          dispatch(setSortSettings({ sortBy: "relevance", sortOrder: "asc" }));
        }
      } else {
        // 不是当前排序，设置为倒序（第一次点击）
        dispatch(setSortSettings({ sortBy: value, sortOrder: "desc" }));
      }
    }
  };

  // 当选项变化时，确保选中的值是有效的（只在分组模式下）
  React.useEffect(() => {
    if (!flatMode && categoryOptions.length > 0) {
      const isValidCategory = categoryOptions.some(
        (option) => option.value === selectedCategory
      );
      if (!isValidCategory) {
        dispatch(setSelectedCategory("all"));
      }
    }
  }, [categoryOptions, selectedCategory, flatMode]);

  React.useEffect(() => {
    if (sortOptions.length > 0) {
      const isValidSort = sortOptions.some((option) => option.value === sortBy);
      if (!isValidSort) {
        dispatch(setSortSettings({ sortBy: "relevance", sortOrder: "asc" }));
      }
    }
  }, [sortOptions, sortBy]);

  // 使用 shadcn DropdownMenu 自动处理 Portal

  let element;
  if (pluginInfo !== null) {
    element = (
      <div className="w-full h-full relative">
        <div
          className="absolute flex justify-center items-center w-12 h-full z-10 cursor-move"
          onMouseDown={handleDrag}
        >
          <img
            className={"absolute w-12 h-12 px-2 py-2"}
            src={pluginInfo.icon}
            alt="plugin icon"
          />
        </div>

        <input
          style={{
            background: "transparent",
          }}
          autoFocus
          ref={inputRef}
          value={inputValue}
          type="text"
          className={`pl-12 w-full h-full bg-main-query border-main-query-border shadow-mq-shadow focus:outline-none border-2 rounded-md text-lg px-4 py-1`}
          placeholder={t("searchBar.searchPlaceholder")}
          onChange={(e) => dispatch(setInputValue(e.target.value))}
        />
      </div>
    );
  } else {
    element = (
      <div className="w-full h-full relative">
        <div
          className="absolute flex justify-center items-center w-12 h-full z-10 cursor-move"
          onMouseDown={handleDrag}
        >
          <img className={"w-12 h-12 px-2 py-2"} src={logo} alt="plugin icon" />
        </div>

        <input
          style={{
            background: "transparent",
          }}
          autoFocus
          ref={inputRef}
          value={inputValue}
          type="text"
          className={`pl-12 w-full h-full bg-main-query border-main-query-border shadow-mq-shadow focus:outline-none border-2 rounded-md text-lg px-4 py-1 ${
            aiMode ? "border-blue-500" : ""
          } ${
            // 动态调整右边距基于显示的按钮数量
            (() => {
              const showCategory = categoryOptions.length > 0;
              const showSort = sortOptions.length > 1;
              const buttonCount = (showCategory ? 1 : 0) + (showSort ? 1 : 0);

              if (buttonCount === 2) {
                return aiMode ? "pr-32" : "pr-16";
              } else if (buttonCount === 1) {
                return aiMode ? "pr-24" : "pr-10";
              } else {
                return aiMode ? "pr-16" : "";
              }
            })()
          }`}
          placeholder={
            aiMode
              ? t("searchBar.aiSearchPlaceholder")
              : t("searchBar.searchPlaceholder")
          }
          onChange={(e) => dispatch(setInputValue(e.target.value))}
          onKeyDown={handleKeyDown}
          onKeyUp={handleKeyUp}
        />

        {/* 右侧按钮容器 */}
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
          {/* 文件分类按钮 - 只在有搜索结果时显示 */}
          {categoryOptions.length > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 p-0 hover:bg-transparent"
                >
                  <svg
                    width="25"
                    height="25"
                    viewBox="0 0 25 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12.1569 3.92157V20.9216C12.1569 21.1868 12.0515 21.4411 11.864 21.6287C11.6764 21.8162 11.4221 21.9216 11.1569 21.9216H5.15686C4.62643 21.9216 4.11772 21.7109 3.74265 21.3358C3.36757 20.9607 3.15686 20.452 3.15686 19.9216V5.92157C3.15686 5.39114 3.36757 4.88243 3.74265 4.50736C4.11772 4.13228 4.62643 3.92157 5.15686 3.92157H19.1569C19.6873 3.92157 20.196 4.13228 20.5711 4.50736C20.9461 4.88243 21.1569 5.39114 21.1569 5.92157V11.9216C21.1569 12.1868 21.0515 12.4411 20.864 12.6287C20.6764 12.8162 20.4221 12.9216 20.1569 12.9216H3.15686M16.1569 19.9216L18.1569 21.9216L22.1569 17.9216"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {/* 分组开关 */}
                <DropdownMenuCheckboxItem
                  checked={!flatMode}
                  onCheckedChange={handleGroupingToggle}
                  className="font-medium"
                >
                  {t("searchBar.categorizeByType")}
                </DropdownMenuCheckboxItem>

                {/* 只有在开启分组时才显示类型选项 */}
                {!flatMode && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuLabel className="text-xs text-muted-foreground">
                      {t("searchBar.quickDirectToType")}
                    </DropdownMenuLabel>
                    <DropdownMenuRadioGroup
                      value={selectedCategory}
                      onValueChange={handleCategorySelect}
                    >
                      {categoryOptions.map((option) => (
                        <DropdownMenuRadioItem
                          key={option.value}
                          value={option.value}
                          className="cursor-pointer"
                        >
                          <span className="mr-2">{option.icon}</span>
                          {option.label}
                        </DropdownMenuRadioItem>
                      ))}
                    </DropdownMenuRadioGroup>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* 文件排序按钮 - 只在有搜索结果时显示 */}
          {sortOptions.length > 1 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 p-0 hover:bg-transparent"
                >
                  <svg
                    width="25"
                    height="25"
                    viewBox="0 0 25 25"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.1569 12.9216H21.1569M10.1569 18.9216H21.1569M10.1569 6.92157H21.1569M4.15686 10.9216H6.15686M4.15686 6.92157H5.15686V10.9216M6.15686 18.9217H4.15686C4.15686 17.9217 6.15686 16.9217 6.15686 15.9217C6.15686 14.9217 5.15686 14.4217 4.15686 14.9217"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel className="text-xs text-muted-foreground">
                  {t("searchBar.rankBy")}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuRadioGroup
                  value={sortBy}
                  onValueChange={handleSortSelect}
                >
                  {sortOptions.map((option) => {
                    const isCurrentSort = sortBy === option.value;
                    const getSortIndicator = () => {
                      if (option.value === "relevance") {
                        return null;
                      }
                      if (isCurrentSort) {
                        return sortOrder === "asc" ? "↑" : "↓";
                      }
                      return "↕";
                    };

                    return (
                      <DropdownMenuRadioItem
                        key={option.value}
                        value={option.value}
                        className="cursor-pointer"
                      >
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center">
                            <span className="mr-2">{option.icon}</span>
                            <span>{option.label}</span>
                            {getSortIndicator() && (
                              <span className="ml-2 text-xs text-muted-foreground">
                                {getSortIndicator()}
                              </span>
                            )}
                          </div>
                        </div>
                      </DropdownMenuRadioItem>
                    );
                  })}
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* AI模式提示 */}
          {aiMode && (
            <div className="flex items-center text-gray-400 ml-2">
              <KeyboardReturnIcon fontSize="small" />
              <span className="text-sm ml-1">
                {t("searchBar.pressEnterToSearch")}
              </span>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <>
      <div
        className="sticky top-0 h-14 rounded-md"
        style={{
          backgroundColor: "transparent",
        }}
        onContextMenu={handleRightClick} // 添加右键菜单事件处理
      >
        {element}
      </div>

      {/* shadcn DropdownMenu 组件已经在上面渲染 */}
    </>
  );
};
