# Plugin-Service 架构介绍

## 概述

Plugin-Service 是一个基于 Spring Boot 的独立插件运行时服务，专门负责插件的生命周期管理、AI模型集成和插件间通信。该服务作为插件系统的核心组件，提供了完整的插件运行环境和丰富的API接口。

## 技术栈

- **框架**: Spring Boot 3.x
- **JDK**: JDK 21
- **AI集成**: Ollama LLM + MCP协议
- **构建工具**: Maven
- **日志**: SLF4J + Logback
- **序列化**: Jackson + Gson
- **线程池**: 自定义线程池管理

## 核心架构

### 分层架构图

```
┌─────────────────────────────────────────────────────────────┐
│                      REST API层                             │
│              PluginController                               │
├─────────────────────────────────────────────────────────────┤
│                    插件管理层                               │
│        ┌─────────────────────────────────────┐             │
│        │         PluginLoader                │             │
│        │  ┌─────────────┐ ┌─────────────┐    │             │
│        │  │插件生命周期  │ │消息处理器    │    │             │
│        │  │管理器       │ │MessageHandler│    │             │
│        │  └─────────────┘ └─────────────┘    │             │
│        └─────────────────────────────────────┘             │
├─────────────────────────────────────────────────────────────┤
│                    AI集成层                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │LLMInterface │ │LLMFactory   │ │MCPUtils     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│         │               │               │                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │OllamaLLM    │ │其他LLM实现   │ │MCP协议支持   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                   插件运行时                                │
│    ┌─────────────────────────────────────────────────────┐ │
│    │              插件容器                               │ │
│    │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐   │ │
│    │  │插件A     │ │插件B     │ │插件C     │ │插件D     │   │ │
│    │  │ClassLoader││ClassLoader││ClassLoader││ClassLoader││ │
│    │  └─────────┘ └─────────┘ └─────────┘ └─────────┘   │ │
│    └─────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   工具和配置层                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │I18nUtil     │ │ThreadPoolUtil│ │DebugUtil    │           │
│  │国际化工具    │ │线程池管理    │ │调试工具      │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                   存储层                                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │插件JAR文件   │ │插件配置文件  │ │聊天历史文件  │           │
│  │plugins/     │ │plugin configs/│ │chat/       │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件详解

### 1. PluginLoader (插件加载器)
**职责**: 插件生命周期管理的核心组件
- **插件扫描**: 自动扫描plugins目录下的JAR文件
- **动态加载**: 使用独立ClassLoader加载插件类
- **配置管理**: 处理插件的settings.json配置文件
- **消息处理**: 处理插件间的异步消息通信
- **热插拔**: 支持插件的动态加载和卸载

**关键方法**:
- `loadAllPlugins()`: 加载所有插件
- `loadJarPlugin()`: 加载单个JAR插件
- `unloadPlugin()`: 卸载插件
- `handleMessages()`: 处理插件消息队列

### 2. PluginController (插件控制器)
**职责**: 提供插件管理的REST API接口
- **插件列表**: 获取所有已加载插件信息
- **插件状态**: 查看插件运行状态和错误信息
- **配置管理**: 读取和修改插件配置
- **资源访问**: 访问插件内部资源文件
- **热部署**: 支持插件的在线安装和卸载

**主要端点**:
- `GET /plugins`: 获取插件列表
- `GET /pluginInfo`: 获取插件详细信息
- `GET /pluginConfig`: 获取插件配置
- `PUT /setPluginConfig`: 修改插件配置

### 3. LLM集成系统

#### LLMInterface (LLM接口)
**职责**: 定义AI模型的统一接口
```java
public interface LLMInterface {
    String OLLAMA = "ollama";
    String NONE = "none";
    
    Optional<OllamaChatResponseModel> chat(String sessionId, String message);
    String newSession();
    void removeSession(String sessionId);
    Optional<OllamaToolsResult> generateWithTools(String message);
    // 更多AI相关方法...
}
```

#### OllamaLLM (Ollama实现)
**职责**: Ollama大语言模型的具体实现
- **会话管理**: 多会话聊天支持
- **流式响应**: 实时流式文本生成
- **工具调用**: 支持函数调用和工具使用
- **图像处理**: 多模态图像理解
- **模型配置**: 动态模型参数配置

#### MCPUtils (MCP工具)
**职责**: 模型控制协议(MCP)支持
- **协议实现**: 实现MCP标准协议
- **工具注册**: 为AI注册可调用的工具函数
- **上下文管理**: 管理MCP会话上下文

### 4. 插件运行时环境

#### PluginDelegate (插件代理)
**职责**: 封装插件实例，提供统一的插件操作接口
```java
public class PluginDelegate {
    // 插件基本信息
    public final String filePath;
    public final String name;
    public final String identifier;
    public final String version;
    
    // 运行时组件
    public final Object instance;
    public final Class<?> clazz;
    public final URLClassLoader urlClassLoader;
    
    // 生命周期方法
    public void load(Map<String, Object> configs);
    public void afterLoaded();
    public void unload();
    // ...
}
```

#### 消息系统
**职责**: 插件间异步通信机制
- **消息队列**: 每个插件都有独立的消息队列
- **消息类型**: 支持多种消息类型(命令注册、配置修改、AI聊天等)
- **异步处理**: 所有消息都是异步处理，不阻塞主线程

### 5. 配置管理系统

#### ConfigEntity (配置实体)
**职责**: 结构化的插件配置管理
```java
public class ConfigEntity {
    private String title;
    private Map<String, ConfigComponent> config;
    
    public static class ConfigComponent {
        private String type;           // 配置类型
        private String description;    // 配置描述
        private Object defaultValue;   // 默认值
        private Object value;          // 当前值
        private List<Object> enumValues;      // 枚举选项
        private List<String> enumDescription; // 枚举描述
    }
}
```

**支持的配置类型**:
- `boolean`: 布尔值
- `string`: 字符串
- `number`: 数字
- `object`: 嵌套对象
- `array`: 数组类型

### 6. 工具类库

#### I18nUtil (国际化工具)
- **多语言支持**: 支持插件的多语言配置
- **动态翻译**: 根据用户语言环境动态翻译文本
- **资源管理**: 管理插件的国际化资源文件

#### ThreadPoolUtil (线程池工具)
- **任务调度**: 管理插件的异步任务执行
- **资源控制**: 控制线程池大小，防止资源耗尽
- **优雅关闭**: 应用关闭时优雅关闭所有任务

#### DebugUtil (调试工具)
- **运行时监控**: 监控插件运行状态
- **性能分析**: 分析插件性能指标
- **错误诊断**: 帮助诊断插件问题

## 启动流程

1. **初始化阶段**
   - 清理待删除的插件JAR文件
   - 初始化LLM配置和连接
   - 创建插件配置目录

2. **插件加载阶段**
   - 扫描plugins目录
   - 解析plugin.json描述文件
   - 验证API版本兼容性
   - 创建独立ClassLoader
   - 实例化插件类

3. **配置初始化阶段**
   - 加载settings.json配置
   - 同步配置结构
   - 创建配置实体
   - 调用插件load()方法

4. **后置处理阶段**
   - 启动消息处理循环
   - 等待所有插件加载完成
   - 调用插件afterLoaded()方法
