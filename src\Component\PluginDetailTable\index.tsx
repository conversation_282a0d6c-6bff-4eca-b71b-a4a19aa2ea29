import {
  PickaxeIcon,
  BookTextIcon,
  BlocksIcon,
  GitForkIcon,
} from "lucide-react";
import { useTranslation } from "react-i18next";

const PluginDetailTable = ({ plugin }) => {
  const { t } = useTranslation();

  const pluginDetails = [
    {
      icon: <BlocksIcon className="w-4 h-4 mr-2" />,
      label: t("searchBar.plugin.identifier"),
      value: plugin.identifier,
      title: plugin.identifier,
    },
    {
      icon: <GitForkIcon className="w-4 h-4 mr-2" />,
      label: t("searchBar.plugin.version"),
      value: plugin.version,
      title: plugin.version,
    },
    {
      icon: <PickaxeIcon className="w-4 h-4 mr-2" />,
      label: t("searchBar.plugin.author"),
      value: plugin.author,
      title: plugin.author,
    },
    {
      icon: <BookTextIcon className="w-4 h-4 mr-2" />,
      label: t("searchBar.plugin.description"),
      value: plugin.detailedDescription
        ? plugin.detailedDescription
        : plugin.description,
      title: plugin.detailedDescription
        ? plugin.detailedDescription
        : plugin.description,
    },
  ];

  return (
    <div className="w-full max-w-md mx-auto bg-gray-100 rounded-lg shadow-inner overflow-hidden dark:bg-gray-800">
      {pluginDetails.map((detail, index) => {
        const isDescription =
          detail.label === t("searchBar.plugin.description");

        return (
          <div key={index} className="flex p-2 bg-gray-100 dark:bg-gray-800">
            <div className="flex items-center text-xs text-gray-600 dark:text-gray-400 w-1/2">
              {detail.icon}
              <span className="truncate max-w-xs" title={detail.title}>
                {detail.label}
              </span>
            </div>
            <div className="text-xs text-right text-gray-800 dark:text-gray-200 w-1/2">
              {isDescription ? (
                <div
                  className="whitespace-pre-wrap break-words"
                  title={detail.title}
                >
                  {detail.value}
                </div>
              ) : (
                <div className="truncate max-w-xs" title={detail.title}>
                  {detail.value}
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PluginDetailTable;
