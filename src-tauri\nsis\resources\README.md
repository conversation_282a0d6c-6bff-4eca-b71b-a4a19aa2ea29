# NSIS 安装包美化资源说明

## 目录结构

```
src-tauri/nsis/resources/
├── images/                    # 图片资源
│   ├── sidebar.bmp           # 侧边栏图片 (164x314 像素)
│   ├── header.bmp            # 头部图片 (150x57 像素)
│   ├── slides/               # 轮播图片
│   │   ├── slide1.bmp        # 第一张轮播图
│   │   ├── slide2.bmp        # 第二张轮播图
│   │   └── slide3.bmp        # 第三张轮播图
│   └── installer.ico         # 安装程序图标
└── README.md                 # 此文件
```

## 美化功能说明

### 1. 现代化UI主题
- 启用Windows现代UI主题
- 圆角窗口效果（Windows 11）
- 窗口阴影效果
- 自定义颜色方案

### 2. 轮播图功能
- 产品功能介绍轮播
- 自动切换（4秒间隔）
- 手动导航按钮
- 支持emoji图标和格式化文本

### 3. 动画效果
- 欢迎页面淡入效果
- 安装过程进度条动画
- 完成页面庆祝效果
- 窗口闪烁提示

## 自定义资源

### 图片资源
如需使用自定义图片，请将图片放在对应目录并确保：
- 侧边栏图片：164x314像素，BMP格式
- 头部图片：150x57像素，BMP格式
- 轮播图片：推荐尺寸适中的BMP格式

### 声音资源
如需使用自定义音效，请将WAV格式的音频文件放在sounds目录。

## 配置说明

在 `tauri.conf.json` 中配置：

```json
{
  "tauri": {
    "bundle": {
      "windows": {
        "nsis": {
          "installerIcon": "icons/installer.ico",
          "headerImage": "nsis/resources/images/header.bmp",
          "sidebarImage": "nsis/resources/images/sidebar.bmp",
          "template": "nsis/installer.nsi"
        }
      }
    }
  }
}
```

## 功能特色

1. **智能跳过**：在被动模式(-p)和静默模式(-s)下自动跳过美化效果
2. **性能优化**：轻量级动画，不影响安装速度
3. **兼容性**：支持Windows 7及以上版本
4. **响应式**：适配不同屏幕DPI设置

## 注意事项

1. 美化功能只在GUI模式下生效
2. 声音播放使用系统内置音效，确保兼容性
3. 动画效果可能在低端设备上表现不佳
4. 圆角窗口功能需要Windows 11支持

## 构建说明

使用以下命令构建带美化的安装包：

```bash
npm run tauri build
```

构建完成后，生成的安装包将包含所有美化功能。 