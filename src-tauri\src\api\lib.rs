use reqwest::Client;
use reqwest::Method;
use serde_json::Value;

pub struct Aiverything<PERSON>pi {
    base_url: String,
    client: Client,
}

impl AiverythingApi {
    pub fn new(base_url: &str) -> Result<Self, reqwest::Error> {
        let client = Client::builder()
            .danger_accept_invalid_certs(true) // Ignore HTTPS errors
            .build()?;
        Ok(Self {
            base_url: base_url.to_string(),
            client,
        })
    }

    // Helper method to construct full URL
    fn url(&self, endpoint: &str) -> String {
        format!("{}{}", self.base_url, endpoint)
    }

    pub async fn status(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::GET, self.url("/status"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn prepare_search(
        &self,
        search_text: &str,
        max_result_num: i32,
    ) -> Result<Value, reqwest::Error> {
        let params = [
            ("searchText", search_text),
            ("maxResultNum", &max_result_num.to_string()),
        ];
        let response = self
            .client
            .request(Method::POST, self.url("/prepareSearch"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn search_async(
        &self,
        search_text: &str,
        max_result_num: i32,
    ) -> Result<Value, reqwest::Error> {
        let params = [
            ("searchText", search_text),
            ("maxResultNum", &max_result_num.to_string()),
        ];
        let response = self
            .client
            .request(Method::POST, self.url("/searchAsync"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn remove_summary(&self, session_id: &str) -> Result<Value, reqwest::Error> {
        let params = [("sessionId", session_id)];
        let response = self
            .client
            .request(Method::DELETE, self.url("/summarizeAI"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_summary(&self, session_id: &str) -> Result<Value, reqwest::Error> {
        let params = [("sessionId", session_id)];
        let response = self
            .client
            .request(Method::GET, self.url("/summarizeAI"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn summarize_ai(&self, file: &str) -> Result<Value, reqwest::Error> {
        let params = [("file", file)];
        let response = self
            .client
            .request(Method::POST, self.url("/summarizeAI"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn search_ai(
        &self,
        search_text: &str,
        max_result_num: i32,
    ) -> Result<Value, reqwest::Error> {
        let params = [("maxResultNum", &max_result_num.to_string())];
        let response = self
            .client
            .request(Method::POST, self.url("/searchAI"))
            .query(&params)
            .body(search_text.to_owned())
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn stop_search(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::DELETE, self.url("/search"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_result(&self, uuid: &str) -> Result<Value, reqwest::Error> {
        let params = [("uuid", uuid)];
        let response = self
            .client
            .request(Method::GET, self.url("/result"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_uwp_result(&self, uuid: &str) -> Result<Value, reqwest::Error> {
        let params = [("uuid", uuid)];
        let response = self
            .client
            .request(Method::GET, self.url("/uwpResult"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn remove_result(&self, uuid: &str) -> Result<Value, reqwest::Error> {
        let params = [("uuid", uuid)];
        let response = self
            .client
            .request(Method::DELETE, self.url("/result"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn version(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::GET, self.url("/version"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn build_version(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::GET, self.url("/build_version"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn flush_file_changes(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::POST, self.url("/flushFileChanges"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn clear_suffix_priority(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::DELETE, self.url("/clearSuffixPriority"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_suffix_priority(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::GET, self.url("/suffixPriority"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn add_suffix_priority(
        &self,
        suffix: &str,
        priority: i32,
    ) -> Result<Value, reqwest::Error> {
        let params = [("suffix", suffix), ("priority", &priority.to_string())];
        let response = self
            .client
            .request(Method::POST, self.url("/suffixPriority"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn delete_suffix_priority(&self, suffix: &str) -> Result<Value, reqwest::Error> {
        let params = [("suffix", suffix)];
        let response = self
            .client
            .request(Method::DELETE, self.url("/suffixPriority"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn modify_suffix_priority(
        &self,
        old_suffix: &str,
        new_suffix: &str,
        priority: i32,
    ) -> Result<Value, reqwest::Error> {
        let params = [
            ("oldSuffix", old_suffix),
            ("newSuffix", new_suffix),
            ("priority", &priority.to_string()),
        ];
        let response = self
            .client
            .request(Method::PUT, self.url("/suffixPriority"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn add_cache(&self, path: &str) -> Result<Value, reqwest::Error> {
        let params = [("path", path)];
        let response = self
            .client
            .request(Method::POST, self.url("/cache"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_cache(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::GET, self.url("/cache"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn delete_cache(&self, path: &str) -> Result<Value, reqwest::Error> {
        let params = [("path", path)];
        let response = self
            .client
            .request(Method::DELETE, self.url("/cache"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_frequent_result(
        &self,
        num: i32,
        search_text: String,
    ) -> Result<Value, reqwest::Error> {
        let num_str = num.to_string();
        let params = [
            ("num", num_str.as_str()),
            ("searchText", search_text.as_str()),
        ];
        let response = self
            .client
            .request(Method::GET, self.url("/frequentResult"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_icon(&self, path: &str, is_uwp: bool) -> Result<Value, reqwest::Error> {
        let params = [("path", path), ("isUwp", &is_uwp.to_string())];
        let response = self
            .client
            .request(Method::GET, self.url("/icon"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn update(&self, is_drop_previous: bool) -> Result<Value, reqwest::Error> {
        let params = [("isDropPrevious", &is_drop_previous.to_string())];
        let response = self
            .client
            .request(Method::POST, self.url("/update"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn close(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::POST, self.url("/close"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_disk(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::GET, self.url("/disk"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_gpu(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::GET, self.url("/gpu"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_config(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::GET, self.url("/config"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn set_config(&self, config: &Value) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::POST, self.url("/config"))
            .json(config)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn run_uwp(&self, app_user_model_id: &str) -> Result<Value, reqwest::Error> {
        let params = [("appUserModelId", app_user_model_id)];
        let response = self
            .client
            .request(Method::POST, self.url("/runUwp"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }
}
