import React, { useState, useEffect, useRef } from "react";
import { getIcon } from "../../api/aiverything";
import { cropImage } from "../../utils/cropImage";
const useIntersectionObserver = (callback) => {
  const observer = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    observer.current = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          callback(entry.target);
        }
      });
    });

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [callback]);

  const observe = (element) => {
    if (element && observer.current) {
      // 延迟观察
      setTimeout(() => {
        if (observer.current) {
          observer.current.observe(element);
        }
      }, 100);
    }
  };

  return observe;
};

const FileIcon = ({ filepath, target, isUwp = false }) => {
  const [iconSrc, setIconSrc] = useState("");
  const elementRef = useRef(null);
  const observe = useIntersectionObserver((element) => {
    const fetchIcon = async () => {
      try {
        const response = await getIcon(filepath, isUwp);
        const croppedImage = await cropImage(response.data);
        setIconSrc(croppedImage);
      } catch (error) {
        console.error("Error fetching icon:", error);
      }
    };
    fetchIcon();
  });

  useEffect(() => {
    if (elementRef.current) {
      observe(elementRef.current);
    }
  }, [observe]);

  let sizeClass;
  if (target === "Detail") {
    sizeClass = "w-64 h-32 mb-4 mt-8";
  } else if (target == "Compact") {
    sizeClass = "";
  } else {
    sizeClass = "w-12 h-12 mr-4";
  }

  return (
    <div ref={elementRef} className={`${sizeClass}`}>
      {iconSrc ? (
        <img src={iconSrc} alt="" className="w-full h-full object-contain" />
      ) : (
        <div className="bg-gray-200"></div>
      )}
    </div>
  );
};

export default FileIcon;
