# Aiverything AI 搜索功能用户指南

## 📋 目录
- [功能概述](#功能概述)
- [启用AI搜索](#启用ai搜索)
- [AI搜索特性](#ai搜索特性)
- [使用方法](#使用方法)
- [AI文件摘要](#ai文件摘要)
- [配置设置](#配置设置)
- [使用技巧](#使用技巧)
- [常见问题](#常见问题)
- [最佳实践](#最佳实践)

---

## 🌟 功能概述

**AI 搜索功能** 将先进的人工智能技术与传统文件搜索相结合，为用户提供更智能、更直观的文件查找体验。通过集成大语言模型，AI 搜索能够理解自然语言查询，分析文件内容，并提供个性化的搜索结果。

### 核心优势

- 🧠 **自然语言理解** - 支持用人类语言描述搜索需求
- 📊 **智能内容分析** - 深度理解文件内容和上下文
- 📝 **文档摘要生成** - 可生成文件内容摘要

---

## 🚀 启用AI搜索

### 界面切换

![enable ai](./pictures/enable%20ai.png)

1. **打开搜索窗口**
   - 使用快捷键 `Ctrl + Shift + Alt + A` 打开搜索界面
   - 或双击 `Ctrl` 键快速打开（需在设置中手动开启）

2. **启用AI模式**
   - 点击搜索框右侧的 **AI 图标** 
   - 图标变亮表示已启用AI搜索模式

### 模式识别

启用AI搜索后，界面会有以下变化：
- AI图标高亮显示
- 搜索提示文字更新为"使用AI搜索..."

---

## 🤖 AI搜索特性

### 1. 自然语言理解

AI搜索最大的特点是能够理解方向性描述，用户只需要说明自己的需求或课题方向，AI会自动联想相关关键词并执行搜索。

#### AI搜索工作原理

1. **接收方向描述**：用户输入需求或研究方向
2. **智能关键词联想**：AI根据描述联想出相关的关键词和概念
3. **执行搜索匹配**：使用联想出的关键词在文件库中搜索
4. **结果智能排序**：按相关度对搜索结果进行排序

例如：
- 输入："帮我找一下机器学习算法相关的文档" 
- AI联想：machine learning, 算法, 深度学习, neural network, AI, 模型训练等关键词
- 搜索匹配：查找包含这些关键词的文档

### 2. 智能文件分析

AI搜索不仅仅匹配文件名，还会分析：
- **文件内容**：理解文档实际包含的信息
- **文件类型**：识别文件的用途和性质

### 3. 个性化推荐

系统会学习用户的搜索和使用习惯，提供个性化的搜索结果：
- 经常访问的文件优先显示
- 相关性更高的文件排名靠前

---

## 📖 使用方法

### 基础使用

1. **启用AI模式**（按照上述步骤）

2. **输入方向性描述**
   ```
   示例查询：
   - "找一下消费记录的Excel表格"
   - "帮我找Transformer相关的文档"
   ```

3. **查看智能结果**
   - AI会根据您的方向描述联想相关关键词
   - 自动搜索包含这些关键词的文件
   - 返回最相关的文件列表，按相关性排序

### 搜索结果操作

AI搜索的结果支持与传统搜索相同的操作：
- **Enter** - 打开文件
- **Ctrl + Enter** - 以管理员权限打开
- **Alt + Enter** - 复制文件路径
- **Shift + Enter** - 打开文件所在文件夹

---

## 📄 AI文件摘要

### 功能介绍

当您选中搜索结果中的文件时，右侧详情面板会显示AI生成的文件摘要。

![ai summary](./pictures/ai%20summary.png)

### 摘要内容

AI摘要通常包含：
- **文件概述**：文档的主要内容和用途
- **关键信息**：重要数据、日期、人员等
- **文件结构**：章节、标题、主要部分

### 支持的文件格式

- **文档类型**：PDF、Word (.doc/.docx)
- **表格类型**：Excel (.xls/.xlsx)、CSV
- **文本类型**：TXT、RTF、Markdown (.md)
- **其他格式**：HTML、XML（部分支持）

### 摘要生成过程

![summary page](./pictures/summary%20page.png)

1. 选择文件后，AI开始分析文档内容
2. 显示"正在生成摘要..."的提示
3. 几秒钟后显示完整的文件摘要

---

## ⚙️ 配置设置

### AI功能设置

![ai settings](./pictures/ai%20settings.png)

进入设置界面：`设置 → 高级设置 → 大语言模型`

#### 主要配置项

1. **Ollama配置**
   - 配置本地Ollama服务
   - 检查连接状态

2. **AI模型选择**
   - 选择使用的语言模型

## 💡 使用技巧

### 提高AI搜索效果

**使用方向性描述**
   ```
   好的查询：找一下关于2024年第一季度销售数据的Excel表格
   一般查询：找Excel文件
   
   AI会根据"分析公司第一季度业务表现"这个方向，
   联想到：销售、营收、报表、数据、Q1、第一季度等关键词
   ```

### 利用AI摘要功能

1. **快速预览文档内容**
   - 无需打开文件即可了解内容
   - 节省时间和系统资源

2. **文档管理决策**
   - 根据摘要判断文件重要性
   - 快速识别重复或过时文档

---

## ❓ 常见问题

### AI搜索无响应

**Q: 启用AI模式后搜索没有结果？**

A: 排查步骤
1. **验证配置**
   - 检查 `设置 → 高级设置 → 大语言模型` 配置
   - 确认Ollama服务运行正常

2. **重启服务**
   - 重启Aiverything应用
   - 重启Ollama服务

### AI摘要生成失败

**Q: 选择文件后无法生成摘要？**

A: 可能原因和解决方案
1. **文件格式不支持**
   - 确认文件格式在支持列表中
   - 尝试转换文件格式

2. **文件损坏或加密**
   - 检查文件是否正常打开
   - 加密文件需要先解密

3. **AI服务超时**
   - 大文件处理时间较长，请耐心等待

### 搜索结果不准确

**Q: AI搜索结果与预期不符？**

A: 改进建议
1. **优化查询描述**
   - 使用更具体的描述

2. **调整AI模型**
   - 尝试不同的AI模型
   - 调整模型参数

3. **反馈学习**
   - 系统会学习您的使用习惯
   - 使用越多，准确性越高

---

## 🎯 最佳实践

### 查询语句建议

#### 优秀的方向性查询示例
```
✅ "搜索人工智能在金融领域的应用相关文档"
✅ "找一下客户满意度调研的结果文件"
✅ "搜索下React前端开发框架学习文档"
```

#### 需要改进的查询示例
```
❌ "文件"（太宽泛，没有方向性）
❌ "找东西"（没有明确的主题或方向）
❌ "重要的"（方向不明确）
❌ "最新的"（缺少具体领域或主题）
```

## 📞 获取帮助

如果您在使用AI搜索功能时遇到问题：

1. **查看本文档**：首先参考本指南的故障排除部分
2. **官方社区**：访问 [GitHub Issues](https://github.com/panwangwin/aiverything-official-forum/issues)
3. **QQ交流群**：加入官方QQ群 893463594
4. **官方网站**：访问 https://aiverything.me/ 获取最新信息

---

*感谢您使用 Aiverything AI 搜索功能！我们将持续改进和优化，为您提供更好的智能搜索体验。* 