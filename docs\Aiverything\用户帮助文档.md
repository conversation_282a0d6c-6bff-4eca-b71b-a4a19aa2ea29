# Aiverything 用户帮助文档

## 📋 目录
- [产品介绍](#产品介绍)
- [安装和启动](#安装和启动)
- [基础使用](#基础使用)
- [高级搜索功能](#高级搜索功能)
- [AI 搜索功能](#ai-搜索功能)
- [插件系统](#插件系统)
- [快捷键操作](#快捷键操作)
- [设置配置](#设置配置)
- [问题排查](#问题排查)
- [联系我们](#联系我们)

---

## 🌟 产品介绍

**Aiverything** 是一款基于 GPU 加速的高效文件搜索工具，专为 Windows 系统设计。它结合了 **GPU 并行计算**、**智能索引**、**AI 搜索** 和 **插件系统**，为用户提供快速、精准的本地文件搜索体验。
![pE2mnBt.png](https://s21.ax1x.com/2025/04/10/pE2mnBt.png)
![pEmPKUA.png](https://s21.ax1x.com/2025/02/07/pEmPKUA.png)
![pEsoq9x.png](https://s21.ax1x.com/2025/03/31/pEsoq9x.png)

### 核心特性

- 🚀 **GPU 加速搜索** - 利用显卡并行计算能力，搜索速度提升数倍
- 🧠 **AI 智能搜索** - 集成大语言模型，可总结文档以及自然语言搜索
- 🔌 **插件系统** - 支持扩展功能，实现个性化需求
- ⚡ **实时索引** - 自动监控文件变化，保持索引最新
- 🎯 **智能排序** - 根据使用频率优化搜索结果
- 🔑 **全局快捷键** - 随时随地快速搜索（默认为`Ctrl + Alt + Shift + A`）
- 💎 **现代界面** - 亚克力效果，美观易用

---

## 💻 安装和启动

### 系统要求
- **操作系统**: Windows 10/11 (64位)
- **GPU**: 支持 AMD/NVIDIA 显卡（可选，用于加速）
- **RAM**: 建议 4GB 以上
- **存储**: 约 200MB 安装空间

### 下载安装
1. **下载地址**：
   - [GitHub Release](https://github.com/panwangwin/aiverything-official-forum/releases)
   - [夸克网盘](https://pan.quark.cn/s/5ac2bf9154d7)

2. **安装步骤**：
   - 下载 `aiverything_x.x.x_x64-setup.exe`
   - 以管理员权限运行安装程序
   - 按照向导完成安装

3. **首次启动**：
   - 应用会自动建立文件索引
   - 首次索引可能需要几分钟时间
   - 系统托盘会显示 Aiverything 图标

---

## 🔍 基础使用

### 启动搜索

有多种方式打开搜索窗口：

1. **全局快捷键**：`Ctrl + Shift + Alt + A`（默认）
2. **双击 Ctrl 键**：快速连按两次 Ctrl 键
3. **系统托盘**：右键点击托盘图标 → 选择"搜索"

### 基本搜索

在搜索框中直接输入关键词即可：

```
示例：输入 "document" 搜索包含该词的文件
```

### 搜索结果操作

搜索结果支持以下操作：

- **Enter** - 打开文件
- **Ctrl + Enter** - 以管理员权限打开
- **Alt + Enter** - 复制文件路径
- **Shift + Enter** - 打开文件所在文件夹
- **双击** - 打开文件

### 快速访问

当搜索框为空时，显示快速访问面板：

- 此电脑、回收站、下载文件夹
- 控制面板、系统设置、环境变量
- 设备管理器、任务管理器等系统工具
- 支持滚轮翻页查看更多快捷方式

---

## 🔧 高级搜索功能

### 多关键词搜索

使用分号 `;` 分隔多个关键词：

```
示例：test;file  # 同时包含 "test" 和 "file" 的文件
```

### 搜索过滤器

使用竖线 `|` 添加搜索条件：

| 过滤器 | 说明 | 示例 |
|--------|------|------|
| `f` | 仅搜索文件 | `photo|f` |
| `d` | 仅搜索目录 | `project|d` |
| `full` | 全匹配 | `readme.txt|full` |
| `case` | 区分大小写 | `Test|case` |
| `p` | 正则表达式 | `\d+\.txt|p` |
| `c` | 内容搜索 | `function|c` |

### 缓存搜索

输入冒号 `:` 开头搜索最近使用的文件：

```
示例：:report  # 从缓存中搜索包含 "report" 的文件
```

### 网络搜索

当没有找到本地文件时，搜索结果会显示"网络搜索"选项，支持：
- Google、Bing、百度、DuckDuckGo
- 可在设置中更改默认搜索引擎

---

## 🤖 AI 搜索功能

### 启用 AI 模式

点击搜索框右侧的 AI 图标切换到 AI 搜索模式。

### AI 搜索特性

- **自然语言理解**：用自然语言描述搜索需求
- **智能文件分析**：理解文件内容和用途
- **个性化推荐**：基于使用习惯推荐相关文件

### 使用示例

```
普通搜索：document.pdf
AI 搜索：帮我找一下上周的工作报告
```

### AI 文件摘要

- 右侧面板显示文件详情时，支持 AI 摘要功能
- 自动分析文档内容，生成摘要
- 支持多种文件格式（PDF、Word、Excel 等）

---

## 🔌 插件系统

### 插件管理

输入 `>` 进入插件模式：

```
示例：>action  # 搜索Action插件
```

### 插件安装

1. 在插件模式下浏览可用插件
2. 选择插件后按 Enter 安装
3. 插件支持嵌入式和独立窗口两种模式

### 插件开发

- 支持 Java 插件开发
- 提供插件 SDK（即将开放）
- 支持 Web 技术栈开发插件界面

---

## ⌨️ 快捷键操作

### 全局快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl + Shift + Alt + A` | 打开/隐藏搜索窗口 |
| `双击 Ctrl` | 快速打开搜索窗口 |
| `双击 Shift` | 附加模式时打开搜索 |

### 搜索结果快捷键

| 快捷键 | 功能 |
|--------|------|
| `Enter` | 打开文件 |
| `Ctrl + Enter` | 以管理员权限打开 |
| `Alt + Enter` | 复制文件路径 |
| `Shift + Enter` | 打开文件所在文件夹 |
| `↑/↓` | 选择上/下一个结果 |
| `Esc` | 隐藏搜索窗口 |

### 自定义快捷键

可在设置中自定义以下快捷键：
- 复制路径快捷键（默认：Alt）
- 打开父文件夹快捷键（默认：Ctrl）
- 管理员权限打开快捷键（默认：Shift）

---

## ⚙️ 设置配置

通过系统托盘右键菜单 → "设置" 进入配置界面。

### 账户设置

- 用户登录和许可证管理

### 常规设置

- **开机启动**：自动随系统启动
- **搜索引擎**：选择默认网络搜索引擎
- **语言**：界面显示语言
- **贴靠资源管理器**：附加到文件管理器

### 索引设置

- **磁盘选择**：选择需要索引的磁盘
- **忽略目录**：排除不需要索引的文件夹
- **缓存大小**：设置索引缓存最大数量
- **文件变更检查**：监控间隔时间设置

### 搜索设置

- **模糊匹配**：启用智能匹配算法
- **GPU 设备**：选择用于加速的显卡
- **优先文件夹**：设置优先搜索的目录

### 数据类型设置

- **文件类型映射**：配置不同文件类型的后缀名
- **自定义分类**：添加新的文件类型分类

### 缓存设置

- **缓存列表**：查看和管理已缓存的文件
- **缓存搜索**：在缓存中快速查找文件

### 快捷键设置

- **全局快捷键**：自定义呼出搜索的组合键
- **文件操作**：配置文件操作相关快捷键
- **双击 Ctrl**：启用/禁用双击 Ctrl 功能

### 高级设置

- **调试模式**：开发者选项
- **缓存块配置**：内存和 GPU 缓存参数
- **内容索引**：启用文件内容索引
- **大语言模型**：配置 AI 功能相关设置

---

## 🔧 问题排查

### 常见问题

**Q: 搜索结果不完整或过时？**
A: 
1. 检查索引设置中是否包含了目标磁盘
2. 手动更新索引：设置 → 索引 → 更新索引
3. 检查忽略目录设置是否排除了目标文件夹

**Q: 快捷键不响应？**
A:
1. 检查是否有其他程序占用了相同快捷键
2. 以管理员权限运行程序
3. 在设置中重新配置快捷键

**Q: GPU 加速不工作？**
A:
1. 确认显卡驱动已更新到最新版本
2. 在搜索设置中选择正确的 GPU 设备
3. 检查显卡是否支持 CUDA 或 OpenCL

**Q: AI 功能无法使用？**
A:
1. 检查网络连接是否正常
2. 确认Ollama配置正确
3. 查看高级设置中的 AI 相关配置

**Q: 插件无法加载？**
A:
1. 确保 Java 运行环境已正确安装
2. 检查插件是否兼容当前版本
3. 查看插件错误日志获取详细信息

### 性能优化

1. **减少索引范围**：只索引必要的磁盘和文件夹
2. **调整缓存大小**：根据内存情况设置合适的缓存大小
4. **使用 SSD**：将索引文件存储在固态硬盘上
---

## 📞 联系我们

### 官方渠道

- **官方网站**：[https://aiverything.me/](https://aiverything.me/)
- **GitHub**：[问题反馈](https://github.com/panwangwin/aiverything-official-forum/issues)
- **QQ 交流群**：893463594

### 反馈建议

我们欢迎您的反馈和建议：

1. **Bug 报告**：请在 GitHub Issues 中详细描述问题
2. **功能建议**：通过 GitHub 或 QQ 群提出新功能需求
3. **使用体验**：分享您的使用心得和改进建议

### 版本更新

- 当前版本：Beta 测试阶段
- 更新检查：设置 → 关于 → 检查更新
- 更新日志：GitHub Releases 页面

---

## 🎯 使用技巧

### 提高搜索效率

1. **使用缓存搜索**：常用文件用 `:` 前缀快速查找
2. **组合搜索条件**：结合多个过滤器精确定位
3. **设置优先文件夹**：常用目录优先显示结果
4. **利用智能排序**：系统会学习您的使用habits

### 工作流优化

1. **配置启动项**：设置开机自动启动
2. **自定义快捷键**：设置适合自己的操作习惯
3. **使用插件系统**：安装工作相关的插件
4. **定期维护**：清理缓存和更新索引

### 高级用法

1. **正则表达式搜索**：使用 `|p` 进行复杂匹配
2. **内容搜索**：启用内容索引搜索文件内部文本
3. **AI 辅助搜索**：用自然语言描述搜索需求
4. **插件开发**：开发个人定制插件

---
*感谢您选择 Aiverything！如有任何问题，请随时联系我们的支持团队。* 