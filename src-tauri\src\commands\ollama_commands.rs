use reqwest::Client;
use reqwest::Method;
use serde_json::Value;

#[tauri::command]
pub async fn get_model_list(address: String) -> Result<Value, String> {
    let client = Client::builder().build().expect("Failed to create client");
    let response = client
        .request(Method::GET, format!("{}/api/tags", address))
        .send()
        .await
        .expect("Failed to send request")
        .json::<Value>()
        .await
        .expect("Failed to parse response");
    Ok(response)
}
