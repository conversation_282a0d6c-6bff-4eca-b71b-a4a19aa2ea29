import { invoke } from "@tauri-apps/api/core";

const PluginApi = {
  getPluginList: "get_plugin_list",
  getPluginInfo: "get_plugin_info",
  getPluginResourceUrl: "get_plugin_resource_url",
  getPluginConfigValue: "get_plugin_config_value",
  getPluginConfigRaw: "get_plugin_config_raw",
  getPluginConfigSchema: "get_plugin_config_schema",
  getPluginConfigFilePath: "get_plugin_config_file_path",
  getPluginLoadError: "get_plugin_load_error",
  pluginEnter: "plugin_enter",
  pluginExit: "plugin_exit",
};

const baseUrl = "https://localhost:38884";

export interface PluginLoadError {
  type: string;
  name: string[];
}

export function getPluginList(pluginName: string) {
  return invoke<any>(PluginApi.getPluginList, {
    pluginName,
  });
}

export function getPluginInfo(pluginIdentifier: string, locale: string) {
  return invoke<any>(PluginApi.getPluginInfo, {
    pluginIdentifier,
    locale: locale || "",
  });
}

export function getPluginConfigValue(pluginIdentifier: string) {
  return invoke<any>(PluginApi.getPluginConfigValue, {
    pluginIdentifier,
  });
}

export function getPluginConfigRaw(pluginIdentifier: string) {
  return invoke<any>(PluginApi.getPluginConfigRaw, {
    pluginIdentifier,
  });
}

export function getPluginResourceUrl(
  pluginIdentifier: string,
  resourceName: string
) {
  return invoke<any>(PluginApi.getPluginResourceUrl, {
    pluginIdentifier,
    resourceName,
  });
}

export async function setPluginConfig(
  pluginIdentifier: string,
  configName: string,
  configComponent: any
) {
  const response = await fetch(
    `${baseUrl}/plugin/setPluginConfig?pluginIdentifier=${encodeURIComponent(
      pluginIdentifier
    )}&configName=${encodeURIComponent(configName)}`,
    {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(configComponent),
    }
  );
  return response.json();
}

export async function addPluginConfig(
  pluginIdentifier: string,
  configName: string,
  configComponent: any
) {
  const response = await fetch(
    `${baseUrl}/plugin/addPluginConfig?pluginIdentifier=${encodeURIComponent(
      pluginIdentifier
    )}&configName=${encodeURIComponent(configName)}`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(configComponent),
    }
  );
  return response.json();
}

export async function deletePluginConfig(
  pluginIdentifier: string,
  configName: string
) {
  const response = await fetch(
    `${baseUrl}/plugin/deletePluginConfig?pluginIdentifier=${encodeURIComponent(
      pluginIdentifier
    )}&configName=${encodeURIComponent(configName)}`,
    {
      method: "DELETE",
    }
  );
  return response.json();
}

export function getPluginConfigFilePath(pluginIdentifier: string) {
  return invoke<any>(PluginApi.getPluginConfigFilePath, {
    pluginIdentifier,
  });
}

export function getPluginLoadError() {
  return invoke<any>(PluginApi.getPluginLoadError);
}

export function pluginEnter(pluginIdentifier: string) {
  return invoke<any>(PluginApi.pluginEnter, {
    pluginIdentifier,
  });
}

export function pluginExit(pluginIdentifier: string) {
  return invoke<any>(PluginApi.pluginExit, {
    pluginIdentifier,
  });
}

export function updateLlm() {
  return fetch(`${baseUrl}/plugin/updateLlm`, {
    method: "PUT",
  });
}
