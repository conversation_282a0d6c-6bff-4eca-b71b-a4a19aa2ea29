# Aiverything 插件开发指南总览

## 概述

本项目是 Aiverything 平台的插件开发模板，为开发者提供了一个完整的插件开发框架。每个插件都是一个完整的应用，由后端逻辑和前端页面组成，支持配置管理和国际化功能。

## 插件架构组成

一个完整的 Aiverything 插件由以下部分组成：

### 🔧 [后端开发](./插件后端开发指南.md)
- **插件基类和生命周期管理** - 插件的核心逻辑
- **命令注册和处理机制** - 处理前端调用和业务逻辑
- **配置管理和数据持久化** - 管理插件设置和数据存储
- **错误处理和日志记录** - 确保插件稳定运行
- **测试和调试** - 开发调试和质量保证

**作用：**
- 提供插件的核心业务逻辑
- 处理数据计算和存储
- 响应前端页面的API调用
- 管理插件配置和状态

### 🖼️ [嵌入页面前端开发](./插件嵌入页面开发指南.md)
- **嵌入式HTML页面开发** - 在搜索界面中显示
- **与主应用的消息通信** - 与Aiverything主界面交互
- **实时搜索交互** - 响应用户搜索输入
- **轻量级UI组件** - 简洁的用户界面

**适用场景：**
- 在搜索结果中显示额外信息
- 提供快速工具和计算功能
- 增强搜索体验的轻量级交互
- 实时预览和简单操作

### 🌐 [独立页面前端开发](./插件独立页面开发指南.md)
- **完整的单页应用开发** - 独立窗口运行
- **复杂的用户界面** - 丰富的交互功能
- **状态管理和路由** - 完整的应用架构
- **高级交互功能** - 复杂的业务流程

**适用场景：**
- 需要复杂界面的管理工具
- 数据编辑和批量处理
- 图表展示和数据可视化
- 多步骤的工作流程

## 目录结构

```
plugin-template/
├── pom.xml                                    # Maven 项目配置文件
├── README.md                                  # 项目说明文档
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/platform/plugin/template/
│   │   │       └── TestPlugin.java           # 主插件类（后端逻辑 - 必需）
│   │   └── resources/
│   │       ├── CHANGELOG.md                  # 更新日志
│   │       ├── plugin.json                   # 插件元数据配置
│   │       ├── settings.json                 # 插件设置配置
│   │       ├── README.md                     # 插件使用说明
│   │       ├── i18n/                         # 国际化资源
│   │       │   ├── Plugin_en_US.properties   # 英文语言包
│   │       │   └── Plugin_zh_CN.properties   # 中文语言包
│   │       └── static/                       # 静态资源（前端页面 - 可选）
│   │           ├── embedded/                 # 嵌入页面目录（可选）
│   │           │   ├── index.html            # 嵌入页面主文件
│   │           │   ├── style.css             # 嵌入页面样式
│   │           │   └── script.js             # 嵌入页面脚本
│   │           ├── entry/                    # 独立页面目录
│   │           │   ├── index.html            # 独立页面主文件
│   │           │   ├── style.css             # 独立页面样式
│   │           │   └── script.js             # 独立页面脚本
│   │           └── icon.png                  # 插件图标
│   └── test/
│       └── java/                             # 测试代码
└── target/                                   # 编译输出目录
```

### 目录说明

**后端部分：**
- `src/main/java/` - Java后端代码，包含插件主类和业务逻辑
- `src/main/resources/plugin.json` - 插件元数据和配置
- `src/main/resources/settings.json` - 插件设置定义
- `src/main/resources/i18n/` - 国际化语言包

**前端部分：**
- `src/main/resources/static/embedded/` - 嵌入页面相关文件
- `src/main/resources/static/entry/` - 独立页面相关文件
- `src/main/resources/static/icon.png` - 插件图标文件

## 开发环境要求

- **Java 版本**: JDK 21 或更高版本
- **构建工具**: Maven 3.6+
- **IDE**: 推荐使用 IntelliJ IDEA

## 快速开始

### 1. 确定插件架构

根据您的插件功能需求，确定需要开发的组件：

**必需组件：**
- ✅ **后端逻辑** - 所有插件都需要后端来处理业务逻辑

**可选组件：**
- 🔲 **嵌入页面** - 如需在搜索界面中提供交互功能
- 🔲 **独立页面** - 如需提供完整的管理界面

### 2. 配置插件元数据

在 `plugin.json` 中配置插件基本信息：

```json
{
  "name": "My Plugin",
  "identifier": "my-plugin",
  "version": "1.0.0",
  "className": "com.platform.plugin.MyPlugin",
  "author": "Your Name",
  "description": "{{description}}",
  "categories": ["Tools"],
  "keywords": ["tool", "utility"],
  "icon": "icon.png",
  
  // 独立页面配置
  "entryPage": "entry/index.html",
  
  // 嵌入页面配置
  "embeddedSupport": true,
  "embeddedPage": "embedded/index.html"
}
```

### 3. 实现插件组件

按照插件架构，依次实现各个组件：

#### 第一步：实现后端逻辑
参考 [后端开发指南](./插件后端开发指南.md)：
- 创建插件主类，继承 `Plugin` 基类
- 实现生命周期方法（`load`, `afterLoaded`, `unload` 等）
- 注册命令处理器，供前端调用
- 配置插件设置和国际化

#### 第二步：开发前端页面
根据需要选择开发：

**嵌入页面开发**：
- 参考 [嵌入页面开发指南](./插件嵌入页面开发指南.md)
- 创建轻量级HTML页面
- 实现与主应用的消息通信
- 调用后端API获取数据

**独立页面开发**：
- 参考 [独立页面开发指南](./插件独立页面开发指南.md)
- 使用现代前端框架（推荐Vite + Vue/React）
- 实现完整的应用界面
- 集成后端API调用

#### 第三步：测试和调试
- 启用调试模式进行开发测试
- 使用IDE远程调试后端逻辑
- 测试前后端API通信
- 验证用户界面交互

### 前端开发技巧

#### 使用 Vite 开发服务器进行快速开发

在开发嵌入页面时，可以利用 Vite 的热更新功能来提高开发效率：

**嵌入页面开发**：
```json
// plugin.json - 开发时配置
{
  "embeddedSupport": true,
  "embeddedPage": "http://localhost:5173/plugin/static/my-plugin-identifier/embedded/"  // 包含base路径的完整地址
}
```

**开发流程**：
1. 启动 Vite 开发服务器：`npm run dev`
2. 修改 `plugin.json` 中的 `embeddedPage` 路径指向开发服务器（包含base路径）
3. 重新加载插件，享受热更新开发体验
4. 开发完成后改回静态文件路径用于生产部署

**注意事项**：
- 开发服务器地址必须包含在 `vite.config.js` 中配置的 `base` 路径
- 此方法仅适用于嵌入页面开发，独立页面需要使用传统的构建部署方式
- 确保 Vite 开发服务器的端口与配置中的端口一致

**优势**：
- ⚡ 热更新：代码修改后自动刷新
- 🛠️ 开发工具：完整的浏览器调试支持
- 🚀 快速迭代：无需重复构建和部署

## 开发工作流

### 开发阶段

1. **环境准备**
   ```bash
   # 克隆模板项目
   git clone [template-repo]
   cd plugin-template
   
   # 安装依赖
   mvn clean install
   ```

2. **启用调试模式**
   - 在Aiverything中启用调试模式
   - 配置JDK路径和Java Agent路径
   - 重启Aiverything启用35005端口调试

3. **开发和测试**
   - 使用IDE连接到35005端口进行远程调试
   - 查看调试模式设置：
   
   ![debug mode settings](./pictures/enable%20debug.png)

### 部署阶段

1. **构建插件**
   ```bash
   mvn clean package
   ```

2. **安装插件**
   ```bash
   # 将JAR文件复制到插件目录
   cp target/plugin-name-1.0.0.jar /path/to/aiverything/core/plugins/
   ```

3. **重启应用**
   - 重启Aiverything以加载新插件

## 配置文件说明

### plugin.json - 插件元数据

定义插件的基本信息和配置：

```json
{
  "name": "插件显示名称",
  "identifier": "插件唯一标识符",
  "version": "版本号",
  "className": "主类全限定名",
  "author": "作者信息",
  "description": "{{description}}",      // 支持国际化
  "detailedDescription": "{{detailedDescription}}",
  "categories": ["分类"],
  "keywords": ["关键词"],
  "icon": "图标文件名",
  "preview": false,                     // 是否为预览版
  "dependencies": [],                   // 依赖的其他插件
  "entryPage": "入口页面",              // 独立页面
  "embeddedSupport": true,              // 是否支持嵌入
  "embeddedPage": "嵌入页面"            // 嵌入页面
}
```

### settings.json - 插件设置

定义插件的配置选项：

```json
{
  "title": "设置标题",
  "config": {
    "configName": {
      "type": "配置类型",              // string, number, boolean, object, array
      "description": "配置描述",
      "defaultValue": "默认值",
      "enumValues": ["枚举值"],        // 可选的枚举值
      "enumDescription": ["枚举描述"]   // 枚举值描述
    }
  }
}
```

## 国际化支持

### 语言包文件

在 `i18n` 目录下创建语言包：

**Plugin_zh_CN.properties** (中文)
```properties
description=插件描述
detailedDescription=详细描述
configName.description=配置项描述
```

**Plugin_en_US.properties** (英文)
```properties
description=Plugin description
detailedDescription=Detailed description
configName.description=Config item description
```
## 调试和测试

### 调试配置图示

以下是调试配置的详细步骤：

 - 打开Aiverything调试模式，并重启软件
![enabledebug.png](./pictures/enable%20debug.png)
 - 使用idea打开插件项目，编辑运行/调试配置
![openidea](./pictures/openidea.png)

![setapp](./pictures/setapp.png)

![loadplugin](./pictures/loadplugin.png)

![loadplugin2](./pictures/loadplugin2.png)

![unloadplugin](./pictures/unloadplugin.png)

![unloadplugin2](./pictures/unloadplugin2.png)

![jvmdebug](./pictures/jvmdebug.png)

![jvmdebug2](./pictures/jvmdebug2.png)

![jvmdebug3](./pictures/jvmdebug3.png)

![jvmdebug4](./pictures/jvmdebug4.png)

![jvmdebug5](./pictures/jvmdebug5.png)

![jvmdebug6](./pictures/jvmdebug6.png)

![jvmdebug3](./pictures/jvmdebug3.png)

![jvmdebug7](./pictures/jvmdebug7.png)

![jvmdebug8](./pictures/jvmdebug8.png)

![jvmdebug9](./pictures/jvmdebug9.png)

## 常见问题

### 插件加载失败
- 检查Java版本是否为21+
- 确认plugin.json中的className正确
- 检查依赖配置

### 前端页面不显示
- 检查HTML文件路径
- 确认plugin.json中的页面配置
- 查看浏览器控制台错误

### 调试连接失败
- 确认35005端口未被占用
- 检查JDK路径配置
- 验证Java Agent路径

### 配置不生效
- 确认配置格式正确
- 检查配置文件权限
- 重启Aiverything应用

## 技术支持

- **官方文档**: 查看详细的API文档
- **示例代码**: 参考模板项目中的示例
- **社区支持**: 加入开发者交流群
- **GitHub**: 提交Issue和Pull Request

---

## 相关文档

- [插件后端开发指南](./插件后端开发指南.md) - 插件后端逻辑开发的完整指南，包括插件基类、配置管理、命令注册等核心功能
- [插件嵌入页面开发指南](./插件嵌入页面开发指南.md) - 嵌入式前端页面开发指南，用于在搜索界面中提供轻量级交互功能
- [插件独立页面开发指南](./插件独立页面开发指南.md) - 独立前端应用开发指南，用于创建完整的插件管理界面和复杂功能
- [API参考文档](./API参考.md) - 完整的插件开发API参考文档
- [快速入门指南](./快速入门.md) - 新手快速上手插件开发的教程

