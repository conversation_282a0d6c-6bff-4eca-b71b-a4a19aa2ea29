import React, { useState, useEffect } from "react";

const Key = ({ label, isPressed }) => {
  const baseClasses =
    "px-1.5 py-0.25 flex items-center justify-center text-xs font-medium rounded border shadow-sm transition-colors duration-100";
  const pressedClasses = isPressed
    ? "bg-blue-500 text-white border-blue-600"
    : "bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200";

  const widthClasses =
    label.length < 2
      ? "min-w-[24px]"
      : label.length <= 5
      ? "min-w-[32px]"
      : "min-w-[48px]";

  return (
    <div className={`${baseClasses} ${pressedClasses} ${widthClasses}`}>
      {label}
    </div>
  );
};

interface ShortCutHintProps {
  shortcut: string[];
  hintMessage: string;
}

export const ShortCutHint: React.FC<ShortCutHintProps> = ({
  shortcut,
  hintMessage,
}) => {
  const [pressedKeys, setPressedKeys] = useState<string[]>([]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Alt") {
        event.preventDefault();
      }
      let key = event.key.toUpperCase();
      if (key === "CONTROL") {
        key = "CTRL";
      } else if (key === " ") {
        key = "SPACEBAR";
      }

      setPressedKeys((prevKeys) => {
        if (!prevKeys.includes(key)) {
          return [...prevKeys, key];
        }
        return prevKeys;
      });
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      setPressedKeys((prevKeys) => {
        let key = event.key.toUpperCase();
        if (key === "CONTROL") {
          key = "CTRL";
        } else if (key === " ") {
          key = "SPACEBAR";
        }
        return prevKeys.filter((k) => k !== key);
      });
    };

    const handleBlur = () => {
      setPressedKeys([]);
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);
    window.addEventListener("blur", handleBlur);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
      window.removeEventListener("blur", handleBlur);
    };
  }, []);

  return (
    <div className="flex items-center text-xs text-gray-500">
      <div className="flex items-center gap-1">
        {shortcut.map((key) => {
          if (key === "CONTROL") {
            key = "CTRL";
          }
          const isCurrentKeyPressed = pressedKeys.includes(key?.toUpperCase());
          return (
            key && <Key key={key} label={key} isPressed={isCurrentKeyPressed} />
          );
        })}
      </div>
      <span className="ml-2">{hintMessage}</span>
    </div>
  );
};
