# 快速入门指南

## 5分钟创建你的第一个插件

### 步骤 1: 环境准备

确保你的开发环境满足以下要求：
- Java 21+
- Maven 3.6+
- IDE (推荐 IntelliJ IDEA)

### 步骤 2: 克隆模板项目

```bash
git clone <plugin-template-repo>
cd plugin-template
```

### 步骤 3: 修改插件信息

1. **修改 `src/main/resources/plugin.json`**:
```json
{
  "name": "我的第一个插件",
  "identifier": "my-first-plugin", 
  "version": "1.0.0",
  "className": "com.platform.plugin.template.TestPlugin",
  "author": "你的名字",
  "description": "{{description}}", // 在plugin.json以及settings.json中使用{{varName}}表示在i18n文件中寻找对应的value进行替换，即下方的Plugin_zh_CN.properties
  "categories": ["工具"],
  "keywords": ["示例", "工具"]
}
```

2. **修改 `src/main/resources/i18n/Plugin_zh_CN.properties`**:
```properties
description=我的第一个插件
detailedDescription=这是我创建的第一个 Aiverything 插件
```

### 步骤 4: 实现插件逻辑

修改 `src/main/java/com/platform/plugin/template/TestPlugin.java`:

```java
@Override
public void load(Map<String, Object> config) {
    System.out.println("我的插件正在加载...");
    
    // 注册一个简单的问候命令
    registerCommandHandler("sayHello", paramsMap -> {
        String name = (String) paramsMap.get("name");
        if (name == null) name = "世界";
        return "你好, " + name + "!";
    });
    
    System.out.println("问候命令已注册");
}
```

### 步骤 5: 构建插件

```bash
mvn clean package
```

### 步骤 6: 测试插件
 - 打开Aiverything调试模式，并重启软件
![enabledebug.png](./pictures/enable%20debug.png)
 - 使用idea打开插件项目，编辑运行/调试配置
![openidea](./pictures/openidea.png)

![setapp](./pictures/setapp.png)

![loadplugin](./pictures/loadplugin.png)

![loadplugin2](./pictures/loadplugin2.png)

![unloadplugin](./pictures/unloadplugin.png)

![unloadplugin2](./pictures/unloadplugin2.png)

![jvmdebug](./pictures/jvmdebug.png)

![jvmdebug2](./pictures/jvmdebug2.png)

![jvmdebug3](./pictures/jvmdebug3.png)

![jvmdebug4](./pictures/jvmdebug4.png)

![jvmdebug5](./pictures/jvmdebug5.png)

![jvmdebug6](./pictures/jvmdebug6.png)

![jvmdebug3](./pictures/jvmdebug3.png)

![jvmdebug7](./pictures/jvmdebug7.png)

![jvmdebug8](./pictures/jvmdebug8.png)

![jvmdebug9](./pictures/jvmdebug9.png)

## 下一步

- 阅读 [插件开发指南](./插件开发指南.md) 了解更多功能
- 查看 [API 参考](./API参考.md) 了解所有可用的方法
- 探索 [示例项目](../src/main/java/com/platform/plugin/template/TestPlugin.java) 中的高级用法

## 常用命令

```bash
# 清理并编译
mvn clean compile

# 运行测试
mvn test

# 打包插件
mvn clean package

# 跳过测试直接打包
mvn clean package -DskipTests
```

## 文件结构说明

```
src/main/
├── java/                          # Java 源代码
│   └── com/platform/plugin/template/
│       └── TestPlugin.java       # 主插件类
└── resources/                     # 资源文件
    ├── plugin.json               # 插件元数据
    ├── settings.json             # 插件配置
    ├── i18n/                     # 国际化文件
    └── static/                   # 静态资源 (HTML, CSS, JS, 图片等)
```

祝你开发愉快！🎉 