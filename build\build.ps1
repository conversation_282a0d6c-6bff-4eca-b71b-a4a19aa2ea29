cd D:\Windows-code\File-Engine-Core-Wrapper\File-Engine-Core
git status
git pull
python build.py --jdk-home "D:\windows-jdks\jdk-21" --additional-module jdk.crypto.ec jdk.unsupported java.base java.compiler java.datatransfer java.desktop java.instrument java.logging java.management java.management.rmi java.naming java.net.http java.prefs java.rmi java.scripting java.se java.security.jgss java.security.sasl java.smartcardio java.sql java.sql.rowset java.transaction.xa java.xml java.xml.crypto jdk.charsets

cd D:\Windows-code\win-platform
git status
git pull

cd D:\Windows-code\win-platform\file-manager
# Set JAVA HOME
$env:JAVA_HOME = "D:\windows-jdks\jdk-21"
mvn clean install -DskipTests

rm -r D:\Windows-code\aiverything\src-tauri\core\*
cp -r D:\Windows-code\File-Engine-Core-Wrapper\File-Engine-Core\build\* D:\Windows-code\aiverything\src-tauri\core\
Get-ChildItem -Path "D:\Windows-code\win-platform\file-manager\plugin-service\target\" -Filter "*.jar" | Where-Object { $_ -notmatch "proguard" } | Sort-Object -Property Name | Select-Object -First 1 | Copy-Item -Destination "D:\Windows-code\aiverything\src-tauri\core\Plugin-Service.jar"

cd D:\Windows-code\aiverything
git status
git pull
$env:TAURI_SIGNING_PRIVATE_KEY="dW50cnVzdGVkIGNvbW1lbnQ6IHJzaWduIGVuY3J5cHRlZCBzZWNyZXQga2V5ClJXUlRZMEl5emZ1c3ZoVjVFNVd1aTNjMXZnYlY0RXNxN0tHL0dZc1U3UGY3aWVaVHBoa0FBQkFBQUFBQUFBQUFBQUlBQUFBQS9DaGpSa1BUOWhDMXJYbFpydy9HSFhYQk5rVjV5WUpVNmdNRlRwYmdxcmhOencySDhvR3A0bWlXdHgxcVRLWDVET0EwVGMzVEpGam12SUpNRWdwdjlsVU44RlFoTWthZ3NUbGcrNm1wSE1zWWFGSm1lT0NXVlJoZnhqS25HTUJBM1ZsK2t6dnhJTWM9Cg=="
$env:TAURI_SIGNING_PRIVATE_KEY_PASSWORD="aiverything"
yarn
yarn tauri build

explorer "D:\Windows-code\aiverything\src-tauri\target\release\bundle\nsis"