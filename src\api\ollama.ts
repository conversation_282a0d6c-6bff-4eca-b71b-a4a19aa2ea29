import { invoke } from "@tauri-apps/api/core";

export interface OllamaModel {
  name: string;
  modified_at: string;
  size: number;
  digest: string;
}

export interface OllamaResponse {
  models: OllamaModel[];
}

const ollamaApi = {
  getModelList: "get_model_list",
};

export async function listModels(address: string): Promise<OllamaResponse> {
  return invoke<OllamaResponse>(ollamaApi.getModelList, { address });
}
