# Host Editor插件详细使用指南

## 概述

Host Editor插件是一个IP域名管理工具，主要用于管理Windows系统的hosts文件和设备证书。它可以帮助用户轻松添加、删除和管理域名映射。

## 核心概念

### 1. 设备管理
- **定义**：通过IP地址添加和管理网络设备
- **功能**：自动生成唯一域名，修改hosts文件，管理设备连接
- **特点**：支持批量操作，自动化域名生成

### 2. Hosts文件管理
- **定义**：Windows系统的域名解析配置文件
- **作用**：将IP地址映射到域名，实现本地DNS解析
- **位置**：`%SYSTEMROOT%\System32\drivers\etc\hosts`

### 3. 证书管理
- **定义**：管理设备的SSL/TLS证书
- **功能**：获取、下载、导入证书到系统受信任根证书存储
- **目的**：导入Aiverything证书，确保HTTPS连接的安全性和可信度

## 主要功能特性

### 🖥️ 设备管理
- **智能域名生成**：基于IP地址的MD5哈希生成唯一域名
- **实时验证**：添加设备时自动验证连接状态

### 🔧 Hosts文件操作
- **自动修改**：使用管理员权限自动修改hosts文件

### 🔒 证书管理
- **证书获取**：从设备自动获取SSL证书
- **证书导入**：将证书导入到系统受信任根证书存储
- **证书下载**：将证书保存到Downloads文件夹

### 🌐 插件URL管理
- **URL注册**：管理各插件在设备上的URL
- **快速访问**：一键在浏览器中打开插件页面
- **状态监控**：实时显示插件URL的可用性

### 🌍 用户体验
- **多语言支持**：支持中文和英文界面
- **响应式设计**：适配不同屏幕尺寸
- **直观操作**：简洁的用户界面和操作流程

## 详细使用步骤

### 1. 启动插件

1. **打开插件界面**
   - 在插件列表中找到"Host Editor"
   - 点击进入插件界面

2. **界面概览**
   - 顶部：插件标题和语言选择器
   - 中间：设备添加表单
   - 底部：设备列表和操作按钮

### 2. 添加设备

#### 单个设备添加
1. **输入设备信息**
   - 在输入框中输入设备的IP地址
   - 示例：`*************`

2. **提交添加**
   - 点击"添加设备"按钮
   - 或按Enter键快速添加

3. **系统处理**
   - 插件会自动生成唯一域名（基于IP地址MD5哈希的前8位）
   - 示例：`*************` → `a1b2c3d4.aiverything.me`

### 3. 管理设备

#### 查看设备信息
- **设备列表**：显示所有已添加的设备
- **基本信息**：IP地址、生成的域名
- **状态信息**：连接状态、插件URL列表

#### 访问设备
1. **点击域名链接**
   - 直接在浏览器中打开设备主页
   - 格式：`https://域名.aiverything.me`

2. **访问插件页面**
   - 点击插件URL列表中的链接
   - 格式：`https://域名.aiverything.me:38884/插件路径`

#### 删除设备
1. **选择删除**
   - 点击设备右侧的"删除"按钮
   - 系统会弹出确认对话框

2. **确认删除**
   - 点击"确认删除"按钮
   - 系统会从配置和hosts文件中移除设备

### 4. 证书管理

#### 导入证书
1. **点击导入证书**
   - 在设备列表中点击"导入证书"按钮
   - 系统会自动从设备获取证书

2. **证书处理**
   - 证书会自动下载到Downloads文件夹
   - 文件名：`cert.cer`
   - 系统会自动打开文件夹并选中证书文件

3. **手动安装**
   - 由于权限限制，需要手动安装证书
   - 双击证书文件，选择"安装证书"
   - 选择"本地计算机"和"受信任的根证书颁发机构"

#### 证书验证
1. **验证安装**
   - 打开证书管理器（certmgr.msc）
   - 检查"受信任的根证书颁发机构"
   - 确认证书已正确安装

2. **测试连接**
   - 在浏览器中访问设备HTTPS页面
   - 确认没有证书警告

### 5. 高级功能

#### 插件URL管理
1. **URL注册**
   - 插件会自动注册其URL到设备
   - 支持多个插件同时注册

2. **URL访问**
   - 通过设备列表中的插件URL链接访问
   - 支持一键打开功能

## 安全注意事项

### 1. 权限管理
- 插件需要管理员权限来修改hosts文件
- 证书导入需要访问系统证书存储
- 建议在安全的环境中使用

### 2. 证书安全
- 只导入来自可信设备的证书
- 定期检查和更新证书
- 避免导入不明来源的证书

### 3. 网络安全
- 确保设备网络连接的安全性
- 使用HTTPS连接保护数据传输
- 定期更新设备的安全配置

## 总结

Host Editor插件为用户提供了一个强大而便捷的网络设备管理解决方案。通过自动化的域名生成、hosts文件管理和证书处理，用户可以：

- 🚀 **提高效率**：自动化网络设备的域名配置
- 🔒 **增强安全**：简化HTTPS证书的管理流程
- 🎯 **简化操作**：通过直观的界面管理复杂的网络配置
- 🌐 **统一管理**：集中管理多个设备的访问和证书

无论是开发环境的快速配置，还是生产环境的设备管理，Host Editor插件都能为您提供专业可靠的支持。
