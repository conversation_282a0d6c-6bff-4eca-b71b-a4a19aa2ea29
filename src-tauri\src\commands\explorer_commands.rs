use std::{
    thread::sleep,
    time::{self, SystemTime, UNIX_EPOCH},
};

use serde::{Deserialize, Serialize};
use tauri::{App, <PERSON>pp<PERSON><PERSON><PERSON>, WebviewWindow};
use window_vibrancy::apply_acrylic;

use crate::{api::explorer_handle, config::app_config, utils::round_corner};

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct ExplorerStatus {
    x: u64,
    y: u64,
    width: u64,
    height: u64,
    path: String,
    is_dialog: bool,
}

impl ExplorerStatus {
    pub fn new() -> Self {
        Self {
            x: 0,
            y: 0,
            width: 0,
            height: 0,
            path: "".to_string(),
            is_dialog: false,
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SearchBarPos {
    bar_x: f64,
    bar_y: f64,
    bar_width: f64,
    bar_height: f64,
    textfield_height: f64,
}

impl SearchBarPos {
    pub fn new(x: f64, y: f64, width: f64, height: f64) -> Self {
        Self {
            bar_x: x,
            bar_y: y,
            bar_width: width,
            bar_height: height,
            textfield_height: height / 8 as f64,
        }
    }
}

lazy_static::lazy_static! {
    static ref EXPLORER_STATUS: std::sync::Mutex<ExplorerStatus> = std::sync::Mutex::new(ExplorerStatus::new());
}

#[tauri::command]
pub fn get_calculated_window_info() -> Result<SearchBarPos, String> {
    match calculate_pos() {
        Ok(search_bar_pos) => Ok(search_bar_pos),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub fn set_explorer_edit_path(path: &str, file_name: &str) -> Result<(), String> {
    let path = format!("{}\0", path);
    let file_name = format!("{}\0", file_name);
    let ret =
        explorer_handle::set_edit_path(path.as_ptr() as *const i8, file_name.as_ptr() as *const i8);
    match ret {
        Ok(_) => Ok(()),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub fn bring_window_to_top() -> Result<bool, String> {
    match explorer_handle::bring_window_to_top() {
        Ok(ret) => Ok(ret > 0),
        Err(e) => Err(e.to_string()),
    }
}

fn calculate_pos() -> Result<SearchBarPos, String> {
    match EXPLORER_STATUS.lock() {
        Ok(exp_val) => {
            let bar_width = exp_val.width as f64 * 0.3;
            let bar_height = exp_val.height as f64 * 0.5;
            let textfield_height = bar_height / 8 as f64;
            let bar_x;
            let bar_y;
            if exp_val.is_dialog {
                bar_x = exp_val.x as f64 + (exp_val.width as f64 / 2 as f64 - bar_width / 2 as f64);
                bar_y = exp_val.y as f64 + exp_val.height as f64 - bar_height + textfield_height;
            } else {
                bar_x = exp_val.x as f64 + exp_val.width as f64
                    - bar_width
                    - exp_val.width as f64 * 0.05;
                bar_y = exp_val.y as f64 + exp_val.height as f64 - bar_height - textfield_height;
            }
            Ok(SearchBarPos::new(bar_x, bar_y, bar_width, bar_height))
        }
        Err(e) => Err(format!(
            "Failed to create explorer attach window, Error: {}",
            e.to_string()
        )),
    }
}

pub fn create_attach_window(handle: &AppHandle) -> Result<WebviewWindow, String> {
    match calculate_pos() {
        Ok(searchbar_pos) => {
            let _webview_window = tauri::WebviewWindowBuilder::new(
                handle,
                "attach",
                tauri::WebviewUrl::App("attach.html".into()),
            )
            .title("Aiverything-SearchBar")
            .position(
                searchbar_pos.bar_x,
                searchbar_pos.bar_y + searchbar_pos.bar_height - searchbar_pos.textfield_height,
            )
            .inner_size(searchbar_pos.bar_width, searchbar_pos.textfield_height)
            .decorations(false)
            .shadow(false)
            .transparent(true)
            .always_on_top(true)
            .skip_taskbar(true)
            .build()
            .unwrap();

            apply_acrylic(&_webview_window, Some((243, 243, 243, 125)))
                .expect("Unsupported platform! 'apply_acrylic' is only supported on Windows");
            round_corner::set_window_round_corner(&_webview_window)
                .expect("Failed to set window round corner");

            Ok(_webview_window)
        }
        Err(e) => Err(e),
    }
}

pub fn start_monitor_explorer(app: &mut App) {
    let handle = app.handle().clone();
    std::thread::spawn(move || {
        let attach_val = time::Duration::from_millis(25);
        let mut attach_window = None;
        let mut last_explorer_path = String::new();
        let mut last_explorer_path_valid_start_time: Option<u64> = None; // 记录explorer_path有效时间计算起点
        let mut last_was_dialog = false; // 上次是否是dialog模式

        loop {
            let app_configs = app_config::read_config().expect("Failed to read config");
            if !app_configs.attach_explorer {
                sleep(attach_val);
                continue;
            }

            // 开始检测资源管理器位置
            let change_to_attach =
                explorer_handle::change_to_attach().expect("Failed to get explorer status");
            let change_to_normal =
                explorer_handle::change_to_normal().expect("Failed to get explorer status");

            if change_to_attach > 0 {
                // 进入attach模式
                let current_time = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .expect("Time went backwards")
                    .as_secs();

                let explorer_x =
                    explorer_handle::get_explorer_x().expect("Failed to get explorer X");
                let explorer_y =
                    explorer_handle::get_explorer_y().expect("Failed to get explorer Y");
                let explorer_width =
                    explorer_handle::get_explorer_width().expect("Failed to get explorer width");
                let explorer_height =
                    explorer_handle::get_explorer_height().expect("Failed to get explorer height");
                let explorer_dialog =
                    explorer_handle::is_dialog_window().expect("Failed to get explorer type");

                let max_len = 500 as u64;
                let mut explorer_path = vec![0 as u8; 500];
                explorer_handle::get_explorer_path(explorer_path.as_mut_ptr() as *mut i8, max_len)
                    .expect("Failed to get explorer path");
                let explorer_path_utf8 = String::from_utf8(explorer_path)
                    .expect("Failed to convert explorer path from vec[u8]");

                if let Ok(mut exp_val_mut) = EXPLORER_STATUS.lock() {
                    exp_val_mut.x = explorer_x;
                    exp_val_mut.y = explorer_y;
                    exp_val_mut.width = explorer_width;
                    exp_val_mut.height = explorer_height;
                    exp_val_mut.path = explorer_path_utf8.to_owned();
                    exp_val_mut.is_dialog = explorer_dialog > 0;

                    // 如果当前模式为对话框贴靠，并且上一次attach模式并不是对话框模式
                    // 则可能为explorer模式直接切换到dialog，或者explorer模式切换到normal，再切换到dialog
                    if exp_val_mut.is_dialog && !last_was_dialog {
                        // 从explorer切换到dialog：检查是否满足跳转条件
                        let can_jump = if let Some(last_time) = last_explorer_path_valid_start_time
                        {
                            // 不管从explorer切换到dialog中间是否推出了attach模式
                            // 只要在5秒有效时间内从explorer模式切换到dialog模式，则可以跳转
                            let time_diff = current_time - last_time;
                            let should_jump = time_diff <= 5;
                            println!(
                                "Explorer->Dialog: time_diff={}, can_jump={}",
                                time_diff, should_jump
                            );
                            should_jump
                        } else {
                            println!("Explorer->Dialog: no previous dialog->explorer switch, can_jump=false");
                            false
                        };

                        if can_jump
                            && exp_val_mut.path != last_explorer_path
                            && !last_explorer_path.trim().is_empty()
                        {
                            sleep(time::Duration::from_millis(250));
                            println!("Jumping dialog to explorer path: {}", last_explorer_path);
                            set_explorer_edit_path(&last_explorer_path, "")
                                .expect("Failed to set explorer edit path");
                            last_explorer_path = String::new();
                        }
                    } else if !exp_val_mut.is_dialog {
                        // 当前不是dialog模式，不停刷新explorer模式开始时间
                        last_explorer_path_valid_start_time = Some(current_time);
                    }

                    // 处理路径保存
                    if !exp_val_mut.is_dialog {
                        // Explorer模式：保存当前路径
                        if last_explorer_path != explorer_path_utf8
                            && !explorer_path_utf8.trim().is_empty()
                        {
                            println!("Saving explorer path: {}", explorer_path_utf8);
                            last_explorer_path = explorer_path_utf8;
                        }
                    }

                    // 更新状态
                    last_was_dialog = exp_val_mut.is_dialog;
                } else {
                    eprintln!("Failed to lock EXPLORER_STATUS");
                }

                if attach_window == None {
                    match create_attach_window(&handle) {
                        Ok(webview_window) => {
                            attach_window.replace(webview_window);
                        }
                        Err(e) => eprintln!("Failed to create attach window, Error {}", e),
                    }
                }

                sleep(attach_val);
            } else if change_to_normal > 0 {
                if let Some(window) = attach_window.take() {
                    window.close().unwrap();
                }
                sleep(attach_val);
            } else {
                sleep(attach_val);
            }
        }
    });
}
