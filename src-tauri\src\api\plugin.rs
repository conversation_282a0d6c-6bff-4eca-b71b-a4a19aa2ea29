use reqwest::Client;
use reqwest::Method;
use serde_json::Value;

pub struct PluginApi {
    base_url: String,
    client: Client,
}

impl PluginApi {
    pub fn new(base_url: &str) -> Result<Self, reqwest::Error> {
        let client = Client::builder()
            .danger_accept_invalid_certs(true) // Ignore HTTPS errors
            .build()?;
        Ok(Self {
            base_url: base_url.to_string(),
            client,
        })
    }

    // Helper method to construct full URL
    fn url(&self, endpoint: &str) -> String {
        format!("{}{}", self.base_url, endpoint)
    }

    pub async fn get_plugin_list(&self, plugin_name: &str) -> Result<Value, reqwest::Error> {
        let params = [("pluginName", plugin_name)];
        let response = self
            .client
            .request(Method::GET, self.url("/plugin/plugin"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_plugin_info(
        &self,
        plugin_identifier: &str,
        locale: &str,
    ) -> Result<Value, reqwest::Error> {
        let params = [("pluginIdentifier", plugin_identifier), ("locale", locale)];
        let response = self
            .client
            .request(Method::GET, self.url("/plugin/pluginInfo"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_plugin_config_value(
        &self,
        plugin_identifier: &str,
    ) -> Result<Value, reqwest::Error> {
        let params = [("pluginIdentifier", plugin_identifier)];
        let response = self
            .client
            .request(Method::GET, self.url("/plugin/pluginConfigValue"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_plugin_config_raw(
        &self,
        plugin_identifier: &str,
    ) -> Result<Value, reqwest::Error> {
        let params = [("pluginIdentifier", plugin_identifier)];
        let response = self
            .client
            .request(Method::GET, self.url("/plugin/pluginConfigRaw"))
            .query(&params)
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn get_plugin_load_error(&self) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::GET, self.url("/plugin/errorInfo"))
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn plugin_enter(&self, plugin_identifier: &str) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::POST, self.url("/plugin/pluginEnter"))
            .query(&[("identifier", plugin_identifier)])
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }

    pub async fn plugin_exit(&self, plugin_identifier: &str) -> Result<Value, reqwest::Error> {
        let response = self
            .client
            .request(Method::POST, self.url("/plugin/pluginExit"))
            .query(&[("identifier", plugin_identifier)])
            .send()
            .await?
            .json::<Value>()
            .await?;
        Ok(response)
    }
}
