use raw_window_handle::<PERSON><PERSON><PERSON><PERSON>Handle;
use windows::Win32::Graphics::Dwm::{
    DwmSetWindowAttribute, DWMWA_WINDOW_CORNER_PREFERENCE, DWMWCP_ROUND,
};

pub fn set_window_round_corner(window: impl HasWindowHandle) -> Result<(), String> {
    match window.window_handle() {
        Ok(window_handle) => match window_handle.as_raw() {
            #[cfg(target_os = "windows")]
            raw_window_handle::RawWindowHandle::Win32(handle) => {
                unsafe {
                    let _ = DwmSetWindowAttribute(
                        windows::Win32::Foundation::HWND(handle.hwnd.get() as *mut _),
                        DWMWA_WINDOW_CORNER_PREFERENCE,
                        &DWMWCP_ROUND as *const _ as _,
                        4,
                    );
                };
                Ok(())
            }
            _ => Err("Failed to set window round corner".to_string()),
        },
        Err(e) => Err(format!(
            "Failed to get window handle, error {}",
            e.to_string()
        )),
    }
}
