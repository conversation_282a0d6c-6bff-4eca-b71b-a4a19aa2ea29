import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Tabs,
  Tab,
  Box,
  Typo<PERSON>,
  TextField,
  FormControlLabel,
  Checkbox,
  Paper,
  Collapse,
  IconButton,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Divider,
  Chip,
  FormHelperText,
  Snackbar,
  Alert,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import StoreIcon from "@mui/icons-material/Store";
import {
  getPluginConfigRaw,
  setPluginConfig,
  deletePluginConfig,
  addPluginConfig,
  getPluginConfigFilePath,
} from "../../api/plugin";
import { PluginInfo } from "../MainAnwser";
import { useTranslation } from "react-i18next";
import { showItemInFolder } from "../../api/system";
import { open } from "@tauri-apps/plugin-shell";
import { WebviewWindow } from "@tauri-apps/api/webviewWindow";
import supabasePluginApi from "../../api/supabasePlugin";
import SystemUpdateIcon from "@mui/icons-material/SystemUpdate";
import semver from "semver";

interface ConfigItem {
  type: string;
  description: string;
  defaultValue: any;
  enumValues?: any[];
  enumDescription?: string[];
}

interface ConfigObject {
  [key: string]: ConfigItem;
}

interface PluginConfig {
  title: string;
  config: ConfigObject;
}

interface Plugin extends PluginInfo {
  configRaw?: PluginConfig;
  configSchema?: any;
}

interface ConfigField {
  type: string;
  description: string;
  defaultValue: any;
  value: any;
}

interface NewObjectField {
  fieldName: string;
  fieldConfig: ConfigField;
}

interface NewConfigFormData {
  type: string;
  description: string;
  defaultValue: any;
  value: any;
}

const RenderConfigField = ({
  name,
  config,
  onChange,
  onDelete,
  onAdd,
  path = "",
  isInArray = false,
}: {
  name: string;
  config: any;
  onChange: (path: string, value: any) => void;
  onDelete: (path: string) => void;
  onAdd: (path: string) => void;
  path?: string;
  isInArray?: boolean;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [inputValue, setInputValue] = useState(
    config.value ?? config.defaultValue ?? ""
  );
  const { t } = useTranslation();

  // 添加 useEffect 来监听 config 的变化
  useEffect(() => {
    setInputValue(config.value ?? config.defaultValue ?? "");
  }, [config]);

  const configType = config.type;
  const description = config.description;
  const defaultValue = config.defaultValue;

  let currentPath: string;
  if (path) {
    currentPath = name.startsWith("[") ? `${path}${name}` : `${path}.${name}`;
  } else {
    currentPath = name;
  }

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange(currentPath, {
      type: configType,
      description: description,
      defaultValue: defaultValue,
      value: configType === "number" ? Number(newValue) : newValue,
    });
  };

  // 修改格式化标签的函数
  const getDisplayLabel = (name: string) => {
    if (/^\[\d+\]$/.test(name)) {
      const index = parseInt(name.slice(1, -1)) + 1;
      // 从路径中获取数组的键名
      const arrayKey = path.split(".").pop() || "";
      return `${arrayKey}[${index}]`;
    }
    return name;
  };

  switch (configType) {
    case "number":
      // 如果有枚举值，使用下拉菜单
      if (config.enumValues) {
        return (
          <Box sx={{ mb: 2 }}>
            <Stack direction="row" spacing={1} alignItems="flex-start">
              <FormControl fullWidth margin="normal">
                <InputLabel>{getDisplayLabel(name)}</InputLabel>
                <Select
                  value={inputValue ?? defaultValue}
                  label={getDisplayLabel(name)}
                  onChange={(e) => {
                    setInputValue(e.target.value);
                    onChange(currentPath, {
                      type: configType,
                      description: description,
                      defaultValue: defaultValue,
                      value: e.target.value,
                      enumValues: config.enumValues,
                      enumDescription: config.enumDescription,
                    });
                  }}
                >
                  {config.enumValues.map((value: any, index: number) => (
                    <MenuItem key={value} value={value}>
                      <Stack>
                        <Typography>{value}</Typography>
                        <Typography variant="caption" color="textSecondary">
                          {config.enumDescription?.[index] || ""}
                        </Typography>
                      </Stack>
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText>{description}</FormHelperText>
              </FormControl>
              {isInArray && (
                <IconButton
                  size="small"
                  onClick={() => onDelete(currentPath)}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "56px",
                    alignSelf: "flex-start",
                    mt: "16px",
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              )}
            </Stack>
          </Box>
        );
      }
      // 如果没有枚举值，使用原来的数字输入框
      return (
        <Box sx={{ mb: 2 }}>
          <Stack direction="row" spacing={1} alignItems="flex-start">
            <TextField
              fullWidth
              type="number"
              label={getDisplayLabel(name)}
              helperText={description}
              value={inputValue}
              onChange={handleInputChange}
              margin="normal"
            />
            {isInArray && (
              <IconButton
                size="small"
                onClick={() => onDelete(currentPath)}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "56px",
                  alignSelf: "flex-start",
                  mt: "16px",
                }}
              >
                <DeleteIcon />
              </IconButton>
            )}
          </Stack>
        </Box>
      );

    case "boolean":
      return (
        <Box sx={{ mb: 2 }}>
          <Stack direction="row" spacing={1} alignItems="flex-start">
            <FormControlLabel
              control={
                <Checkbox
                  checked={inputValue ?? defaultValue}
                  onChange={(e) => {
                    setInputValue(e.target.checked);
                    onChange(currentPath, {
                      type: configType,
                      description: description,
                      defaultValue: defaultValue,
                      value: e.target.checked,
                    });
                  }}
                />
              }
              label={
                <Stack spacing={0}>
                  <Typography>{getDisplayLabel(name)}</Typography>
                  <Typography variant="caption" color="textSecondary">
                    {description}
                  </Typography>
                </Stack>
              }
            />
            {isInArray && (
              <IconButton
                size="small"
                onClick={() => onDelete(currentPath)}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "56px",
                  alignSelf: "flex-start",
                  mt: "16px",
                }}
              >
                <DeleteIcon />
              </IconButton>
            )}
          </Stack>
        </Box>
      );

    case "object":
      return (
        <Paper variant="outlined" sx={{ p: 2, my: 2 }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            onClick={() => setIsExpanded(!isExpanded)}
            sx={{ cursor: "pointer" }}
          >
            <Typography variant="subtitle1">{getDisplayLabel(name)}</Typography>
            <Box>
              <IconButton size="small">
                {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
              {isInArray && (
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(currentPath);
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              )}
            </Box>
          </Stack>
          <Typography variant="caption" color="textSecondary">
            {description}
          </Typography>
          <Collapse in={isExpanded}>
            <Box sx={{ pl: 2, mt: 2 }}>
              {Object.entries(defaultValue).map(
                ([key, subConfig]: [string, any]) => (
                  <RenderConfigField
                    key={key}
                    name={key}
                    config={subConfig}
                    onChange={(newPath, newValue) => {
                      onChange(newPath, newValue);
                    }}
                    onDelete={onDelete}
                    onAdd={onAdd}
                    path={currentPath}
                    isInArray={false}
                  />
                )
              )}
            </Box>
          </Collapse>
        </Paper>
      );

    case "array":
      return (
        <Paper variant="outlined" sx={{ p: 2, my: 2 }}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            onClick={() => setIsExpanded(!isExpanded)}
            sx={{ cursor: "pointer" }}
          >
            <Typography variant="subtitle1">{getDisplayLabel(name)}</Typography>
            <Box>
              <IconButton size="small">
                {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  onAdd(currentPath);
                }}
              >
                <AddIcon />
              </IconButton>
              {isInArray && (
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(currentPath);
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              )}
            </Box>
          </Stack>
          <Typography variant="caption" color="textSecondary">
            {description}
          </Typography>
          <Collapse in={isExpanded}>
            <Box sx={{ pl: 2, mt: 2 }}>
              {(defaultValue || []).map((item: any, index: number) => (
                <Box key={`box-${name}-${index}`} sx={{ mb: 2 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    {t("pluginSettings.arrayItem.title", { index: index + 1 })}
                  </Typography>
                  <RenderConfigField
                    key={`${name}-${index}`}
                    name={`[${index}]`}
                    config={item}
                    onChange={(newPath, newValue) => {
                      onChange(newPath, newValue);
                    }}
                    onDelete={onDelete}
                    onAdd={onAdd}
                    path={currentPath}
                    isInArray={true}
                  />
                </Box>
              ))}
            </Box>
          </Collapse>
        </Paper>
      );

    default:
      // 如果有枚举值，使用下拉菜单
      if (config.enumValues) {
        return (
          <Box sx={{ mb: 2 }}>
            <Stack direction="row" spacing={1} alignItems="flex-start">
              <FormControl fullWidth margin="normal">
                <InputLabel>{getDisplayLabel(name)}</InputLabel>
                <Select
                  value={inputValue ?? defaultValue}
                  label={getDisplayLabel(name)}
                  onChange={(e) => {
                    setInputValue(e.target.value);
                    onChange(currentPath, {
                      type: configType,
                      description: description,
                      defaultValue: defaultValue,
                      value: e.target.value,
                      enumValues: config.enumValues,
                      enumDescription: config.enumDescription,
                    });
                  }}
                >
                  {config.enumValues.map((value: string, index: number) => (
                    <MenuItem key={value} value={value}>
                      <Stack>
                        <Typography>{value}</Typography>
                        <Typography variant="caption" color="textSecondary">
                          {config.enumDescription?.[index] || ""}
                        </Typography>
                      </Stack>
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText>{description}</FormHelperText>
              </FormControl>
              {isInArray && (
                <IconButton
                  size="small"
                  onClick={() => onDelete(currentPath)}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    height: "56px",
                    alignSelf: "flex-start",
                    mt: "16px",
                  }}
                >
                  <DeleteIcon />
                </IconButton>
              )}
            </Stack>
          </Box>
        );
      }
      // 如果没有枚举值，使用原来的文本输入框
      return (
        <Box sx={{ mb: 2 }}>
          <Stack direction="row" spacing={1} alignItems="flex-start">
            <TextField
              fullWidth
              label={getDisplayLabel(name)}
              type={name === "password" ? "password" : "text"}
              helperText={description}
              value={inputValue}
              onChange={handleInputChange}
              margin="normal"
              multiline={
                typeof inputValue === "string" && inputValue.length > 50
              }
              rows={
                typeof inputValue === "string" && inputValue.length > 50 ? 3 : 1
              }
            />
            {isInArray && (
              <IconButton
                size="small"
                onClick={() => onDelete(currentPath)}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "56px",
                  alignSelf: "flex-start",
                  mt: "16px",
                }}
              >
                <DeleteIcon />
              </IconButton>
            )}
          </Stack>
        </Box>
      );
  }
};

// 添加防抖 hook
const useDebounce = (callback: (...args: any[]) => void, delay: number) => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return useCallback(
    (...args: any[]) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    },
    [callback, delay]
  );
};

const ConfigFieldRenderer = ({
  rawConfig,
  onChange,
  onDelete,
  onAdd,
}: {
  rawConfig: any;
  onChange: (path: string, newConfig: any) => void;
  onDelete: (path: string) => void;
  onAdd: (path: string) => void;
}) => {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deletingPath, setDeletingPath] = useState<string>("");
  const { t } = useTranslation();

  // 使用防抖包装 onChange
  const debouncedOnChange = useDebounce((path: string, newConfig: any) => {
    console.log(`Updating config, path: ${path}`, newConfig);
    onChange(path, newConfig);
  }, 500);

  const handleDeleteClick = (path: string) => {
    setDeletingPath(path);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    console.log(`Deleting config, path: ${deletingPath}`);
    onDelete(deletingPath);
    setDeleteDialogOpen(false);
    setDeletingPath("");
  };

  return (
    <Box>
      {Object.entries(rawConfig).map(([key, configItem]) => (
        <RenderConfigField
          key={key}
          name={key}
          config={configItem}
          onChange={debouncedOnChange}
          onDelete={handleDeleteClick}
          onAdd={onAdd}
        />
      ))}

      {/* 删除确认对话框 */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          {t("settings.messages.confirmDeleteTitle")}
        </DialogTitle>
        <DialogContent>
          <Typography id="delete-dialog-description">
            {t("settings.messages.confirmDelete")}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>
            {t("settings.buttons.cancel")}
          </Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            {t("settings.buttons.delete")}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

const PluginSettingsContent = ({
  pluginInfoList,
}: {
  pluginInfoList: Plugin[];
}) => {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState(0);
  const [currentPluginConfig, setCurrentPluginConfig] = useState<any>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [addingConfigPath, setAddingConfigPath] = useState("");
  const [newConfigData, setNewConfigData] = useState<NewConfigFormData>({
    type: "string",
    description: "",
    defaultValue: "",
    value: "",
  });
  const [objectFields, setObjectFields] = useState<{
    [key: string]: ConfigField;
  }>({});
  const [currentPath, setCurrentPath] = useState<string[]>([]);
  const [newFieldData, setNewFieldData] = useState<NewObjectField>({
    fieldName: "",
    fieldConfig: {
      type: "string",
      description: "",
      defaultValue: "",
      value: "",
    },
  });
  const [isCheckingUpdate, setIsCheckingUpdate] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<{
    status: string;
    version?: string;
    pluginUrl?: string;
  } | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<
    "success" | "info" | "warning" | "error"
  >("info");

  // 添加插件更新状态缓存
  const [pluginUpdateStatus, setPluginUpdateStatus] = useState<{
    [key: string]: {
      checked: boolean;
      hasUpdate: boolean;
      latestVersion?: string;
      pluginUrl?: string;
    };
  }>({});

  const reloadPluginConfig = () => {
    const selectedPlugin = pluginInfoList[selectedTab].identifier;

    getPluginConfigRaw(selectedPlugin)
      .then((pluginRawConfig) => {
        console.log("current plugin config", pluginRawConfig.data);
        const rawConfig = pluginRawConfig.data;
        setCurrentPluginConfig({ rawConfig });
      })
      .catch((error) => {
        console.error("Error fetching plugin config:", error);
      });
  };

  useEffect(() => {
    reloadPluginConfig();
    // 切换标签时重置更新按钮状态
    setUpdateInfo(null);
    setIsCheckingUpdate(false);
  }, [selectedTab, pluginInfoList]);

  const handleAddConfig = () => {
    const selectedPlugin = pluginInfoList[selectedTab].identifier;

    if (newConfigData.type === "object") {
      const configData = {
        ...newConfigData,
        defaultValue: {},
        value: {},
      };
      addPluginConfig(selectedPlugin, addingConfigPath, configData)
        .then((success) => {
          if (success) {
            setIsAddDialogOpen(false);
            reloadPluginConfig();
            setNewConfigData({
              type: "string",
              description: "",
              defaultValue: "",
              value: "",
            });
          }
        })
        .catch((err) => {
          console.error(err);
        });
    } else {
      addPluginConfig(selectedPlugin, addingConfigPath, newConfigData)
        .then((success) => {
          if (success) {
            setIsAddDialogOpen(false);
            reloadPluginConfig();
            setNewConfigData({
              type: "string",
              description: "",
              defaultValue: "",
              value: "",
            });
          }
        })
        .catch((err) => {
          console.error(err);
        });
    }
  };

  const handleTypeChange = (newType: string) => {
    let defaultValue: any;
    let value: any;

    switch (newType) {
      case "number":
        defaultValue = 0;
        value = 0;
        break;
      case "boolean":
        defaultValue = false;
        value = false;
        break;
      case "object":
      case "array":
        defaultValue = null;
        value = null;
        break;
      default: // string
        defaultValue = "";
        value = "";
    }

    setNewConfigData({
      ...newConfigData,
      type: newType,
      defaultValue,
      value,
    });
  };

  const addFieldToObject = (path: string[], field: NewObjectField) => {
    setObjectFields((prev) => {
      try {
        const newFields = { ...prev };
        let current = newFields;

        // 遍历路径找到正确的嵌套位置
        for (const pathKey of path) {
          if (!current[pathKey]) {
            current[pathKey] = {
              type: "object",
              description: "",
              defaultValue: {},
              value: {},
            };
          }
          current = current[pathKey].defaultValue;
        }

        // 添加新字段
        if (field.fieldConfig.type === "object") {
          current[field.fieldName] = {
            type: "object",
            description: field.fieldConfig.description,
            defaultValue: {},
            value: {},
          };
        } else if (field.fieldConfig.type === "array") {
          current[field.fieldName] = {
            type: "array",
            description: field.fieldConfig.description,
            defaultValue: [],
            value: [],
          };
        } else {
          current[field.fieldName] = {
            type: field.fieldConfig.type,
            description: field.fieldConfig.description,
            defaultValue: field.fieldConfig.defaultValue,
            value: field.fieldConfig.value,
          };
        }

        return newFields;
      } catch (error) {
        console.error("Error adding field to object:", error);
        return prev;
      }
    });
  };

  const handleAddField = () => {
    const fieldName = newFieldData.fieldName.trim();
    if (!fieldName) {
      return;
    }

    // 检查字段名是否已存在
    let current = objectFields;
    for (const pathKey of currentPath) {
      if (!current[pathKey]?.defaultValue) {
        console.error("Invalid path:", currentPath);
        return;
      }
      current = current[pathKey].defaultValue;
    }

    if (current[fieldName]) {
      console.warn("Field name already exists");
      return;
    }

    addFieldToObject(currentPath, {
      fieldName: fieldName,
      fieldConfig: {
        ...newFieldData.fieldConfig,
        defaultValue:
          newFieldData.fieldConfig.type === "object"
            ? {}
            : newFieldData.fieldConfig.defaultValue,
        value:
          newFieldData.fieldConfig.type === "object"
            ? {}
            : newFieldData.fieldConfig.value,
      },
    });

    if (newFieldData.fieldConfig.type === "object") {
      setCurrentPath((prev) => [...prev, fieldName]);
    }

    // 重置字段数据
    setNewFieldData({
      fieldName: "",
      fieldConfig: {
        type: "string",
        description: "",
        defaultValue: "",
        value: "",
      },
    });
  };

  const handleFinishObject = () => {
    if (Object.keys(objectFields).length === 0) {
      return;
    }

    try {
      const configData = {
        type: "object",
        description: newConfigData.description,
        defaultValue: objectFields,
        value: objectFields, // 初始时 value 与 defaultValue 相同
      };

      const selectedPlugin = pluginInfoList[selectedTab].identifier;
      addPluginConfig(selectedPlugin, addingConfigPath, configData)
        .then((success) => {
          if (success) {
            setIsAddDialogOpen(false);
            setObjectFields({});
            setCurrentPath([]);
            reloadPluginConfig();
          }
        })
        .catch((err) => {
          console.error("Error adding plugin config:", err);
        });
    } catch (error) {
      console.error("Error creating object config:", error);
    }
  };

  const formatObjectStructure = (fields: {
    [key: string]: ConfigField;
  }): any => {
    const result: any = {};
    for (const [key, field] of Object.entries(fields)) {
      if (field.type === "object") {
        result[key] = formatObjectStructure(field.defaultValue);
      } else {
        result[key] = {
          type: field.type,
          description: field.description,
          value: field.defaultValue,
        };
      }
    }
    return result;
  };

  const handleCloseDialog = () => {
    setIsAddDialogOpen(false);
    setObjectFields({});
    setCurrentPath([]);
    setNewFieldData({
      fieldName: "",
      fieldConfig: {
        type: "string",
        description: "",
        defaultValue: "",
        value: "",
      },
    });
  };

  const getCurrentLevelFields = (
    fields: {
      [key: string]: ConfigField;
    },
    path: string[]
  ): string[] => {
    let current = fields;
    for (const key of path) {
      if (!current[key]?.defaultValue) return [];
      current = current[key].defaultValue;
    }
    return Object.keys(current);
  };

  const getFieldByPath = (
    fields: {
      [key: string]: ConfigField;
    },
    path: string[]
  ): ConfigField | null => {
    let current = fields;
    for (let i = 0; i < path.length - 1; i++) {
      if (!current[path[i]]?.defaultValue) return null;
      current = current[path[i]].defaultValue;
    }
    return current[path[path.length - 1]] || null;
  };

  const handleDeleteField = (fieldName: string) => {
    setObjectFields((prev) => {
      const newFields = { ...prev };
      let current = newFields;

      // 导航到正确的嵌套层级
      for (const pathKey of currentPath) {
        if (!current[pathKey]?.defaultValue) return prev;
        current = current[pathKey].defaultValue;
      }

      // 删除字段
      delete current[fieldName];
      return newFields;
    });
  };

  const handleEditJson = () => {
    const selectedPlugin = pluginInfoList[selectedTab].identifier;
    getPluginConfigFilePath(selectedPlugin).then((filePath) => {
      console.log("Open config file", filePath);
      showItemInFolder(filePath);
    });
  };

  const handleOpenEntryPage = () => {
    const currentPluginInfo = pluginInfoList[selectedTab];
    if (currentPluginInfo?.entryPage) {
      const entryPageTitle = `entryPage-${currentPluginInfo.identifier}`;
      WebviewWindow.getByLabel(entryPageTitle)
        .then((window) => {
          window.unminimize();
          window.setFocus();
        })
        .catch(() => {
          const webview = new WebviewWindow(entryPageTitle, {
            url: currentPluginInfo.entryPage,
            width: 800,
            height: 600,
            center: true,
            title: currentPluginInfo.name,
          });

          webview.once("tauri://created", function () {
            // webview window successfully created
          });
          webview.once("tauri://error", function (e) {
            console.error("Error creating entry page window:", e);
          });
        });
    }
  };

  const handleCheckUpdate = async () => {
    if (isCheckingUpdate) return;

    try {
      setIsCheckingUpdate(true);
      setUpdateInfo(null);

      const selectedPlugin = pluginInfoList[selectedTab];

      // 如果已经检查过，直接使用缓存的状态
      if (pluginUpdateStatus[selectedPlugin.identifier]?.checked) {
        const status = pluginUpdateStatus[selectedPlugin.identifier];
        if (status.hasUpdate) {
          setUpdateInfo({
            status: "available",
            version: status.latestVersion,
            pluginUrl: status.pluginUrl,
          });
          setSnackbarMessage(
            t("pluginSettings.updatePrompt", { version: status.latestVersion })
          );
          setSnackbarSeverity("info");
          setSnackbarOpen(true);
        } else {
          setUpdateInfo({ status: "latest" });
          setSnackbarMessage(t("pluginSettings.noUpdateAvailable"));
          setSnackbarSeverity("success");
          setSnackbarOpen(true);
        }
        return;
      }

      const pluginInfo = await supabasePluginApi.getByIdentifier(
        selectedPlugin.identifier
      );

      if (!pluginInfo) {
        setUpdateInfo({ status: "error" });
        setSnackbarMessage(t("pluginSettings.updateError"));
        setSnackbarSeverity("error");
        setSnackbarOpen(true);
        return;
      }

      const currentVersion = selectedPlugin.version;
      const latestVersion = pluginInfo.latest_version;

      let hasUpdate = false;
      if (
        latestVersion &&
        ((semver.valid(latestVersion) &&
          semver.valid(currentVersion) &&
          semver.gt(latestVersion, currentVersion)) ||
          currentVersion !== latestVersion)
      ) {
        hasUpdate = true;
      }

      // 缓存检查结果
      setPluginUpdateStatus((prev) => ({
        ...prev,
        [selectedPlugin.identifier]: {
          checked: true,
          hasUpdate,
          latestVersion,
          pluginUrl: `https://aiverything.me/market/${pluginInfo.id}`,
        },
      }));

      if (hasUpdate) {
        setUpdateInfo({
          status: "available",
          version: latestVersion,
          pluginUrl: `https://aiverything.me/market/${pluginInfo.id}`,
        });
        setSnackbarMessage(
          t("pluginSettings.updatePrompt", { version: latestVersion })
        );
        setSnackbarSeverity("info");
        setSnackbarOpen(true);
      } else {
        setUpdateInfo({ status: "latest" });
        setSnackbarMessage(t("pluginSettings.noUpdateAvailable"));
        setSnackbarSeverity("success");
        setSnackbarOpen(true);
      }
    } catch (error) {
      console.error("Error checking for updates:", error);
      setUpdateInfo({ status: "error" });
      setSnackbarMessage(t("pluginSettings.updateError"));
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsCheckingUpdate(false);
    }
  };

  const handleOpenUpdateWebsite = () => {
    // 如果有可用更新并且有网址，则打开官网
    if (updateInfo?.status === "available" && updateInfo.pluginUrl) {
      open(updateInfo.pluginUrl);
    } else {
      // 否则打开默认的插件市场页面
      open("https://aiverything.me/market");
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  const checkAllPluginsUpdate = async () => {
    setSnackbarMessage(t("pluginSettings.updateAllPlugins"));
    setSnackbarSeverity("info");
    setSnackbarOpen(true);

    let hasUpdates = false;
    const newUpdateStatus = { ...pluginUpdateStatus };

    for (const plugin of pluginInfoList) {
      try {
        const pluginInfo = await supabasePluginApi.getByIdentifier(
          plugin.identifier
        );

        if (!pluginInfo) continue;

        const currentVersion = plugin.version;
        const latestVersion = pluginInfo.latest_version;

        const hasUpdate =
          latestVersion &&
          ((semver.valid(latestVersion) &&
            semver.valid(currentVersion) &&
            semver.gt(latestVersion, currentVersion)) ||
            currentVersion !== latestVersion);

        if (hasUpdate) {
          hasUpdates = true;
        }

        // 缓存每个插件的检查结果
        newUpdateStatus[plugin.identifier] = {
          checked: true,
          hasUpdate,
          latestVersion,
          pluginUrl: `https://aiverything.me/market/${pluginInfo.id}`,
        };
      } catch (error) {
        console.error(
          `Error checking updates for plugin ${plugin.name}:`,
          error
        );
      }
    }

    // 更新所有插件的状态缓存
    setPluginUpdateStatus(newUpdateStatus);

    if (hasUpdates) {
      setSnackbarMessage(t("pluginSettings.updateAvailableMultiple"));
      setSnackbarSeverity("info");
    } else {
      setSnackbarMessage(t("pluginSettings.noUpdateAvailable"));
      setSnackbarSeverity("success");
    }
    setSnackbarOpen(true);
  };

  // 在组件加载时检查所有插件更新
  useEffect(() => {
    if (Object.keys(pluginUpdateStatus).length === 0) {
      checkAllPluginsUpdate();
    }
  }, []);

  // 当选择的插件改变时，更新当前插件的状态
  useEffect(() => {
    const selectedPlugin = pluginInfoList[selectedTab];
    const status = pluginUpdateStatus[selectedPlugin.identifier];

    if (status?.checked) {
      if (status.hasUpdate) {
        setUpdateInfo({
          status: "available",
          version: status.latestVersion,
          pluginUrl: status.pluginUrl,
        });
      } else {
        setUpdateInfo({ status: "latest" });
      }
    } else {
      setUpdateInfo(null);
    }
  }, [selectedTab, pluginUpdateStatus]);

  return (
    <Box sx={{ display: "flex", height: "100vh" }}>
      {/* 左侧插件列表 */}
      <Box
        sx={{
          width: 200,
          borderRight: 1,
          borderColor: "divider",
          bgcolor: "background.paper",
          boxShadow: "4px 0 8px rgba(0,0,0,0.05)",
          overflow: "auto",
          height: "100vh",
        }}
      >
        <Tabs
          orientation="vertical"
          value={selectedTab}
          onChange={(_, newValue) => {
            // 如果点击的是最后一个标签（插件商店）
            if (newValue === pluginInfoList.length) {
              open("https://aiverything.me/market");
              return;
            }
            setSelectedTab(newValue);
          }}
          sx={{
            "& .MuiTab-root": {
              alignItems: "flex-start",
              minHeight: 48,
              padding: 1,
              transition: "all 0.2s",
              "&:hover": {
                bgcolor: "action.hover",
              },
              "&.Mui-selected": {
                bgcolor: "primary.dark",
                color: "common.white",
                "& .MuiTypography-root": {
                  color: "common.white",
                },
                "& .MuiTypography-caption": {
                  color: "rgba(255, 255, 255, 0.7)",
                },
              },
            },
          }}
        >
          {pluginInfoList.map((plugin) => (
            <Tab
              key={plugin.identifier}
              icon={
                <Box sx={{ display: "flex", alignItems: "center", mb: 0.5 }}>
                  <img
                    src={plugin.icon}
                    alt={plugin.name}
                    style={{
                      width: 24,
                      height: 24,
                      borderRadius: "4px",
                      filter: "drop-shadow(0 2px 4px rgba(0,0,0,0.1))",
                    }}
                  />
                </Box>
              }
              label={
                <Box sx={{ textAlign: "left", width: "100%" }}>
                  <Typography
                    variant="subtitle2"
                    fontWeight="600"
                    sx={{
                      mb: 0.25,
                      fontSize: "0.8rem",
                    }}
                  >
                    {plugin.name}
                  </Typography>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{
                      display: "-webkit-box",
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: "vertical",
                      overflow: "hidden",
                      fontSize: "0.65rem",
                      lineHeight: 1.2,
                    }}
                  >
                    {plugin.description}
                  </Typography>
                </Box>
              }
            />
          ))}
          {/* 插件商店标签 */}
          <Tab
            icon={
              <StoreIcon
                sx={{ width: 24, height: 24, color: "primary.main" }}
              />
            }
            label={
              <Box sx={{ textAlign: "left", width: "100%" }}>
                <Typography
                  variant="subtitle2"
                  fontWeight="600"
                  sx={{ mb: 0.5 }}
                >
                  {t("pluginSettings.store.title")}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {t("pluginSettings.store.description")}
                </Typography>
              </Box>
            }
          />
        </Tabs>
      </Box>

      {/* 右侧设置内容 */}
      <Box
        sx={{
          flex: 1,
          p: 2,
          overflow: "auto",
          height: "100vh",
          bgcolor: "background.default",
        }}
      >
        <Typography
          variant="h6"
          sx={{
            mb: 2,
            fontWeight: 600,
            background: "linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}
        >
          {t("pluginSettings.settings", {
            pluginName: pluginInfoList[selectedTab].name,
          })}
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          {" "}
          <Typography variant="subtitle2" color="text.secondary" sx={{ mr: 1 }}>
            {" "}
            {t("pluginSettings.version")}:{" "}
          </Typography>{" "}
          <Chip
            label={pluginInfoList[selectedTab].version}
            size="small"
            color="primary"
            variant="outlined"
            sx={{ borderRadius: 1, "& .MuiChip-label": { px: 1, py: 0.25 } }}
          />{" "}
        </Box>{" "}
        <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={handleEditJson}
            sx={{
              borderRadius: 2,
              textTransform: "none",
              fontWeight: 500,
              transition: "all 0.2s",
              "&:hover": {
                transform: "translateY(-2px)",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
              },
            }}
          >
            {t("pluginSettings.editJson")}
          </Button>

          <Button
            variant="outlined"
            startIcon={<OpenInNewIcon />}
            onClick={handleOpenEntryPage}
            sx={{
              borderRadius: 2,
              textTransform: "none",
              fontWeight: 500,
              transition: "all 0.2s",
              "&:hover": {
                transform: "translateY(-2px)",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
              },
            }}
          >
            {t("pluginSettings.openEntryPage")}
          </Button>

          <Button
            variant="outlined"
            startIcon={<SystemUpdateIcon />}
            onClick={
              updateInfo?.status === "available"
                ? handleOpenUpdateWebsite
                : handleCheckUpdate
            }
            disabled={isCheckingUpdate}
            sx={{
              borderRadius: 2,
              textTransform: "none",
              fontWeight: 500,
              transition: "all 0.2s",
              "&:hover": {
                transform: "translateY(-2px)",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
              },
              ...(updateInfo?.status === "available"
                ? {
                    bgcolor: "primary.main",
                    color: "common.white",
                    "&:hover": {
                      bgcolor: "primary.dark",
                      transform: "translateY(-2px)",
                      boxShadow: "0 4px 8px rgba(33, 150, 243, 0.3)",
                    },
                  }
                : {}),
            }}
          >
            {isCheckingUpdate
              ? t("pluginSettings.checking")
              : updateInfo?.status === "available"
              ? t("pluginSettings.clickToUpdate")
              : t("pluginSettings.checkUpdate")}
          </Button>
        </Stack>
        {currentPluginConfig && (
          <Box sx={{ bgcolor: "background.paper", p: 2, borderRadius: 2 }}>
            <ConfigFieldRenderer
              rawConfig={currentPluginConfig.rawConfig}
              onChange={(path, newConfig) => {
                console.log(`Updating config, path: ${path}`, newConfig);
                const selectedPlugin = pluginInfoList[selectedTab].identifier;
                setPluginConfig(selectedPlugin, path, newConfig)
                  .then((success) => {
                    if (success) {
                      reloadPluginConfig();
                    }
                  })
                  .catch((err) => {
                    console.error(err);
                  });
              }}
              onDelete={(path) => {
                console.log(`Deleting config, path: ${path}`);
                const selectedPlugin = pluginInfoList[selectedTab].identifier;
                deletePluginConfig(selectedPlugin, path)
                  .then((success) => {
                    if (success) {
                      reloadPluginConfig();
                    }
                  })
                  .catch((err) => {
                    console.error(err);
                  });
              }}
              onAdd={(path) => {
                setAddingConfigPath(path);
                setIsAddDialogOpen(true);
              }}
            />
          </Box>
        )}
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {
            setIsAddDialogOpen(true);
            setNewConfigData({
              type: "string",
              description: "",
              defaultValue: "",
              value: "",
            });
          }}
          sx={{
            mt: 3,
            borderRadius: 2,
            textTransform: "none",
            fontWeight: 500,
            background: "linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)",
            boxShadow: "0 3px 5px 2px rgba(33, 203, 243, .3)",
            transition: "all 0.2s",
            "&:hover": {
              background: "linear-gradient(45deg, #1976D2 30%, #00B4D8 90%)",
              transform: "translateY(-2px)",
            },
          }}
        >
          {t("pluginSettings.buttons.add")}
        </Button>
        <Dialog
          open={isAddDialogOpen}
          onClose={handleCloseDialog}
          PaperProps={{
            sx: {
              borderRadius: 3,
              boxShadow: "0 8px 32px rgba(0,0,0,0.1)",
              p: 2,
              bgcolor: "background.paper",
            },
          }}
        >
          <DialogTitle>{t("pluginSettings.dialog.title")}</DialogTitle>
          <DialogContent>
            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel>{t("pluginSettings.dialog.type")}</InputLabel>
              <Select
                value={newConfigData.type}
                label={t("pluginSettings.dialog.type")}
                onChange={(e) => handleTypeChange(e.target.value)}
              >
                <MenuItem value="string">
                  {t("pluginSettings.types.string")}
                </MenuItem>
                <MenuItem value="number">
                  {t("pluginSettings.types.number")}
                </MenuItem>
                <MenuItem value="boolean">
                  {t("pluginSettings.types.boolean")}
                </MenuItem>
                <MenuItem value="object">
                  {t("pluginSettings.types.object")}
                </MenuItem>
                <MenuItem value="array">
                  {t("pluginSettings.types.array")}
                </MenuItem>
              </Select>
            </FormControl>

            <TextField
              fullWidth
              margin="normal"
              label={t("pluginSettings.dialog.description")}
              value={newConfigData.description}
              onChange={(e) =>
                setNewConfigData({
                  ...newConfigData,
                  description: e.target.value,
                })
              }
              multiline={newConfigData.description.length > 50}
              rows={newConfigData.description.length > 50 ? 3 : 1}
            />

            {newConfigData.type === "object" && (
              <>
                <Box sx={{ mt: 2, mb: 2 }}>
                  <Box
                    sx={{
                      mb: 2,
                      display: "flex",
                      alignItems: "center",
                      flexWrap: "wrap",
                      gap: 1,
                    }}
                  >
                    <Button
                      size="small"
                      onClick={() => setCurrentPath([])}
                      variant={
                        currentPath.length === 0 ? "contained" : "outlined"
                      }
                    >
                      {t("pluginSettings.root")}
                    </Button>
                    {currentPath.map((path, index) => (
                      <React.Fragment key={path}>
                        <Typography variant="body2" color="textSecondary">
                          /
                        </Typography>
                        <Button
                          size="small"
                          onClick={() =>
                            setCurrentPath(currentPath.slice(0, index + 1))
                          }
                          variant={
                            index === currentPath.length - 1
                              ? "contained"
                              : "outlined"
                          }
                        >
                          {path}
                        </Button>
                      </React.Fragment>
                    ))}
                  </Box>

                  <Typography variant="subtitle1">
                    {currentPath.length > 0
                      ? t("pluginSettings.addingFieldsTo", {
                          path: currentPath.join("."),
                        })
                      : t("pluginSettings.objectField.addFields")}
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    <Typography
                      variant="subtitle2"
                      color="textSecondary"
                      sx={{ mb: 1 }}
                    >
                      {t("pluginSettings.currentLevelFields")}
                    </Typography>
                    {getCurrentLevelFields(objectFields, currentPath).map(
                      (fieldName) => (
                        <Chip
                          key={fieldName}
                          label={fieldName}
                          onClick={() => {
                            const field = getFieldByPath(objectFields, [
                              ...currentPath,
                              fieldName,
                            ]);
                            if (field?.type === "object") {
                              setCurrentPath([...currentPath, fieldName]);
                            }
                          }}
                          onDelete={() => handleDeleteField(fieldName)}
                          sx={{ m: 0.5 }}
                        />
                      )
                    )}
                  </Box>

                  <TextField
                    fullWidth
                    margin="normal"
                    label={t("pluginSettings.fieldName")}
                    value={newFieldData.fieldName}
                    onChange={(e) =>
                      setNewFieldData({
                        ...newFieldData,
                        fieldName: e.target.value,
                      })
                    }
                  />

                  <FormControl fullWidth sx={{ mt: 2 }}>
                    <InputLabel>{t("pluginSettings.fieldType")}</InputLabel>
                    <Select
                      value={newFieldData.fieldConfig.type}
                      label={t("pluginSettings.fieldType")}
                      onChange={(e) => {
                        const type = e.target.value;
                        let defaultValue: any = "";
                        let value: any = "";

                        switch (type) {
                          case "number":
                            defaultValue = 0;
                            value = 0;
                            break;
                          case "boolean":
                            defaultValue = false;
                            value = false;
                            break;
                          case "object":
                            defaultValue = {};
                            value = {};
                            break;
                          case "array":
                            defaultValue = [];
                            value = [];
                            break;
                        }

                        setNewFieldData({
                          ...newFieldData,
                          fieldConfig: {
                            ...newFieldData.fieldConfig,
                            type,
                            defaultValue,
                            value,
                          },
                        });
                      }}
                    >
                      <MenuItem value="string">
                        {t("pluginSettings.types.string")}
                      </MenuItem>
                      <MenuItem value="number">
                        {t("pluginSettings.types.number")}
                      </MenuItem>
                      <MenuItem value="boolean">
                        {t("pluginSettings.types.boolean")}
                      </MenuItem>
                      <MenuItem value="object">
                        {t("pluginSettings.types.object")}
                      </MenuItem>
                      <MenuItem value="array">
                        {t("pluginSettings.types.array")}
                      </MenuItem>
                    </Select>
                  </FormControl>

                  <TextField
                    fullWidth
                    margin="normal"
                    label={t("pluginSettings.fieldDescription")}
                    value={newFieldData.fieldConfig.description}
                    onChange={(e) =>
                      setNewFieldData({
                        ...newFieldData,
                        fieldConfig: {
                          ...newFieldData.fieldConfig,
                          description: e.target.value,
                        },
                      })
                    }
                    multiline={newFieldData.fieldConfig.description.length > 50}
                    rows={
                      newFieldData.fieldConfig.description.length > 50 ? 3 : 1
                    }
                  />

                  {newFieldData.fieldConfig.type !== "object" &&
                    newFieldData.fieldConfig.type !== "array" && (
                      <>
                        {newFieldData.fieldConfig.type === "boolean" ? (
                          <Box sx={{ mt: 2, mb: 1 }}>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={
                                    newFieldData.fieldConfig.defaultValue
                                  }
                                  onChange={(e) =>
                                    setNewFieldData({
                                      ...newFieldData,
                                      fieldConfig: {
                                        ...newFieldData.fieldConfig,
                                        defaultValue: e.target.checked,
                                        value: e.target.checked,
                                      },
                                    })
                                  }
                                />
                              }
                              label={t("pluginSettings.defaultValue")}
                              sx={{
                                display: "block",
                                ml: 0,
                                "& .MuiFormControlLabel-label": {
                                  ml: 1,
                                },
                              }}
                            />
                            <Typography
                              variant="caption"
                              color="textSecondary"
                              sx={{ ml: 4 }}
                            >
                              {t("pluginSettings.booleanField.description")}
                            </Typography>
                          </Box>
                        ) : (
                          <TextField
                            fullWidth
                            margin="normal"
                            label={t("pluginSettings.defaultValue")}
                            type={
                              newFieldData.fieldConfig.type === "number"
                                ? "number"
                                : "text"
                            }
                            value={newFieldData.fieldConfig.defaultValue || ""}
                            onChange={(e) =>
                              setNewFieldData({
                                ...newFieldData,
                                fieldConfig: {
                                  ...newFieldData.fieldConfig,
                                  defaultValue: e.target.value,
                                  value: e.target.value,
                                },
                              })
                            }
                          />
                        )}
                      </>
                    )}

                  <Button
                    variant="contained"
                    onClick={handleAddField}
                    sx={{ mt: 2 }}
                  >
                    {t("pluginSettings.buttons.addField")}
                  </Button>

                  {currentPath.length > 0 && (
                    <Button
                      sx={{ mt: 2, ml: 2 }}
                      onClick={() => {
                        setCurrentPath((prev) => prev.slice(0, -1));
                      }}
                    >
                      {t("pluginSettings.buttons.backToParent")}
                    </Button>
                  )}
                </Box>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle1">
                  {t("pluginSettings.currentObjectStructure")}
                </Typography>
                <Box
                  component="pre"
                  sx={{
                    maxHeight: "200px",
                    overflow: "auto",
                    backgroundColor: "background.paper",
                    p: 2,
                    borderRadius: 1,
                    "& .highlight": {
                      backgroundColor: "warning.light",
                      px: 1,
                      borderRadius: 0.5,
                    },
                  }}
                >
                  {Object.entries(objectFields).map(([key, field], index) => {
                    const nestedContent = (
                      obj: any,
                      path: string[] = []
                    ): JSX.Element => {
                      if (typeof obj !== "object" || obj === null) {
                        return <span>{JSON.stringify(obj)}</span>;
                      }

                      if (obj.type && obj.type !== "object") {
                        // 处理基础类型（字符串、数字等）
                        const isHighlighted =
                          currentPath.join(".") === path.join(".");
                        return (
                          <span className={isHighlighted ? "highlight" : ""}>
                            {JSON.stringify(
                              {
                                type: obj.type,
                                description: obj.description,
                                defaultValue: obj.defaultValue,
                                value: obj.value,
                              },
                              null,
                              2
                            )}
                          </span>
                        );
                      }

                      return (
                        <span>
                          {"{"}
                          {Object.entries(obj.defaultValue || obj).map(
                            ([nestedKey, nestedValue], i) => {
                              const newPath = [...path, nestedKey];
                              const isNestedHighlighted =
                                currentPath.join(".") === newPath.join(".");
                              return (
                                <div key={nestedKey} style={{ marginLeft: 20 }}>
                                  <span
                                    className={
                                      isNestedHighlighted ? "highlight" : ""
                                    }
                                  >
                                    "{nestedKey}":{" "}
                                    {typeof nestedValue === "object" &&
                                    nestedValue !== null
                                      ? nestedContent(nestedValue, newPath)
                                      : JSON.stringify(nestedValue)}
                                  </span>
                                  {i <
                                  Object.entries(obj.defaultValue || obj)
                                    .length -
                                    1
                                    ? ","
                                    : ""}
                                </div>
                              );
                            }
                          )}
                          {"}"}
                        </span>
                      );
                    };

                    const isHighlighted =
                      currentPath.length === 1 && currentPath[0] === key;
                    return (
                      <div key={key}>
                        <span className={isHighlighted ? "highlight" : ""}>
                          "{key}": {nestedContent(field, [key])}
                        </span>
                        {index < Object.entries(objectFields).length - 1
                          ? ","
                          : ""}
                      </div>
                    );
                  })}
                </Box>
              </>
            )}

            {newConfigData.type !== "object" && (
              <>
                {newConfigData.type === "boolean" ? (
                  <>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={newConfigData.defaultValue}
                          onChange={(e) =>
                            setNewConfigData({
                              ...newConfigData,
                              defaultValue: e.target.checked,
                            })
                          }
                        />
                      }
                      label={
                        <Stack spacing={0}>
                          <Typography>
                            {t("pluginSettings.booleanField.defaultValue")}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {t("pluginSettings.booleanField.description")}
                          </Typography>
                        </Stack>
                      }
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={newConfigData.value}
                          onChange={(e) =>
                            setNewConfigData({
                              ...newConfigData,
                              value: e.target.checked,
                            })
                          }
                        />
                      }
                      label={
                        <Stack spacing={0}>
                          <Typography>
                            {t("pluginSettings.booleanField.currentValue")}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {t("pluginSettings.booleanField.description")}
                          </Typography>
                        </Stack>
                      }
                    />
                  </>
                ) : (
                  <>
                    <TextField
                      fullWidth
                      margin="normal"
                      label={t("pluginSettings.defaultValue")}
                      type={newConfigData.type === "number" ? "number" : "text"}
                      value={newConfigData.defaultValue || ""}
                      onChange={(e) => {
                        const value =
                          newConfigData.type === "number"
                            ? Number(e.target.value)
                            : e.target.value;
                        setNewConfigData({
                          ...newConfigData,
                          defaultValue: value,
                        });
                      }}
                      multiline={
                        typeof newConfigData.defaultValue === "string" &&
                        newConfigData.defaultValue.length > 50
                      }
                      rows={
                        typeof newConfigData.defaultValue === "string" &&
                        newConfigData.defaultValue.length > 50
                          ? 3
                          : 1
                      }
                    />

                    <TextField
                      fullWidth
                      margin="normal"
                      label={t("pluginSettings.currentValue")}
                      type={newConfigData.type === "number" ? "number" : "text"}
                      value={newConfigData.value || ""}
                      onChange={(e) => {
                        const value =
                          newConfigData.type === "number"
                            ? Number(e.target.value)
                            : e.target.value;
                        setNewConfigData({
                          ...newConfigData,
                          value: value,
                        });
                      }}
                      multiline={
                        typeof newConfigData.value === "string" &&
                        newConfigData.value.length > 50
                      }
                      rows={
                        typeof newConfigData.value === "string" &&
                        newConfigData.value.length > 50
                          ? 3
                          : 1
                      }
                    />
                  </>
                )}
              </>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>
              {t("pluginSettings.buttons.cancel")}
            </Button>
            <Button
              onClick={
                newConfigData.type === "object"
                  ? handleFinishObject
                  : handleAddConfig
              }
              variant="contained"
              disabled={
                newConfigData.type === "object" &&
                Object.keys(objectFields).length === 0
              }
            >
              {t("pluginSettings.buttons.add")}
            </Button>
          </DialogActions>
        </Dialog>
        {/* 提示消息 */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        >
          <Alert
            onClose={handleCloseSnackbar}
            severity={snackbarSeverity}
            sx={{ width: "100%" }}
          >
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </Box>
    </Box>
  );
};

export default PluginSettingsContent;
