use serde_json::Value;

use crate::api::plugin::PluginApi;

const PLUGIN_PORT: &str = "38884";
const PLUGIN_URL: &str = "https://localhost:";

lazy_static::lazy_static! {
    static ref plugin_api: PluginApi = PluginApi::new((PLUGIN_URL.to_string() + PLUGIN_PORT).as_str())
    .expect("Failed to create API instance");
}

#[tauri::command]
pub async fn get_plugin_list(plugin_name: String) -> Result<Value, String> {
    match plugin_api.get_plugin_list(&plugin_name).await {
        Ok(plugins) => Ok(plugins),
        Err(err) => Err(err.to_string()),
    }
}

#[tauri::command]
pub async fn get_plugin_info(plugin_identifier: String, locale: String) -> Result<Value, String> {
    match plugin_api
        .get_plugin_info(&plugin_identifier, &locale)
        .await
    {
        Ok(plugin_info) => Ok(plugin_info),
        Err(err) => Err(err.to_string()),
    }
}

#[tauri::command]
pub async fn get_plugin_config_value(plugin_identifier: String) -> Result<Value, String> {
    match plugin_api.get_plugin_config_value(&plugin_identifier).await {
        Ok(plugin_config_value) => Ok(plugin_config_value),
        Err(err) => Err(err.to_string()),
    }
}

#[tauri::command]
pub async fn get_plugin_config_raw(plugin_identifier: String) -> Result<Value, String> {
    match plugin_api.get_plugin_config_raw(&plugin_identifier).await {
        Ok(plugin_config_raw) => Ok(plugin_config_raw),
        Err(err) => Err(err.to_string()),
    }
}

#[tauri::command]
pub async fn get_plugin_config_file_path(plugin_identifier: String) -> Result<String, String> {
    let current_dir = std::env::current_dir().unwrap();
    let core_dir;
    if cfg!(debug_assertions) {
        core_dir = current_dir.join("../core");
    } else {
        core_dir = current_dir.join("core");
    }
    let plugin_config_file_path = core_dir
        .join("plugins")
        .join("plugin configs")
        .join(plugin_identifier)
        .join("settings.json");
    Ok(plugin_config_file_path
        .canonicalize()
        .unwrap()
        .to_string_lossy()
        .to_string())
}

#[tauri::command]
pub async fn get_plugin_resource_url(
    plugin_identifier: String,
    resource_name: String,
) -> Result<String, String> {
    return Ok(PLUGIN_URL.to_string()
        + PLUGIN_PORT
        + "/plugin/static/"
        + &plugin_identifier
        + "/"
        + &resource_name);
}

#[tauri::command]
pub async fn get_plugin_load_error() -> Result<Value, String> {
    match plugin_api.get_plugin_load_error().await {
        Ok(plugin_load_error) => Ok(plugin_load_error),
        Err(err) => Err(err.to_string()),
    }
}

#[tauri::command]
pub async fn plugin_enter(plugin_identifier: String) -> Result<Value, String> {
    match plugin_api.plugin_enter(&plugin_identifier).await {
        Ok(plugin_enter) => Ok(plugin_enter),
        Err(err) => Err(err.to_string()),
    }
}

#[tauri::command]
pub async fn plugin_exit(plugin_identifier: String) -> Result<Value, String> {
    match plugin_api.plugin_exit(&plugin_identifier).await {
        Ok(plugin_exit) => Ok(plugin_exit),
        Err(err) => Err(err.to_string()),
    }
}
