import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Typography,
  Link,
  List,
  ListItem,
  ListItemText,
  Divider,
  Paper,
  Button,
  CircularProgress,
  Snackbar,
  Alert,
} from "@mui/material";
import logo from "../../assets/logo.png";
import { getVersion } from "@tauri-apps/api/app";
import { check } from "@tauri-apps/plugin-updater";
import { relaunch } from "@tauri-apps/plugin-process";
import SystemUpdateIcon from "@mui/icons-material/SystemUpdate";

interface OpenSourceProject {
  name: string;
  description: string;
  link: string;
  license: string;
}

const openSourceProjects: OpenSourceProject[] = [
  // 前端核心框架
  {
    name: "React",
    description: "A JavaScript library for building user interfaces",
    link: "https://reactjs.org",
    license: "MIT",
  },
  {
    name: "Material-UI",
    description: "React components for faster and easier web development",
    link: "https://mui.com",
    license: "MIT",
  },
  {
    name: "<PERSON><PERSON>",
    description: "Build smaller, faster, and more secure desktop applications",
    link: "https://tauri.app",
    license: "MIT/Apache-2.0",
  },
  // 后端核心框架
  {
    name: "Spring Boot",
    description: "Framework for creating production-grade Spring applications",
    link: "https://spring.io/projects/spring-boot",
    license: "Apache-2.0",
  },
  {
    name: "MyBatis",
    description: "SQL mapper framework for Java",
    link: "https://mybatis.org",
    license: "Apache-2.0",
  },
  {
    name: "MyBatis-Plus",
    description: "Enhanced MyBatis SQL toolkit",
    link: "https://baomidou.com",
    license: "Apache-2.0",
  },
  {
    name: "File-Engine",
    description: "Light weight local file indexer and monitor library.",
    link: "https://github.com/XUANXUQAQ/File-Engine",
    license: "MIT",
  },
  // 搜索和文档处理
  {
    name: "Lucene",
    description: "High-performance, full-featured text search engine library",
    link: "https://lucene.apache.org",
    license: "Apache-2.0",
  },
  {
    name: "Apache POI",
    description: "Java API for Microsoft Documents (Word, Excel, etc.)",
    link: "https://poi.apache.org",
    license: "Apache-2.0",
  },
  {
    name: "PDFBox",
    description: "Java library for working with PDF documents",
    link: "https://pdfbox.apache.org",
    license: "Apache-2.0",
  },
  {
    name: "Apache Tika",
    description: "Content analysis and detection toolkit",
    link: "https://tika.apache.org",
    license: "Apache-2.0",
  },
  // 核心工具库
  {
    name: "Guava",
    description: "Google core libraries for Java",
    link: "https://github.com/google/guava",
    license: "Apache-2.0",
  },
  {
    name: "BouncyCastle",
    description: "Java cryptography APIs",
    link: "https://www.bouncycastle.org",
    license: "MIT",
  },
  {
    name: "Gson",
    description: "Java library for JSON serialization/deserialization",
    link: "https://github.com/google/gson",
    license: "Apache-2.0",
  },
  // 日志相关
  {
    name: "SLF4J",
    description: "Simple Logging Facade for Java",
    link: "https://www.slf4j.org",
    license: "MIT",
  },
  {
    name: "Log4j",
    description: "Logging library for Java",
    link: "https://logging.apache.org/log4j",
    license: "Apache-2.0",
  },
  // 网络和API
  {
    name: "Javalin",
    description: "Simple and modern Java and Kotlin web framework",
    link: "https://javalin.io",
    license: "Apache-2.0",
  },
  {
    name: "Reqwest",
    description: "An ergonomic, batteries-included HTTP Client for Rust",
    link: "https://github.com/seanmonstar/reqwest",
    license: "MIT/Apache-2.0",
  },
  {
    name: "SpringDoc OpenAPI",
    description: "OpenAPI 3 & Spring Boot integration",
    link: "https://springdoc.org",
    license: "Apache-2.0",
  },
  // 数据库和存储
  {
    name: "SQLite JDBC",
    description: "SQLite JDBC Driver for Java",
    link: "https://github.com/xerial/sqlite-jdbc",
    license: "Apache-2.0",
  },
  {
    name: "wxsqlite3",
    description: "wxSQLite3 - a lightweight wrapper for SQLite",
    link: "https://github.com/utelle/wxsqlite3",
    license: "LGPL-3.0",
  },
  // 系统和底层
  {
    name: "JNA",
    description: "Java Native Access library",
    link: "https://github.com/java-native-access/jna",
    license: "Apache-2.0",
  },
  {
    name: "Windows-RS",
    description: "Rust bindings for the Windows API",
    link: "https://github.com/microsoft/windows-rs",
    license: "MIT",
  },
  // 中文处理
  {
    name: "IK Analyzer",
    description: "Chinese tokenizer and analyzer for Lucene",
    link: "https://github.com/magese/ik-analyzer-solr",
    license: "Apache-2.0",
  },
  {
    name: "TinyPinyin",
    description: "Chinese character to Pinyin converter for Java",
    link: "https://github.com/promeG/TinyPinyin",
    license: "Apache-2.0",
  },
  // AI 和 OCR
  {
    name: "Ollama4j",
    description: "Java client for Ollama API",
    link: "https://github.com/ollama4j/ollama4j",
    license: "MIT",
  },
  {
    name: "Tess4J",
    description: "Java wrapper for Tesseract OCR API",
    link: "https://github.com/nguyenq/tess4j",
    license: "Apache-2.0",
  },
  // 其他工具库
  {
    name: "Serde",
    description:
      "A framework for serializing and deserializing Rust data structures",
    link: "https://serde.rs",
    license: "MIT/Apache-2.0",
  },
  {
    name: "Window Vibrancy",
    description: "A library for adding vibrancy effects to windows",
    link: "https://github.com/tauri-apps/window-vibrancy",
    license: "MIT",
  },
  {
    name: "Rcgen",
    description: "Generate X.509 certificates with pure Rust",
    link: "https://github.com/rustls/rcgen",
    license: "MIT/Apache-2.0",
  },
  {
    name: "Local Encoding",
    description: "Rust library for handling local character encodings",
    link: "https://github.com/hsivonen/encoding_rs",
    license: "MIT/Apache-2.0",
  },
  {
    name: "Config",
    description: "Layered configuration system for Rust applications",
    link: "https://github.com/mehcode/config-rs",
    license: "MIT",
  },
  {
    name: "Semver4j",
    description: "Semantic versioning for Java",
    link: "https://github.com/vdurmont/semver4j",
    license: "MIT",
  },
  {
    name: "BS::thread_pool",
    description:
      "BS::thread_pool: a fast, lightweight, modern, and easy-to-use C++17 / C++20 / C++23 thread pool library",
    link: "https://github.com/bshoshany/thread-pool",
    license: "MIT",
  },
  {
    name: "OpenCL-Wrapper",
    description: "OpenCL-Wrapper",
    link: "https://github.com/ProjectPhysX/OpenCL-Wrapper",
    license:
      "https://github.com/ProjectPhysX/OpenCL-Wrapper/blob/master/LICENSE.md",
  },
  {
    name: "emhash",
    description:
      "emhash - Fast and memory efficient open addressing C++ flat hash table/map",
    link: "https://github.com/ktprime/emhash",
    license: "MIT",
  },
  {
    name: "jsymspell",
    description: "JSymSpell is a zero-dependency Java 8+ port of SymSpell",
    link: "https://github.com/rxp90/jsymspell",
    license: "MIT",
  },
];

const isUrl = (str: string) => {
  try {
    new URL(str);
    return true;
  } catch {
    return false;
  }
};

export const AboutContent: React.FC = () => {
  const { t } = useTranslation();
  const [version, setVersion] = useState<string>("");
  const [checking, setChecking] = useState(false);
  const [downloading, setDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [updateVersion, setUpdateVersion] = useState("");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    getVersion().then((version) => {
      setVersion(version);
    });
  }, []);

  const handleCheckUpdate = async () => {
    try {
      setChecking(true);
      setError(null);
      const update = await check();

      if (update) {
        setUpdateAvailable(true);
        setUpdateVersion(update.version);
      } else {
        setError(t("settings.about.noUpdateAvailable"));
      }
    } catch (err) {
      setError(t("settings.about.checkUpdateError"));
      console.error("Update check failed:", err);
    } finally {
      setChecking(false);
    }
  };

  const handleUpdate = async () => {
    try {
      setDownloading(true);
      setError(null);
      const update = await check();

      if (update) {
        let downloaded = 0;
        let contentLength = 0;

        await update.downloadAndInstall((event) => {
          switch (event.event) {
            case "Started":
              contentLength = event.data.contentLength;
              break;
            case "Progress":
              downloaded += event.data.chunkLength;
              setDownloadProgress((downloaded / contentLength) * 100);
              break;
            case "Finished":
              setDownloadProgress(100);
              break;
          }
        });

        await relaunch();
      }
    } catch (err) {
      setError(t("settings.about.updateError"));
      console.error("Update failed:", err);
      setDownloading(false);
    }
  };

  const renderLicense = (license: string) => {
    if (isUrl(license)) {
      return (
        <Link href={license} target="_blank" rel="noopener" underline="hover">
          LICENSE
        </Link>
      );
    }
    return license;
  };

  return (
    <div className="p-8 flex flex-col items-center">
      <img src={logo} alt="Aiverything Logo" className="w-32 h-32 mb-6" />
      <Typography variant="h4" gutterBottom>
        Aiverything
      </Typography>

      <div className="flex items-center gap-4 mb-4">
        <Typography variant="subtitle1" color="textSecondary">
          {t("settings.about.version", { version })}
        </Typography>

        {!downloading && (
          <Button
            variant="contained"
            color="primary"
            size="small"
            onClick={checking ? undefined : handleCheckUpdate}
            startIcon={
              checking ? <CircularProgress size={20} /> : <SystemUpdateIcon />
            }
            disabled={checking || downloading}
          >
            {checking
              ? t("settings.about.checking")
              : t("settings.about.checkUpdate")}
          </Button>
        )}

        {updateAvailable && !downloading && (
          <Button
            variant="contained"
            color="secondary"
            size="small"
            onClick={handleUpdate}
            startIcon={<SystemUpdateIcon />}
          >
            {t("settings.about.installUpdate", { version: updateVersion })}
          </Button>
        )}

        {downloading && (
          <div className="flex items-center gap-2">
            <CircularProgress
              variant="determinate"
              value={downloadProgress}
              size={24}
            />
            <Typography variant="body2" color="textSecondary">
              {Math.round(downloadProgress)}%
            </Typography>
          </div>
        )}
      </div>

      <Typography variant="body1" className="mt-4 mb-8 text-center max-w-2xl">
        {t("settings.about.description")}
      </Typography>

      <div className="w-full max-w-2xl mt-8">
        <Typography variant="h6" gutterBottom className="mb-4">
          {t("settings.about.openSource")}
        </Typography>

        <Paper variant="outlined" className="mb-8">
          <List className="max-h-[300px] overflow-y-auto">
            {openSourceProjects.map((project, index) => (
              <React.Fragment key={project.name}>
                <ListItem>
                  <ListItemText
                    primary={
                      <Link
                        href={project.link}
                        target="_blank"
                        rel="noopener"
                        underline="hover"
                      >
                        {project.name}
                      </Link>
                    }
                    secondary={
                      <div>
                        <Typography variant="body2" color="textSecondary">
                          {project.description}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          License: {renderLicense(project.license)}
                        </Typography>
                      </div>
                    }
                  />
                </ListItem>
                {index < openSourceProjects.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </Paper>
      </div>

      <Typography variant="body2" color="textSecondary">
        {t("settings.about.copyright")}
      </Typography>

      <Snackbar
        open={error !== null}
        autoHideDuration={6000}
        onClose={() => setError(null)}
      >
        <Alert onClose={() => setError(null)} severity="error">
          {error}
        </Alert>
      </Snackbar>
    </div>
  );
};
