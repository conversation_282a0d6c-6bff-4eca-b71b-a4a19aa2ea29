﻿// dllmain.cpp : 定义 DLL 应用程序的入口点。
#include "pch.h"
#include <Windows.h>
#include <TlHelp32.h>
#include <tchar.h>
#include <thread>
#include <dwmapi.h>
#include "checkHwnd.h"
#include "getExplorerPath.h"
#include "quick_jump.h"
#include "string_to_utf8.h"
#pragma comment(lib, "dwmapi")
#pragma comment(lib, "user32")
#pragma comment(lib, "kernel32")


constexpr auto EXPLORER_MIN_HEIGHT = 200; //当窗口大小满足这些条件后才开始判断是否为explorer.exe
constexpr auto EXPLORER_MIN_WIDTH = 200;

constexpr auto G_DIALOG = 1;
constexpr auto G_EXPLORER = 2;

bool is_explorer_window_at_top = false;
bool is_mouse_click_out_of_explorer = false;
bool is_running = false;
int explorer_x = 0;
int explorer_y = 0;
long explorer_width = 0;
long explorer_height = 0;
int top_window_type = 0;
HWND current_attach_explorer = nullptr;

void checkTopWindowThread();
void checkMouseThread();
inline bool isMouseClicked();
bool isDialogNotExist();

extern "C"
{
	__declspec(dllexport) void start();
	__declspec(dllexport) void stop();
	__declspec(dllexport) BOOL changeToAttach();
	__declspec(dllexport) BOOL changeToNormal();
	__declspec(dllexport) long getExplorerX();
	__declspec(dllexport) long getExplorerY();
	__declspec(dllexport) long getExplorerWidth();
	__declspec(dllexport) long getExplorerHeight();
	__declspec(dllexport) void getExplorerPath(char* output, const size_t len);
	__declspec(dllexport) BOOL isDialogWindow();
	__declspec(dllexport) BOOL setEditPath(const char* path, const char* file_name);
	__declspec(dllexport) BOOL bringWindowToTop();
}

BOOL setEditPath(const char* path, const char* file_name)
{
	auto&& path_wstring = string2wstring(path);
	auto&& file_name_wstring = string2wstring(file_name);
	BOOL ret = FALSE;

	CoInitializeEx(nullptr, COINIT_MULTITHREADED);
	try
	{
		jump_to_dest(current_attach_explorer, path_wstring.c_str());
		set_file_selected(current_attach_explorer, file_name_wstring.c_str());
		ret = TRUE;
	}
	catch (std::exception& ex)
	{
		fprintf(stderr, "%s\n", ex.what());
	}
	catch (...)
	{
		fprintf(stderr, "Jump to destination failed, unknown exception");
	}
	CoUninitialize();
	return ret;
}

BOOL changeToNormal()
{
	return is_mouse_click_out_of_explorer || isDialogNotExist();
}

BOOL isDialogWindow()
{
	return top_window_type == G_DIALOG;
}

/**
 * 获取鼠标当前位置的explorer窗口句柄打开的文件夹位置
 */
void getExplorerPath(char* output, const size_t len)
{
	/*POINT p;
	GetCursorPos(&p);
	auto* hd = WindowFromPoint(p);
	if (is_explorer_window_by_class_name(hd))
	{
		hd = GetAncestor(hd, GA_ROOT);
		strcpy_s(output, len, getPathByHWND(hd).c_str());
		return;
	}

	hd = GetForegroundWindow();
	hd = GetAncestor(hd, GA_ROOT);
	strcpy_s(output, len, getPathByHWND(hd).c_str());*/
	if (current_attach_explorer != nullptr)
	{
		const auto current_explorer_path = getPathByHWND(current_attach_explorer);
		strcpy_s(output, len, current_explorer_path.c_str());
	}
}

/**
 * 检查键盘是否按下
 */
BOOL isKeyPressed(const int vk_key)
{
	return GetAsyncKeyState(vk_key) & 0x8000 ? TRUE : FALSE;
}

/**
 * 检测鼠标的点击
 */
inline bool isMouseClicked()
{
	return isKeyPressed(VK_RBUTTON) ||
		isKeyPressed(VK_MBUTTON) ||
		isKeyPressed(VK_LBUTTON);
}

/**
 * 判断窗口句柄是否已经失效，即被关闭
 */
bool isDialogNotExist()
{
	RECT window_rect;
	auto* hd = GetDesktopWindow(); //得到桌面窗口
	hd = GetWindow(hd, GW_CHILD); //得到屏幕上第一个子窗口
	while (hd != nullptr) //循环得到所有的子窗口
	{
		if (IsWindowVisible(hd) && !IsIconic(hd))
		{
			GetWindowRect(hd, &window_rect);
			const int tmp_explorerWidth = window_rect.right - window_rect.left;
			const int tmp_explorerHeight = window_rect.bottom - window_rect.top;
			if (!(tmp_explorerHeight < EXPLORER_MIN_HEIGHT || tmp_explorerWidth < EXPLORER_MIN_WIDTH))
			{
				if (is_explorer_window_by_class_name(hd) || is_file_chooser_window(hd))
				{
					return false;
				}
			}
		}
		hd = GetWindow(hd, GW_HWNDNEXT);
	}
	return true;
}

BOOL bringWindowToTop()
{
	const auto search_bar_hwnd = get_search_bar_hwnd();
	const DWORD dwCurID = GetCurrentThreadId();
	const DWORD dwForeID = GetWindowThreadProcessId(GetForegroundWindow(), nullptr);
	AttachThreadInput(dwCurID, dwForeID, TRUE);
	Sleep(20);
	SetForegroundWindow(search_bar_hwnd);
	Sleep(20);
	AttachThreadInput(dwCurID, dwForeID, FALSE);
	const auto foreground = GetForegroundWindow();
	return foreground == search_bar_hwnd;
}

/**
 * 开始检测窗口句柄
 */
void start()
{
	if (!is_running)
	{
		is_running = true;
		std::thread checkTopWindow(checkTopWindowThread);
		checkTopWindow.detach();
		std::thread checkMouse(checkMouseThread);
		checkMouse.detach();
		DWORD dwTimeout = -1;
		SystemParametersInfo(SPI_GETFOREGROUNDLOCKTIMEOUT, 0, &dwTimeout, 0);
		if (dwTimeout >= 100)
		{
			SystemParametersInfo(SPI_SETFOREGROUNDLOCKTIMEOUT, 0, nullptr, SPIF_SENDCHANGE | SPIF_UPDATEINIFILE);
		}
	}
}

/**
 * 停止检测
 */
void stop()
{
	is_running = false;
}

/**
 * 判断File-Engine窗口是否切换到贴靠模式
 */
BOOL changeToAttach()
{
	return is_explorer_window_at_top;
}

/**
 * 获取explorer窗口左上角的X坐标
 */
long getExplorerX()
{
	return explorer_x;
}

/**
 * 获取explorer窗口左上角的Y坐标
 */
long getExplorerY()
{
	return explorer_y;
}

/**
 * 获得explorer窗口的宽度
 */
long getExplorerWidth()
{
	return explorer_width;
}

/**
 * 获得explorer窗口的高度
 */
long getExplorerHeight()
{
	return explorer_height;
}

void checkMouseThread()
{
	POINT point;
	RECT explorer_area;
	RECT search_bar_area;
	int count = 0;
	constexpr int wait_count_times = 25;
	constexpr int max_wait_count = wait_count_times * 2;
	bool is_mouse_clicked_flag = false;
	while (is_running)
	{
		if (count <= max_wait_count)
		{
			count++;
		}
		// count防止窗口闪烁
		if (is_mouse_clicked_flag && count > wait_count_times && !is_mouse_click_out_of_explorer || count >
			max_wait_count)
		{
			count = 0;
			is_mouse_clicked_flag = false;
			HWND top_window = GetForegroundWindow();
			is_mouse_click_out_of_explorer = !(is_explorer_window_by_process(top_window) || is_file_chooser_window(
				top_window)
				|| is_search_bar_window(top_window));
		}
		// 如果窗口句柄已经失效或者最小化，则判定为关闭窗口
		if (!IsWindow(current_attach_explorer) || IsIconic(current_attach_explorer))
		{
			is_mouse_click_out_of_explorer = true;
		}
		else if (isMouseClicked())
		{
			// 检测鼠标位置，如果点击位置不在explorer窗口内则判定为关闭窗口
			if (GetCursorPos(&point))
			{
				GetWindowRect(current_attach_explorer, &explorer_area);
				GetWindowRect(get_search_bar_hwnd(), &search_bar_area);
				is_mouse_click_out_of_explorer =
					!(explorer_area.left <= point.x && point.x <= explorer_area.right &&
						(explorer_area.top <= point.y && point.y <= explorer_area.bottom)) &&
					!(search_bar_area.left <= point.x && point.x <= search_bar_area.right &&
						(search_bar_area.top <= point.y && point.y <= search_bar_area.bottom));
#ifdef TEST
				cout << "point X:" << point.x << endl;
				cout << "point Y:" << point.y << endl;
				cout << "left :" << explorerArea.left << "  right :" << explorerArea.right << "  top :" << explorerArea.top << "  bottom :" << explorerArea.bottom << endl;
				if (explorerArea.left <= point.x && point.x <= explorerArea.right && (explorerArea.top <= point.y && point.y <= explorerArea.bottom))
				{
					cout << "return false" << endl;
				}
#endif
			}
			count = 0;
			is_mouse_clicked_flag = true;
		}
		if (IsWindowVisible(get_search_bar_hwnd()))
		{
			Sleep(10);
		}
		else
		{
#ifdef TEST
			cout << "search bar not visible" << endl;
#endif
			Sleep(300);
		}
	}
}

/**
 * 检查顶层窗口类型，是选择文件对话框还是explorer
 */
void checkTopWindowThread()
{
	RECT window_rect;
	SetThreadDpiAwarenessContext(DPI_AWARENESS_CONTEXT_UNAWARE);
	while (is_running)
	{
		HWND hwnd = GetForegroundWindow();
		const auto isExplorerWindow = is_explorer_window_by_class_name(hwnd);
		const auto isDialogWindow = is_file_chooser_window(hwnd);

		if (isExplorerWindow || isDialogWindow)
		{
			GetWindowRect(hwnd, &window_rect);
			if (IsZoomed(hwnd))
			{
				explorer_x = 0;
				explorer_y = 0;
			}
			else
			{
				explorer_x = window_rect.left;
				explorer_y = window_rect.top;
			}
			explorer_width = window_rect.right - window_rect.left;
			explorer_height = window_rect.bottom - window_rect.top;
			if (explorer_height < EXPLORER_MIN_HEIGHT || explorer_width < EXPLORER_MIN_WIDTH)
			{
				is_explorer_window_at_top = false;
			}
			else
			{
				if (isExplorerWindow)
				{
					top_window_type = G_EXPLORER;
				}
				else if (isDialogWindow)
				{
					top_window_type = G_DIALOG;
				}
				current_attach_explorer = hwnd;
				is_explorer_window_at_top = true;
				is_mouse_click_out_of_explorer = false;
			}
		}
		else
		{
			is_explorer_window_at_top = false;
		}
		if (is_explorer_window_at_top)
		{
			Sleep(5);
		}
		else
		{
			Sleep(300);
		}
	}
}
