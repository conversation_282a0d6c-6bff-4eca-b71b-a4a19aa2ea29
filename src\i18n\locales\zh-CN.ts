export default {
  translation: {
    settings: {
      title: "设置",
      tabs: {
        account: "账户",
        general: "常规",
        index: "索引",
        search: "搜索",
        dataType: "数据类型",
        cache: "缓存",
        hotkey: "快捷键",
        advanced: "高级",
        about: "关于",
      },
      account: {
        title: "账户设置",
        username: "用户名",
        email: "邮箱",
        tip: "在这里管理你的账号",
        login: "登录",
        licenseType: "许可证：{{type}}",
        expireDate: "到期时间：{{date}}",
        logout: "退出登录",
        confirmLogout: "确定要退出登录吗？",
        confirmLogoutTitle: "退出确认",
        loginFailed: "登录失败",
        loginSuccess: "登录成功",
        loginRedirect: "正在重定向...",
      },
      general: {
        title: "常规设置",
        startup: "开机启动",
        startupDescription: "允许应用程序在系统启动时自动运行",
        searchEngine: "搜索引擎",
        searchEngineDescription: "选择用于网络搜索的默认搜索引擎",
        selectSearchEngine: "选择默认搜索引擎",
        language: "语言",
        languageDescription: "选择应用程序界面显示的语言",
        attachExplorer: "贴靠资源管理器",
        attachExplorerDescription: "将搜索窗口贴靠到Windows资源管理器的右下角",
      },
      index: {
        title: "索引设置",
        indexingConfigs: "索引配置",
        indexingConfigsDescription: "配置需要索引的磁盘和文件",
        addDisk: "添加磁盘",
        addDiskDescription: "选择需要建立索引的磁盘",
        updateIndex: "更新索引",
        updateIndexDescription: "重新扫描所选磁盘并更新索引",
        selectDisk: "选择磁盘",
        maxCache: "最大缓存数",
        maxCacheDescription: "设置索引缓存的最大数量",
        checkFileChange: "文件变更检查间隔（秒）",
        checkFileChangeDescription: "设置检查文件变更的时间间隔（秒）",
        ignoreConfigs: "忽略目录",
        ignoreConfigsDescription:
          "添加不需要索引的文件夹路径，这些文件夹下的所有文件都将被排除在搜索结果之外",
        ignoreDirectories: "忽略的目录",
        isDropPrevious: "删除旧索引",
        isDropPreviousDescription: "更新索引时是否删除原有索引数据",
      },
      search: {
        title: "搜索设置",
        searchConfigs: "搜索配置",
        fuzzyMatch: "模糊匹配",
        fuzzyMatchDescription:
          "启用模糊搜索，只要文件名包含输入的所有字母（按顺序）即可匹配",
        gpuDevice: "GPU设备",
        gpuDeviceDescription: "选择用于加速搜索的GPU设备",
        enableGPU: "启用GPU加速可以提升搜索速度",
        priorityFolder: "优先搜索文件夹",
        priorityFolderDescription:
          "设置优先搜索的文件夹，这些文件夹中的内容会优先显示在搜索结果中",
        priorityFolderClear: "优先文件夹(双击此处清除)",
      },
      dataType: {
        title: "数据类型设置",
        suffixMap: "数据类型后缀映射",
        suffixMapDescription: "配置不同类型文件的后缀名优先级",
        dataType: "数据类型",
        dataTypeDescription: "文件类型的分类名称",
        add: "添加",
        addDescription: "添加新的数据类型或后缀名",
        delete: "删除",
        deleteDescription: "删除选中的数据类型或后缀名",
        suffix: "后缀",
        suffixDescription: "该类型下的文件后缀名",
        description: "描述",
        noDescription: "暂无描述",
        addDataType: "添加数据类型",
        enterDataType: "请输入要添加的新数据类型",
        suffixTable: "后缀列表",
      },
      cache: {
        title: "缓存设置",
        cacheList: "缓存列表",
        cacheListDescription: "显示所有已缓存的文件列表",
        searchCache: "搜索缓存",
        searchCacheDescription: "在缓存列表中搜索特定文件",
        path: "路径",
      },
      hotkey: {
        title: "快捷键设置",
        globalHotkey: "全局快捷键",
        globalHotkeyDescription: "设置打开搜索窗口的全局快捷键",
        openParentFolder: "打开父文件夹",
        openParentFolderDescription: "设置打开文件所在文件夹的快捷键",
        copyFolder: "复制路径",
        copyFolderDescription: "设置复制文件路径的快捷键",
        openWithAdmin: "以管理员权限打开",
        openWithAdminDescription: "设置以管理员权限打开文件的快捷键",
        enableDoubleCtrl: "启用双击 Ctrl 呼出搜索窗口",
      },
      buttons: {
        save: "保存",
        cancel: "取消",
        ok: "确定",
        apply: "应用",
        add: "添加",
        delete: "删除",
        category: "分类",
        sort: "排序",
        noGrouping: "取消分组",
      },
      messages: {
        saveSuccess: "保存配置成功，部分配置重启后生效",
        saveFailed: "保存配置失败",
        confirmDelete: "确定要删除？",
        confirmDeleteTitle: "删除",
        invalidGlobalHotkey: "全局快捷键无效",
        invalidCopyKey: "复制文件夹快捷键无效",
        invalidOpenKey: "打开父文件夹快捷键无效",
        invalidOpenWithAdminKey: "以管理员权限打开快捷键无效",
        sameHotkeys: "快捷键不能相同",
        configError: "配置错误",
        confirmUpdateIndex: "确定要重建文件索引吗",
        confirmUpdateIndexTitle: "更新索引",
        updatingIndex: "正在重建索引",
        alreadyUpdatingIndex: "已经开始重建索引",
        stillOptimizing: "仍在优化数据库",
        browserOpenFailed: "打开浏览器失败",
      },
      advanced: {
        title: "高级设置",
        waitForSearchTasksTimeoutInMills: "任务执行最长等待时间（毫秒）",
        waitForSearchTasksTimeoutInMillsDescription:
          "设置搜索任务的最大等待时间（毫秒）",
        isDeleteUsnOnExit: "退出时删除USN日志",
        isDeleteUsnOnExitDescription: "程序退出时是否删除文件系统的USN日志",
        restartMonitorDiskThreadTimeoutInMills: "重启文件监控线程时间（毫秒）",
        restartMonitorDiskThreadTimeoutInMillsDescription:
          "设置磁盘监控线程的重启间隔（毫秒）",
        isReadPictureByLLM: "使用大模型读取图片内容",
        isReadPictureByLLMDescription: "是否使用大语言模型来解析图片内容",
        isEnableContentIndex: "启用文件内容索引",
        isEnableContentIndexDescription:
          "是否索引文件的内容（可能会增加索引时间）",
        cacheConfigs: "缓存块配置",
        cacheConfigsDescription: "配置索引缓存的相关参数",
        minCacheBlockNumber: "内存缓存块最少记录数量",
        minCacheBlockNumberDescription:
          "设置每个内存缓存块中最少包含的文件记录数量",
        maxCacheBlockNumber: "内存缓存块最多记录数量",
        maxCacheBlockNumberDescription:
          "设置每个内存缓存块中最多包含的文件记录数量",
        minGpuCacheBlockNumber: "GPU缓存块最少记录数量",
        minGpuCacheBlockNumberDescription:
          "设置每个GPU缓存块中最少包含的文件记录数量",
        maxGpuCacheBlockNumber: "GPU缓存块最多记录数量",
        maxGpuCacheBlockNumberDescription:
          "设置每个GPU缓存块中最多包含的文件记录数量",
        debugMode: "调试模式",
        debugModeDescription: "启用调试相关功能",
        enableDebugMode: "启用调试模式",
        enableDebugModeDescription:
          "开启调试模式以打开插件服务调试端口，进行插件开发",
        pluginServiceDebugPort: "Plugin-Service远程调试端口为{{port}}",
        jdkHome: "JDK主目录",
        jdkHomeDescription: "设置Java开发工具包的安装路径",
        javaAgent: "Java Agent",
        javaAgentDescription:
          "设置Java Agent的路径，用于Java应用程序的调试和监控",
        llmSettings: "大语言模型设置",
        llmSettingsDescription: "配置大语言模型相关设置",
        llmProvider: "大语言模型提供者",
        llmProviderDescription: "选择使用的大语言模型提供商",
        ollamaAddress: "Ollama 服务器地址",
        ollamaAddressDescription: "设置Ollama服务器的地址",
        ollamaApiKey: "Ollama API密钥",
        ollamaApiKeyDescription: "设置Ollama API密钥",
        ollamaModel: "Ollama 模型类型",
        ollamaModelDescription: "选择使用的Ollama模型",
      },
      effectiveMode: {
        instant: "即时生效",
        restart: "重启生效",
        reindex: "重建索引生效",
      },
      about: {
        title: "关于",
        version: "版本 {{version}}",
        description:
          "Aiverything 是一个强大的本地文件搜索工具，结合了 AI 的智能搜索能力，帮助您更快地找到所需文件。",
        openSource: "感谢以下开源项目",
        checkUpdate: "检查更新",
        checking: "检查中...",
        installUpdate: "安装新版本 {{version}}",
        noUpdateAvailable: "已是最新版本",
        checkUpdateError: "检查更新失败",
        updateError: "更新失败，请稍后重试",
        copyright: "© 2025 Aiverything. 保留所有权利。",
      },
    },
    searchBar: {
      copyPath: "复制文件路径",
      copyName: "复制文件名",
      openParentDirectory: "打开上级文件夹",
      openFile: "打开文件",
      openWithAdmin: "以管理员权限打开",
      openInTerminal: "在终端中打开",
      footerHint: "你也可以从右下角的任务栏访问Aiverything",
      filePathCopied: "已复制文件路径",
      noPluginInstalled: "你还没有安装任何插件，请从插件市场进行安装",
      noPluginFound: '未找到包含 "{{keyword}}" 的插件',
      uwpAppNotSupport: "UWP应用不支持以管理员权限打开",
      detail: {
        fileName: "文件名",
        fileLocation: "文件路径",
        lastModified: "上次修改",
        createdAt: "创建时间",
        searchOnWeb: "在网络上搜索",
        searchNow: "立刻搜索",
        searchFor: "搜索 {{inputValue}}",
        searchBrowserPrompt: '按下Enter以在默认浏览器上搜索"{{inputValue}}"',
      },
      plugin: {
        identifier: "标识",
        version: "版本",
        author: "作者",
        description: "描述",
      },
      aiMode: "AI 搜索模式",
      searchPlaceholder: "在这里搜索任何内容...",
      aiSearchPlaceholder: "使用 AI 搜索...",
      aiSearching: "AI 正在搜索...",
      aiSearchPrompt: "正在分析您的搜索请求",
      searching: "正在搜索...",
      searchPrompt: "正在搜索文件",
      waitingForInput: "等待输入完成...",
      waitingPrompt: "请稍等片刻，正在准备搜索",
      pressEnterToSearch: "按回车键搜索",
      aiModeHint: "使用 AI 进行智能搜索和分析",
      result: "个结果",
      results: "个结果",
      showRightPanel: "显示右侧面板",
      hideRightPanel: "隐藏右侧面板",
      quickDirectToType: "快速直达分类",
      categorizeByType: "分类",
      sortByRelevance: "按相关性排序",
      sortByName: "按名称排序",
      sortByDate: "按修改时间排序",
      sortBySize: "按大小排序",
      sortByType: "按类型排序",
      ascending: "升序",
      descending: "降序",
      rankBy: "排序方式",
      relevance: "相关性",
      lastModifiedTime: "最后修改时间",
      name: "名称",
      type: "类型",
      fileSize: "文件大小",
    },
    buttons: {
      category: "分类",
      sort: "排序",
    },
    category: {
      all: "全部",
      document: "文档",
      image: "图片",
      video: "视频",
      audio: "音频",
      archive: "压缩包",
      executable: "可执行文件",
    },
    dataTypes: {
      Search: "搜索",
      Shortcut: "推荐",
      Apps: "应用程序",
      "UWP Apps": "UWP应用",
      Folders: "文件夹",
      Documents: "文档",
      Sheets: "表格",
      Slides: "演示文稿",
      Pictures: "图片",
      Videos: "视频",
      Audios: "音频",
      Developer: "开发者",
      Others: "其他",
    },
    sort: {
      name: "名称",
      size: "大小",
      date: "日期",
      type: "类型",
    },
    tray: {
      settings: "设置",
      pluginSettings: "插件设置",
      pluginStore: "插件商店",
      restart: "重启",
      quit: "退出",
    },
    attach: {
      doubleClickShift: "双击Shift切换到这里",
    },
    core: {
      updateFileIndex: "开始更新文件索引",
      optimizeDatabase: "开始优化数据库",
      updateFileIndexDone: "文件索引更新完成",
      optimizeDatabaseDone: "优化数据库完成",
    },
    pluginSettings: {
      title: "插件设置",
      settings: "{{pluginName}} 设置",
      root: "根目录",
      currentLevelFields: "当前层级字段：",
      fieldName: "字段名称",
      fieldType: "字段类型",
      fieldDescription: "字段描述",
      currentObjectStructure: "当前对象结构：",
      addingFieldsTo: "正在添加字段到: {{path}}",
      defaultValue: "默认值",
      currentValue: "当前值",
      objectField: {
        addFields: "添加字段到对象",
      },
      booleanField: {
        defaultValue: "默认值",
        currentValue: "当前值",
        description: "选中为true，未选中为false",
      },
      types: {
        string: "字符串",
        number: "数字",
        boolean: "布尔值",
        object: "对象",
        array: "数组",
      },
      dialog: {
        title: "添加新配置",
        type: "类型",
        description: "描述",
        defaultValue: "默认值",
        currentValue: "当前值",
      },
      buttons: {
        cancel: "取消",
        add: "添加",
        addField: "添加字段",
        backToParent: "返回上级对象",
      },
      arrayItem: {
        title: "项目 {{index}}",
      },
      store: {
        title: "插件市场",
        description: "在这里你可以找到并安装各种插件",
      },
      editJson: "编辑 settings.json",
      openEntryPage: "打开插件页面",
      loadError: "插件加载错误：{{error}}",
      checkUpdate: "检查更新",
      updateAvailable: "有新版本可用：{{version}}",
      noUpdateAvailable: "已是最新版本",
      checking: "检查中...",
      updateError: "检查更新失败",
      clickToUpdate: "点击前往官网更新",
      updatePrompt: "发现新版本 {{version}}，点击前往官网下载更新",
      version: "版本",
      updateAllPlugins: "正在检查所有插件更新...",
      updateAvailableMultiple: "发现多个插件有更新可用",
    },
    fileDetail: {
      aiSummary: "AI 摘要",
      aiSummarize: "AI 总结",
      viewSummary: "查看 AI 总结",
      aiSummaryFailed: "总结失败，请稍后重试",
      noFile: "未找到文件",
      generating: "正在生成摘要...",
      fileEmpty: "文件为空",
      thinkingProcess: "思考过程",
    },
    common: {
      back: "返回",
      loading: "加载中...",
    },
    fileList: {
      viewAllCategories: "查看所有分类",
      categories: "类",
      fileTypes: "文件类型",
      fileCount: "{{count}} 个文件",
    },
    update: {
      available: "有新版本可用",
      newVersionAvailable: "有新版本可用：{{version}}，请在设置中检查更新",
    },
    shortcuts: {
      quickAccess: "快速访问",
      thisPC: "此电脑",
      recycleBin: "回收站",
      documents: "文档",
      downloads: "下载",
      oneDrive: "OneDrive",
      settings: "设置",
      controlPanel: "控制面板",
      systemSettings: "系统设置",
      environmentVars: "环境变量",
      userAccounts: "用户账户",
      networkSettings: "网络设置",
      powerOptions: "电源选项",
      previousPage: "上一页",
      nextPage: "下一页",
      goToPage: "前往第{{page}}页",
      registryEditor: "注册表编辑器",
      deviceManager: "设备管理器",
      diskManagement: "磁盘管理",
      services: "服务",
      taskManager: "任务管理器",
      eventViewer: "事件查看器",
      groupPolicy: "组策略",
    },
  },
};
