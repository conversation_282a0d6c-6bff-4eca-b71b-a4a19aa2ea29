use std::path::Path;
use std::{error::Error, fs::File, io::Write};
use windows::Win32::Globalization::GetACP;

pub fn generate_open_file_vbs(
    command: &str,
    vbs_file_path: std::path::PathBuf,
) -> Result<(), Box<dyn Error>> {
    let mut vbs_file = File::create(&vbs_file_path)?;
    let code_page;
    unsafe {
        code_page = GetACP();
    }
    let encoding =
        codepage::to_encoding(code_page as u16).expect("Failed to get system codepage encoder");

    let mut write_cp = |content: &str| -> Result<(), Box<dyn Error>> {
        // 使用系统默认编码进行转换
        let (encoded, _, _) = encoding.encode(content);
        vbs_file.write_all(&encoded)?;
        vbs_file.write_all(b"\r\n")?;
        Ok(())
    };

    write_cp("Set WshShell = CreateObject(\"WScript.Shell\")")?;

    if command.contains(" ") {
        write_cp(&format!("Command = \"\"\"{}\"\"\"", command))?;
    } else {
        write_cp(&format!("Command = \"{}\"", command))?;
    }

    write_cp("WshShell.Run Command, 0, False")?;

    Ok(())
}

pub fn generate_open_file_bat(
    command: &str,
    working_dir: &str,
    bat_file_path: std::path::PathBuf,
) -> Result<(), Box<dyn Error>> {
    let mut bat_file = File::create(&bat_file_path)?;
    let code_page;
    unsafe {
        code_page = GetACP();
    }
    let encoding =
        codepage::to_encoding(code_page as u16).expect("Failed to get system codepage encoder");

    let mut write_cp = |content: &str| -> Result<(), Box<dyn Error>> {
        // 使用系统默认编码进行转换
        let (encoded, _, _) = encoding.encode(content);
        bat_file.write_all(&encoded)?;
        bat_file.write_all(b"\r\n")?;
        Ok(())
    };

    let change_dir_str = format!("cd /d \"{}\"", working_dir);

    write_cp(change_dir_str.as_str())?;
    write_cp(command)?;

    Ok(())
}

#[cfg(target_os = "windows")]
pub fn adjust_canonicalization<P: AsRef<Path>>(p: P) -> String {
    const VERBATIM_PREFIX: &str = r#"\\?\"#;
    let p = p.as_ref().display().to_string();
    if p.starts_with(VERBATIM_PREFIX) {
        p[VERBATIM_PREFIX.len()..].to_string()
    } else {
        p
    }
}
