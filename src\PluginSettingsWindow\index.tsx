import { useEffect, useState } from "react";
import { PluginInfo } from "../Component/MainAnwser";
import {
  getPluginInfo,
  getPluginList,
  getPluginResourceUrl,
  getPluginLoadError,
  PluginLoadError,
} from "../api/plugin";
import PluginSettingsContent from "../Component/PluginSettingsContent";
import { readAppConfig } from "../api/aiverything";
import {
  Box,
  Typography,
  Button,
  Alert,
  Snackbar,
  ThemeProvider,
  createTheme,
} from "@mui/material";
import StorefrontIcon from "@mui/icons-material/Storefront";
import { useTranslation } from "react-i18next";
import { open } from "@tauri-apps/plugin-shell";
import { PLUGIN_STORE_URL } from "../utils/constants";
import React from "react";

const PluginSettingsWindow = () => {
  const [pluginInfoList, setPluginInfoList] = useState<PluginInfo[]>(null);
  const { t } = useTranslation();
  const [loadError, setLoadError] = useState<string>("");
  const [showError, setShowError] = useState(false);
  const [systemDarkTheme, setSystemDarkTheme] = useState(
    window.matchMedia("(prefers-color-scheme: dark)").matches
  );

  // 监听系统主题变化
  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

    const handleThemeChange = (e: MediaQueryListEvent) => {
      setSystemDarkTheme(e.matches);
    };

    // 添加监听器
    mediaQuery.addEventListener("change", handleThemeChange);

    // 清理函数
    return () => {
      mediaQuery.removeEventListener("change", handleThemeChange);
    };
  }, []);

  const theme = React.useMemo(
    () =>
      createTheme({
        palette: {
          mode: systemDarkTheme ? "dark" : "light",
          background: {
            default: systemDarkTheme ? "#121212" : "#ffffff",
            paper: systemDarkTheme ? "#1e1e1e" : "#f5f5f5",
          },
          divider: systemDarkTheme
            ? "rgba(255, 255, 255, 0.12)"
            : "rgba(0, 0, 0, 0.12)",
        },
      }),
    [systemDarkTheme]
  );

  const isValidUrl = (urlString: string) => {
    try {
      return Boolean(new URL(urlString));
    } catch (e) {
      return false;
    }
  };

  const fetchPluginInfoList = async (inputPluginName: string) => {
    const appConfig = await readAppConfig();
    const pluginListData = await getPluginList(inputPluginName);
    const pluginList = pluginListData.data;
    const pluginInfoArray = pluginList.map(async (eachPlugin: any) => {
      const eachPluginInfo = await getPluginInfo(
        eachPlugin.identifier,
        appConfig.locale
      );
      const pluginInfo: PluginInfo = eachPluginInfo.data;
      const eachPluginIconUrl = await getPluginResourceUrl(
        eachPlugin.identifier,
        pluginInfo.icon
      );
      let eachPluginEntryPageUrl;
      if (!isValidUrl(pluginInfo.entryPage)) {
        eachPluginEntryPageUrl = await getPluginResourceUrl(
          eachPlugin.identifier,
          pluginInfo.entryPage
        );
      } else {
        eachPluginEntryPageUrl = pluginInfo.entryPage;
      }
      if (pluginInfo.embeddedSupport) {
        let eachPluginEmbeddedPage = pluginInfo.embeddedPage
          ? pluginInfo.embeddedPage
          : await getPluginResourceUrl(eachPlugin.identifier, "embedded.html");
        if (!isValidUrl(eachPluginEmbeddedPage)) {
          eachPluginEmbeddedPage = await getPluginResourceUrl(
            eachPlugin.identifier,
            eachPluginEmbeddedPage
          );
        }
        pluginInfo.embeddedPage = eachPluginEmbeddedPage;
      }

      pluginInfo.icon = eachPluginIconUrl;
      pluginInfo.entryPage = eachPluginEntryPageUrl;
      return pluginInfo;
    });
    const pluginInfos = await Promise.all(pluginInfoArray);
    console.log("Plugin Info List: ", pluginInfos);
    return pluginInfos;
  };

  useEffect(() => {
    fetchPluginInfoList("").then((res) => {
      setPluginInfoList(res);
    });

    getPluginLoadError().then((error) => {
      const errorData: PluginLoadError[] = error?.data;
      console.log("Plugin Load Error: ", errorData);
      if (errorData && errorData.length > 0) {
        const failedPlugins = errorData.flatMap((err) => err.name);
        setLoadError(failedPlugins.join(", "));
        setShowError(true);
      }
    });
  }, []);

  const handleCloseError = () => {
    setShowError(false);
  };

  if (!pluginInfoList?.length) {
    return (
      <ThemeProvider theme={theme}>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            height: "100vh",
            gap: 2,
          }}
        >
          <StorefrontIcon sx={{ fontSize: 60, color: "text.secondary" }} />
          <Typography variant="h6" color="text.secondary">
            {t("searchBar.noPluginInstalled")}
          </Typography>
          <Button
            variant="contained"
            startIcon={<StorefrontIcon />}
            onClick={() => {
              open(PLUGIN_STORE_URL);
            }}
          >
            {t("pluginSettings.store.title")}
          </Button>
        </Box>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ minHeight: "100vh" }}>
        <PluginSettingsContent pluginInfoList={pluginInfoList} />
        <Snackbar
          open={showError}
          autoHideDuration={6000}
          onClose={handleCloseError}
          anchorOrigin={{ vertical: "top", horizontal: "center" }}
        >
          <Alert
            onClose={handleCloseError}
            severity="error"
            sx={{
              width: "100%",
              whiteSpace: "pre-line",
            }}
          >
            {t("pluginSettings.loadError", { error: loadError })}
          </Alert>
        </Snackbar>
      </Box>
    </ThemeProvider>
  );
};

export default PluginSettingsWindow;
