# Tauri + React + Typescript

### **Windows 下的 Tauri 构建指南（包含 NSIS 和 CMake 配置）**

本指南将帮助你在 **Windows** 下构建 **Tauri 应用**，包括 **NSIS 安装包** 和 **CMake** 相关配置。

---

## **1. 环境准备**

### **1.1 安装 Rust**
确保安装了 **Rust** 并更新到最新版本：
```sh
rustup update
```
如果尚未安装 Rust，可执行：
```sh
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

### **1.2 安装 Tauri CLI**
```sh
cargo install tauri-cli
```

### **1.3 安装 Windows 开发工具**
Tauri 需要 **Windows SDK** 和 **Visual Studio C++ 构建工具**：
- 下载并安装 **[Visual Studio Build Tools](https://visualstudio.microsoft.com/visual-cpp-build-tools/)**
- **安装组件**（运行安装程序时勾选）：
  - **Windows 10 SDK**
  - **C++ CMake 工具**
  - **MSVC v142 或更新版本**

### **1.4 安装 Node.js**
```sh
nvm install --lts
nvm use --lts
```

### **1.5 安装 CMake**
Tauri 需要 **CMake** 进行构建：
- **安装方式 1**（官方安装包）：  
  从 [CMake 官网](https://cmake.org/download/) 下载并安装
- **安装方式 2**（使用 `winget`）：  
  ```sh
  winget install Kitware.CMake
  ```
- **验证 CMake 是否安装成功**
  ```sh
  cmake --version
  ```

---

## **2. 构建 Tauri 应用**

### **2.1 开发模式运行**
```sh
yarn tauri dev
```

---

## **3. NSIS 安装包配置**
### **3.1 安装 NSIS**
**方法 1**（从官网下载安装）：
- 访问 [NSIS 官网](https://nsis.sourceforge.io/Download)
- 下载并安装 **NSIS**

**方法 2**（使用 `winget` 安装）：
```sh
winget install NSIS.NSIS
```

### **3.2 生成安装包**
```sh
yarn tauri build
```
生成的 `NSIS` 安装包位于：
```
src-tauri/target/release/bundle/nsis/
```
