export default {
  translation: {
    settings: {
      title: "設定",
      tabs: {
        account: "アカウント",
        general: "一般",
        index: "インデックス",
        search: "検索",
        dataType: "データタイプ",
        cache: "キャッシュ",
        hotkey: "ホットキー",
        advanced: "詳細設定",
        attachExplorer: "エクスプローラーに添付",
        about: "関連情報",
      },
      account: {
        title: "アカウント設定",
        tip: "アカウント設定を管理する",
        username: "ユーザー名",
        email: "メールアドレス",
        login: "ログイン",
        licenseType: "ライセンス：{{type}}",
        expireDate: "有効期限：{{date}}",
        logout: "ログアウト",
        confirmLogout: "ログアウトしてもよろしいですか？",
        confirmLogoutTitle: "ログアウト確認",
        loginFailed: "ログインに失敗しました",
        loginSuccess: "ログインに成功しました",
        loginRedirect: "リダイレクト中...",
      },
      general: {
        title: "一般設定",
        startup: "起動時に実行",
        startupDescription:
          "システム起動時にアプリケーションを自動的に実行する",
        searchEngine: "検索エンジン",
        searchEngineDescription: "ウェブ検索のデフォルト検索エンジンを選択",
        selectSearchEngine: "デフォルト検索エンジンを選択",
        language: "言語",
        languageDescription: "アプリケーションインターフェースの言語を選択",
        attachExplorer: "エクスプローラーに添付",
        attachExplorerDescription:
          "検索ウィンドウをエクスプローラーの右下に添付する",
      },
      index: {
        title: "インデックス設定",
        indexingConfigs: "インデックス設定",
        indexingConfigsDescription: "インデックスするディスクとファイルを設定",
        addDisk: "追加",
        addDiskDescription: "インデックスするディスクを選択",
        updateIndex: "ディスクインデックスを更新",
        updateIndexDescription:
          "選択したディスクを再スキャンしてインデックスを更新",
        selectDisk: "ディスクを選択",
        maxCache: "最大キャッシュ数",
        maxCacheDescription: "インデックスキャッシュの最大数を設定",
        checkFileChange: "ファイル変更チェック間隔(秒)",
        checkFileChangeDescription: "ファイル変更をチェックする間隔を設定(秒)",
        ignoreConfigs: "除外設定",
        ignoreConfigsDescription: "インデックスしないディレクトリパスを追加",
        ignoreDirectories: "除外ディレクトリ",
        isDropPrevious: "以前のファイルインデックスを削除",
        isDropPreviousDescription:
          "インデックス更新時に既存のインデックスデータを削除するかどうか",
      },
      search: {
        title: "検索設定",
        searchConfigs: "検索設定",
        fuzzyMatch: "あいまい検索を有効化",
        fuzzyMatchDescription:
          "あいまい検索を有効にすると、入力した文字（順序通り）を含むファイルがマッチします",
        gpuDevice: "GPUデバイス",
        gpuDeviceDescription: "高速化のためのGPUデバイスを選択",
        enableGPU: "GPU高速化を有効化",
        priorityFolder: "優先フォルダを選択",
        priorityFolderDescription: "検索結果で優先されるフォルダを設定",
        priorityFolderClear: "優先フォルダ(ダブルクリックでクリア)",
      },
      dataType: {
        title: "データタイプ設定",
        suffixMap: "データタイプ拡張子マップ",
        suffixMapDescription: "異なるファイルタイプの拡張子優先度を設定",
        dataType: "データタイプ",
        dataTypeDescription: "ファイルタイプの分類名",
        add: "追加",
        addDescription: "新しいデータタイプまたは拡張子を追加",
        delete: "削除",
        deleteDescription: "選択したデータタイプまたは拡張子を削除",
        suffix: "拡張子",
        suffixDescription: "このタイプのファイル拡張子",
        description: "説明",
        noDescription: "説明なし",
        addDataType: "データタイプを追加",
        enterDataType: "追加する新しいデータタイプを入力してください",
        suffixTable: "拡張子テーブル",
      },
      cache: {
        title: "キャッシュ設定",
        cacheList: "キャッシュリスト",
        cacheListDescription: "キャッシュされたすべてのファイルを表示",
        searchCache: "キャッシュを検索",
        searchCacheDescription: "キャッシュリストで特定のファイルを検索",
        path: "パス",
      },
      hotkey: {
        title: "ホットキー設定",
        globalHotkey: "グローバルホットキー",
        globalHotkeyDescription:
          "検索ウィンドウを開くグローバルホットキーを設定",
        openParentFolder: "親フォルダを開くキー",
        openParentFolderDescription:
          "ファイルの親フォルダを開くホットキーを設定",
        copyFolder: "フォルダをコピーするキー",
        copyFolderDescription: "ファイルパスをコピーするホットキーを設定",
        openWithAdmin: "管理者として開くキー",
        openWithAdminDescription: "管理者権限でファイルを開くホットキーを設定",
        enableDoubleCtrl: "Ctrlキーをダブルクリックして検索バーを表示します",
      },
      buttons: {
        save: "保存",
        cancel: "キャンセル",
        ok: "OK",
        apply: "適用",
        add: "追加",
        delete: "削除",
        category: "カテゴリ",
        sort: "並び替え",
        noGrouping: "グループ化しない",
      },
      messages: {
        saveSuccess: "設定を保存しました。一部の設定は再起動後に有効になります",
        saveFailed: "設定の保存に失敗しました",
        confirmDelete: "削除してもよろしいですか？",
        confirmDeleteTitle: "削除",
        invalidGlobalHotkey: "グローバルホットキーが無効です",
        invalidCopyKey: "フォルダコピーキーが無効です",
        invalidOpenKey: "親フォルダを開くキーが無効です",
        invalidOpenWithAdminKey: "管理者として開くキーが無効です",
        sameHotkeys: "ホットキーは重複できません",
        configError: "設定エラー",
        confirmUpdateIndex: "ファイルインデックスを更新しますか",
        confirmUpdateIndexTitle: "インデックス更新",
        updatingIndex: "ファイルインデックスの更新を開始",
        alreadyUpdatingIndex: "すでにインデックスを更新中です",
        stillOptimizing: "データベースの最適化中です",
        browserOpenFailed: "ブラウザを開くのに失敗しました",
      },
      advanced: {
        title: "詳細設定",
        waitForSearchTasksTimeoutInMills:
          "検索タスクのタイムアウト時間(ミリ秒)",
        waitForSearchTasksTimeoutInMillsDescription:
          "検索タスクの最大待機時間を設定(ミリ秒)",
        isDeleteUsnOnExit: "終了時にUSNジャーナルを削除",
        isDeleteUsnOnExitDescription:
          "プログラム終了時にUSNジャーナルを削除するかどうか",
        restartMonitorDiskThreadTimeoutInMills:
          "ディスク監視スレッドの再起動タイムアウト(ミリ秒)",
        restartMonitorDiskThreadTimeoutInMillsDescription:
          "ディスク監視スレッドの再起動間隔を設定(ミリ秒)",
        isReadPictureByLLM: "LLMで画像内容を読み取る",
        isReadPictureByLLMDescription:
          "大規模言語モデルを使用して画像内容を解析するかどうか",
        isEnableContentIndex: "ファイル内容のインデックスを有効化",
        isEnableContentIndexDescription:
          "ファイル内容のインデックスを作成する（インデックス時間が増加する可能性があります）",
        cacheConfigs: "キャッシュブロック設定",
        cacheConfigsDescription: "インデックスキャッシュの関連パラメータを設定",
        minCacheBlockNumber: "最小メモリキャッシュブロックレコード数",
        minCacheBlockNumberDescription:
          "各メモリキャッシュブロックの最小ファイルレコード数を設定",
        maxCacheBlockNumber: "最大メモリキャッシュブロックレコード数",
        maxCacheBlockNumberDescription:
          "各メモリキャッシュブロックの最大ファイルレコード数を設定",
        minGpuCacheBlockNumber: "最小GPUキャッシュブロックレコード数",
        minGpuCacheBlockNumberDescription:
          "各GPUキャッシュブロックの最小ファイルレコード数を設定",
        maxGpuCacheBlockNumber: "最大GPUキャッシュブロックレコード数",
        maxGpuCacheBlockNumberDescription:
          "各GPUキャッシュブロックの最大ファイルレコード数を設定",
        debugMode: "デバッグモード",
        debugModeDescription: "デバッグ関連機能を有効化",
        enableDebugMode: "デバッグモードを有効化",
        enableDebugModeDescription:
          "プラグイン開発用のデバッグポートを開くためにデバッグモードを有効化",
        pluginServiceDebugPort:
          "Plugin-Serviceのリモートデバッグポートは{{port}}です",
        jdkHome: "JDKホーム",
        jdkHomeDescription: "Java開発キットのインストールパスを設定",
        javaAgent: "Java Agent",
        javaAgentDescription:
          "Javaアプリケーションのデバッグと監視のためのJava Agentパスを設定",
        llmSettings: "LLM設定",
        llmSettingsDescription: "大規模言語モデルの関連設定を構成",
        llmProvider: "LLMプロバイダー",
        llmProviderDescription: "使用する大規模言語モデルプロバイダーを選択",
        ollamaAddress: "Ollamaサーバーアドレス",
        ollamaAddressDescription: "Ollamaサーバーのアドレスを設定",
        ollamaApiKey: "Ollama APIキー設定",
        ollamaApiKeyDescription: "Ollama APIキーを設定",
        ollamaModel: "Ollamaモデルタイプ",
        ollamaModelDescription: "使用するOllamaモデルを選択",
      },
      effectiveMode: {
        instant: "即時反映",
        restart: "再起動後に反映",
        reindex: "再インデックス後に反映",
      },
      about: {
        title: "関連情報",
        version: "バージョン {{version}}",
        description:
          "Aiverythingは、AIの知能を組み込んだ強力なローカルファイル検索ツールです。",
        openSource: "以下のオープンソースプロジェクトに感謝します",
        checkUpdate: "アップデートを確認",
        checking: "確認中...",
        installUpdate: "バージョン {{version}} をインストール",
        noUpdateAvailable: "最新版です",
        checkUpdateError: "アップデートの確認に失敗しました",
        updateError: "アップデートに失敗しました。後でもう一度お試しください",
        copyright: "© 2025 Aiverything. すべての権利を保有しています。",
      },
    },
    searchBar: {
      copyPath: "ファイルパスをコピー",
      copyName: "ファイル名をコピー",
      openParentDirectory: "親ディレクトリを開く",
      openFile: "ファイルを開く",
      openWithAdmin: "管理者として開く",
      footerHint: "右下のトレイからもAiverythingを呼び出せます",
      openInTerminal: "ターミナルで開く",
      filePathCopied: "ファイルパスをコピーしました",
      noPluginInstalled:
        "プラグインがインストールされていません。ストアからインストールしてください",
      noPluginFound: '"{{keyword}}" を含むプラグインが見つかりません',
      uwpAppNotSupport: "UWPアプリは管理者権限で開くことができません",
      detail: {
        fileName: "ファイル名",
        fileLocation: "ファイルの場所",
        lastModified: "最終更新",
        createdAt: "作成日時",
        searchOnWeb: "ウェブで検索",
        searchNow: "今すぐ検索",
        searchFor: "{{inputValue}}を検索",
        searchBrowserPrompt:
          'Enterキーを押して"{{inputValue}}"をデフォルトブラウザで検索',
      },
      plugin: {
        identifier: "識別子",
        version: "バージョン",
        author: "作者",
        description: "説明",
      },
      aiMode: "AI検索モード",
      searchPlaceholder: "ここで何かを見つける...",
      aiSearchPlaceholder: "AIで検索...",
      aiSearching: "AIが検索中...",
      aiSearchPrompt: "AIがクエリを分析しています。少々お待ちください",
      searching: "検索中...",
      searchPrompt: "ファイルを検索中...",
      waitingForInput: "入力完了を待機中...",
      waitingPrompt: "少々お待ちください、検索を準備中",
      pressEnterToSearch: "Enterキーを押して検索",
      aiModeHint: "AIを使用してインテリジェントな検索と分析を行います",
      result: "個の結果",
      results: "個の結果",
      showRightPanel: "右パネルを表示",
      hideRightPanel: "右パネルを隠す",
      quickDirectToType: "タイプへのクイックアクセス",
      categorizeByType: "タイプで分類",
      sortByRelevance: "関連度で並び替え",
      sortByName: "名前で並び替え",
      sortByDate: "日付で並び替え",
      sortBySize: "サイズで並び替え",
      sortByType: "タイプで並び替え",
      ascending: "昇順",
      descending: "降順",
      rankBy: "並び替え方式",
      relevance: "関連度",
      lastModifiedTime: "最終更新日時",
      name: "名前",
      type: "タイプ",
      fileSize: "ファイルサイズ",
    },
    buttons: {
      category: "カテゴリ",
      sort: "並び替え",
    },
    category: {
      all: "すべて",
      document: "ドキュメント",
      image: "画像",
      video: "動画",
      audio: "音声",
      archive: "アーカイブ",
      executable: "実行可能ファイル",
    },
    dataTypes: {
      Search: "検索",
      Shortcut: "おすすめ",
      Apps: "アプリ",
      "UWP Apps": "UWPアプリ",
      Folders: "フォルダ",
      Documents: "ドキュメント",
      Sheets: "スプレッドシート",
      Slides: "プレゼンテーション",
      Pictures: "画像",
      Videos: "動画",
      Audios: "音声",
      Developer: "開発者",
      Others: "その他",
    },
    sort: {
      name: "名前",
      size: "サイズ",
      date: "日付",
      type: "タイプ",
    },
    tray: {
      settings: "設定",
      pluginSettings: "プラグイン設定",
      pluginStore: "プラグインストア",
      restart: "再起動",
      quit: "終了",
    },
    attach: {
      doubleClickShift: "Shiftキーをダブルクリックでここに切り替え",
    },
    core: {
      updateFileIndex: "ファイルインデックスの更新を開始",
      optimizeDatabase: "データベースの最適化を開始",
      updateFileIndexDone: "ファイルインデックスの更新が完了",
      optimizeDatabaseDone: "データベースの最適化が完了",
    },
    pluginSettings: {
      title: "プラグイン設定",
      settings: "{{pluginName}}の設定",
      root: "ルート",
      currentLevelFields: "現在のレベルのフィールド：",
      fieldName: "フィールド名",
      fieldType: "フィールドタイプ",
      fieldDescription: "フィールドの説明",
      currentObjectStructure: "現在のオブジェクト構造：",
      addingFieldsTo: "フィールドを追加：{{path}}",
      defaultValue: "デフォルト値",
      currentValue: "現在の値",
      objectField: {
        addFields: "オブジェクトにフィールドを追加",
      },
      booleanField: {
        defaultValue: "デフォルト値",
        currentValue: "現在の値",
        description: "チェックがtrue、未チェックがfalse",
      },
      types: {
        string: "文字列",
        number: "数値",
        boolean: "真偽値",
        object: "オブジェクト",
        array: "配列",
      },
      dialog: {
        title: "新しい設定を追加",
        type: "タイプ",
        description: "説明",
        defaultValue: "デフォルト値",
        currentValue: "現在の値",
      },
      buttons: {
        cancel: "キャンセル",
        add: "追加",
        addField: "フィールドを追加",
        backToParent: "親オブジェクトに戻る",
      },
      arrayItem: {
        title: "項目 {{index}}",
      },
      store: {
        title: "プラグインショップ",
        description:
          "ここではさまざまなプラグインを見つけてインストールできます",
      },
      editJson: "settings.jsonを編集",
      openEntryPage: "エントリーページを開く",
      loadError: "プラグインの読み込みに失敗しました：{{error}}",
      checkUpdate: "アップデートを確認",
      updateAvailable: "新しいバージョンが利用可能です：{{version}}",
      noUpdateAvailable: "最新版です",
      checking: "確認中...",
      updateError: "アップデートの確認に失敗しました",
      clickToUpdate: "アップデートを確認",
      updatePrompt:
        "新しいバージョン {{version}} が利用可能です。ウェブサイトで確認してください",
      version: "バージョン",
      updateAllPlugins: "すべてのプラグインのアップデートを確認中...",
      updateAvailableMultiple: "複数のプラグインのアップデートが利用可能です",
    },
    fileDetail: {
      aiSummary: "AI 要約",
      aiSummarize: "AI 要約",
      viewSummary: "AI 要約を表示",
      aiSummaryFailed: "要約に失敗しました。後でもう一度お試しください",
      noFile: "ファイルが見つかりません",
      generating: "要約を生成中...",
      fileEmpty: "ファイルは空です",
      thinkingProcess: "思考プロセス",
    },
    common: {
      back: "戻る",
      loading: "読み込み中...",
    },
    fileList: {
      viewAllCategories: "すべてのカテゴリを表示",
      categories: "カテゴリ",
      fileTypes: "ファイルタイプ",
      fileCount: "{{count}} ファイル",
    },
    update: {
      available: "新しいバージョンが利用可能です",
      newVersionAvailable:
        "新しいバージョンが利用可能です：{{version}}，設定で更新を確認してください",
    },
    shortcuts: {
      quickAccess: "クイックアクセス",
      thisPC: "このコンピューター",
      recycleBin: "ゴミ箱",
      documents: "ドキュメント",
      downloads: "ダウンロード",
      oneDrive: "OneDrive",
      settings: "設定",
      controlPanel: "コントロールパネル",
      systemSettings: "システム設定",
      environmentVars: "環境変数",
      userAccounts: "ユーザーアカウント",
      networkSettings: "ネットワーク設定",
      powerOptions: "電源オプション",
      previousPage: "前のページ",
      nextPage: "次のページ",
      goToPage: "ページ{{page}}に移動",
      registryEditor: "レジストリエディター",
      deviceManager: "デバイスマネージャー",
      diskManagement: "ディスク管理",
      services: "サービス",
      taskManager: "タスクマネージャー",
      eventViewer: "イベントビューアー",
      groupPolicy: "グループポリシー",
    },
  },
};
