import React, { useState, useEffect, useRef, useLayoutEffect } from "react";
import { FaGoogle } from "react-icons/fa";
import { BsBing } from "react-icons/bs";
import { SiDuckduckgo, SiBaidu } from "react-icons/si";
import { IoIosArrowDown, IoIosArrowUp } from "react-icons/io";
import { AiOutlinePlus, AiOutlineMinus } from "react-icons/ai";
import {
  getConfig,
  setConfig as setConfigApi,
  status as dbStatusApi,
  update as updateIndexApi,
  getDisk,
  getCache,
  deleteCache,
  getGpu,
  readAppConfig,
  setAppConfig as setAppConfigApi,
} from "../api/aiverything";
import { open as openDialog, ask } from "@tauri-apps/plugin-dialog";
import Button from "@mui/material/Button";
import Stack from "@mui/material/Stack";
import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import {
  Autocomplete,
  Box,
  Checkbox,
  Collapse,
  createTheme,
  DialogActions,
  DialogContent,
  DialogContentText,
  Divider,
  Fab,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  Grid2,
  IconButton,
  InputLabel,
  ListItemButton,
  Select,
  Skeleton,
  Snackbar,
  SnackbarCloseReason,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  ThemeProvider,
  Typography,
  useTheme,
  Chip,
  Paper,
  CircularProgress,
  Alert,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import { addStartup, deleteStartup, hasStartup } from "../api/startup";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from "@hello-pangea/dnd";
import MenuItem from "@mui/material/MenuItem";
import ListItemText from "@mui/material/ListItemText";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import SettingsIcon from "@mui/icons-material/Settings";
import StorageIcon from "@mui/icons-material/Storage";
import SearchIcon from "@mui/icons-material/Search";
import DataUsageIcon from "@mui/icons-material/DataUsage";
import CachedIcon from "@mui/icons-material/Cached";
import MenuIcon from "@mui/icons-material/Menu";
import MenuOpenIcon from "@mui/icons-material/MenuOpen";
import HotkeyIcon from "@mui/icons-material/Keyboard";
import PermDataSettingIcon from "@mui/icons-material/PermDataSetting";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import { useTranslation } from "react-i18next";
import { t } from "i18next";
import * as localesRaw from "@mui/material/locale";
import { getCurrentWindow } from "@tauri-apps/api/window";
import PersonIcon from "@mui/icons-material/Person";
import LoginIcon from "@mui/icons-material/Login";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import LogoutIcon from "@mui/icons-material/Logout";
import InfoIcon from "@mui/icons-material/Info";
import { AboutContent } from "../Component/AboutContent";
import { updateLlm } from "../api/plugin";
import { listModels, OllamaModel } from "../api/ollama";
import { createClient } from "@supabase/supabase-js";
import { onOpenUrl } from "@tauri-apps/plugin-deep-link";
import { convertToCamelCase } from "../utils/camelTransfer";
interface Option {
  value: string;
  label: string;
  icon: React.ReactNode;
}

interface IconSelectorProps {
  options: Option[];
  onChange: (value: string) => void;
  defaultValue?: string;
}

interface LlmConfigs {
  // 由于 llmConfigs 为空对象，我们暂时将其定义为任意键值对
  [key: string]: any;
}

interface DataTypeSuffix {
  dataType: string;
  suffix: string[];
}

interface AdvancedConfigs {
  waitForSearchTasksTimeoutInMills: number;
  isDeleteUsnOnExit: boolean;
  restartMonitorDiskThreadTimeoutInMills: number;
  isReadPictureByLLM: boolean;
  isEnableContentIndex: boolean;
  minCacheBlockNumber: number;
  maxCacheBlockNumber: number;
  minGpuCacheBlockNumber: number;
  maxGpuCacheBlockNumber: number;
}

interface AppConfig {
  [key: string]: any;
}

interface CoreConfig {
  cacheNumLimit: number;
  updateTimeLimit: number;
  ignorePath: string[];
  priorityFolder: string;
  disks: string[];
  isEnableGpuAccelerate: boolean;
  gpuDevice: string;
  searchThreadNumber: number;
  llm: string;
  llmConfigs: LlmConfigs;
  dataTypeSuffixMap: DataTypeSuffix[];
  isEnableFuzzyMatch: boolean;
  advancedConfigs: AdvancedConfigs;
}

interface UserInfo {
  avatar?: string;
  username?: string;
  email?: string;
  license?: {
    type: string;
    expireDate: string;
  };
}

interface LicenseProps {
  id: string;
  licenseType: string;
  status: string;
  expiresAt: string;
  createdAt: string;
  trial: boolean;
  autoRefresh: boolean;
}

const isValidPath = (path: string): boolean => {
  // 这里使用一个简单的正则表达式来验证路径
  // 您可能需要根据具体需求调整这个正则表达式
  const pathRegex = /^[a-zA-Z]:\\(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]*$/;
  return pathRegex.test(path);
};

const IconSelector: React.FC<IconSelectorProps> = ({
  options,
  onChange,
  defaultValue,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedSearchEngine, setSelectedSearchEngine] = useState(
    defaultValue || options[0].value
  );

  const handleSelectSearchEngine = (value: string) => {
    setSelectedSearchEngine(value);
    setIsOpen(false);
    onChange(value);
  };

  const selectedSearchEngineOption = options.find(
    (option) => option.value === selectedSearchEngine
  );

  return (
    <div className="flex items-center">
      <label className="mr-4 font-medium">Choose default search engine:</label>
      <div className="relative w-48">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full p-2 border border-gray-300 rounded flex items-center justify-between bg-white"
        >
          <span className="flex items-center">
            {selectedSearchEngineOption?.icon}
            <span className="ml-2">{selectedSearchEngineOption?.label}</span>
          </span>
          <span className="transition-transform duration-200 ease-in-out">
            {isOpen ? <IoIosArrowUp /> : <IoIosArrowDown />}
          </span>
        </button>
        {isOpen && (
          <div className="absolute top-full left-0 w-full mt-1 bg-white border border-gray-300 rounded shadow-lg z-10">
            {options.map((option) => (
              <button
                key={option.value}
                onClick={() => handleSelectSearchEngine(option.value)}
                className="w-full p-2 text-left hover:bg-gray-100 flex items-center"
              >
                {option.icon}
                <span className="ml-2">{option.label}</span>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

const IgnorePathList: React.FC<{
  paths: string[];
  onAdd: () => void;
  onRemove: (index: number) => void;
}> = ({ paths, onAdd, onRemove }) => {
  return (
    <div className="border rounded-md dark:border-gray-700">
      <div className="flex justify-between items-center p-2 bg-gray-100 dark:bg-gray-800 border-b dark:border-gray-700">
        <h4 className="font-medium dark:text-white">
          {t("settings.index.ignoreDirectories")}
        </h4>
        <div>
          <button
            onClick={onAdd}
            className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full"
          >
            <AiOutlinePlus />
          </button>
        </div>
      </div>
      <ul className="max-h-60 overflow-y-auto dark:bg-gray-900">
        {paths.map((path, index) => (
          <li
            key={index}
            className="flex justify-between items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-800 dark:text-gray-200"
          >
            <span className="truncate flex-grow">{path}</span>
            <button
              onClick={() => onRemove(index)}
              className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full"
            >
              <AiOutlineMinus />
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

const TabContent = ({ title, children, config }) => (
  <div className="flex flex-col px-4 overflow-y-auto ">
    <h2 className="text-xl font-bold mt-4 mb-4">{title}</h2>
    {config ? children : <Skeleton variant="rectangular" className="w-full" />}
    <Divider />
  </div>
);

interface SuffixRowProps {
  dataType: string;
  suffixList: string[];
  deleteSuffix: (dataType: string, suffix: string[]) => void;
  addSuffix: (dataType: string, suffix: string) => void;
  moveSuffix: (dataType: string, dragIndex: number, hoverIndex: number) => void;
  deleteDataType: (dataType: string) => void;
  provided: any;
  snapshot: any;
  appConfig: any;
}

const SuffixRow: React.FC<SuffixRowProps> = ({
  dataType,
  suffixList,
  deleteSuffix,
  addSuffix,
  moveSuffix,
  deleteDataType,
  provided,
  snapshot,
  appConfig,
}) => {
  const [openSuffixMap, setOpenSuffixMap] = useState(false);
  const [selectedSuffix, setSelectedSuffix] = useState<string[]>([]);
  const [openSuffixDialog, setOpenSuffixDialog] = useState(false);
  const { t } = useTranslation();

  const deleteSuffixFunc = () => {
    ask(t("settings.messages.confirmDelete"), {
      title: t("settings.messages.confirmDeleteTitle"),
    }).then((yes) => {
      if (yes) {
        // 删除选中的后缀
        deleteSuffix(dataType, selectedSuffix);
        setSelectedSuffix([]);
      }
    });
  };

  const addSuffixFunc = (dataType: string, suffix: string) => {
    addSuffix(dataType, suffix);
  };

  const handleCloseSuffixDialog = () => {
    document.getElementById("focus-element").focus();
    setOpenSuffixDialog(false);
  };

  const handleDragEnd = (result) => {
    if (!result.destination) {
      return;
    }
    moveSuffix(dataType, result.source.index, result.destination.index);
  };

  const deleteDataTypeFunc = () => {
    deleteDataType(dataType);
  };

  return (
    <React.Fragment>
      <Dialog
        open={openSuffixDialog}
        onClose={handleCloseSuffixDialog}
        PaperProps={{
          component: "form",
          onSubmit: (event: React.FormEvent<HTMLFormElement>) => {
            event.preventDefault();
            const formData = new FormData(event.currentTarget);
            const formJson = Object.fromEntries((formData as any).entries());
            addSuffixFunc(dataType, formJson.suffix);
            handleCloseSuffixDialog();
          },
        }}
      >
        <div className="p-4 text-center">
          <DialogTitle>{t("settings.dataType.add")}</DialogTitle>
          <DialogContentText>
            {t("settings.dataType.enterDataType", { dataType })}
          </DialogContentText>
          <TextField
            autoFocus
            required
            margin="dense"
            id="suffix"
            name="suffix"
            label={t("settings.dataType.suffix")}
            type="text"
            fullWidth
            variant="standard"
          />
          <DialogActions>
            <Button onClick={handleCloseSuffixDialog}>
              {t("settings.buttons.cancel")}
            </Button>
            <Button type="submit">{t("settings.buttons.add")}</Button>
          </DialogActions>
        </div>
      </Dialog>

      <TableRow
        ref={provided.innerRef}
        {...provided.draggableProps}
        {...provided.dragHandleProps}
        style={{
          ...provided.draggableProps.style,
          opacity: snapshot.isDragging ? 0.5 : 1,
        }}
        sx={{ "& > *": { borderBottom: "unset" } }}
      >
        <TableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setOpenSuffixMap(!openSuffixMap)}
          >
            {openSuffixMap ? (
              <KeyboardArrowUpIcon />
            ) : (
              <KeyboardArrowDownIcon />
            )}
          </IconButton>
        </TableCell>
        <TableCell component="th" scope="row">
          <Grid2 container>
            <Grid2 size={8} className="content-center">
              {dataType}
            </Grid2>
            <Grid2 size={2}>
              <Button
                variant="contained"
                color="error"
                onClick={deleteDataTypeFunc}
              >
                {t("settings.dataType.delete")}
              </Button>
            </Grid2>
          </Grid2>
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={6}>
          <Collapse in={openSuffixMap} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Grid2 container>
                <Grid2 size={8}>
                  <Typography
                    variant="h6"
                    gutterBottom
                    component="div"
                    className="place-content-center"
                  >
                    {t("settings.dataType.suffixTable")}
                  </Typography>
                </Grid2>
                <Grid2 size={2}>
                  <Button
                    variant="contained"
                    onClick={() => setOpenSuffixDialog(true)}
                  >
                    {t("settings.buttons.add")}
                  </Button>
                </Grid2>
                <Grid2 size={2}>
                  <Button
                    variant="outlined"
                    color="error"
                    className="place-content-center"
                    onClick={deleteSuffixFunc}
                  >
                    {t("settings.buttons.delete")}
                  </Button>
                </Grid2>
              </Grid2>
              <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId={`suffixList-${dataType}`}>
                  {(provided) => (
                    <Table
                      size="small"
                      aria-label="suffix-table"
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                    >
                      <TableHead>
                        <TableRow>
                          <TableCell padding="checkbox"></TableCell>
                          <TableCell>{t("settings.dataType.suffix")}</TableCell>
                          <TableCell>
                            {t("settings.dataType.description")}
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {suffixList.map((suffix, index) => (
                          <Draggable
                            key={suffix}
                            draggableId={suffix}
                            index={index}
                          >
                            {(provided, snapshot) => (
                              <TableRow
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                style={{
                                  ...provided.draggableProps.style,
                                  opacity: snapshot.isDragging ? 0.5 : 1,
                                }}
                              >
                                <TableCell padding="checkbox">
                                  <Checkbox
                                    color="primary"
                                    checked={selectedSuffix.includes(suffix)}
                                    onChange={(event) => {
                                      const checked = event.target.checked;
                                      if (checked) {
                                        setSelectedSuffix(
                                          Array.from(
                                            new Set([...selectedSuffix, suffix])
                                          )
                                        );
                                      } else {
                                        setSelectedSuffix(
                                          selectedSuffix.filter(
                                            (item) => item !== suffix
                                          )
                                        );
                                      }
                                    }}
                                  />
                                </TableCell>
                                <TableCell component="th" scope="row">
                                  {suffix}
                                </TableCell>
                                <TableCell component="th" scope="row">
                                  {appConfig?.suffix_description[suffix] ||
                                    t("settings.dataType.noDescription")}
                                </TableCell>
                              </TableRow>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </TableBody>
                    </Table>
                  )}
                </Droppable>
              </DragDropContext>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
};

// 添加一个标签组件
const SettingLabel = ({
  type,
}: {
  type: "restart" | "reindex" | "instant";
}) => {
  const { t } = useTranslation();

  const getColor = () => {
    switch (type) {
      case "restart":
        return "warning";
      case "reindex":
        return "error";
      case "instant":
        return "success";
      default:
        return "default";
    }
  };

  const getLabel = () => {
    switch (type) {
      case "restart":
        return t("settings.effectiveMode.restart");
      case "reindex":
        return t("settings.effectiveMode.reindex");
      case "instant":
        return t("settings.effectiveMode.instant");
      default:
        return "";
    }
  };

  return (
    <Chip size="small" color={getColor()} label={getLabel()} sx={{ ml: 1 }} />
  );
};

const SettingsLayout: React.FC = () => {
  const { t, i18n } = useTranslation();
  const [config, setConfig] = useState<CoreConfig | null>(null);
  const [appConfig, setAppConfig] = useState<AppConfig | null>(null);
  const [showAddDiskDialog, setShowAddDiskDialog] = useState(false);
  const [availableDisks, setAvailableDisks] = useState<string[]>([]);
  const [resultCache, setResultCache] = useState<string[]>([]);
  const [resultCachePageResult, setResultCachePageResult] = useState<string[]>(
    []
  );
  const [resultCachePage, setResultCachePage] = useState(0);
  const [resultCachePageSize, setResultCachePageSize] = useState(10);
  const [selectedCache, setSelectedCache] = useState<string[]>([]);
  const [searchCacheKeyword, setSearchCacheKeyword] = useState<string>("");
  const [startup, setStartup] = useState<boolean>(false);
  const [gpuDevice, setGpuDevice] = useState<object>({});
  const [selectedGpuDevice, setSelectedGpuDevice] = useState<string>("");
  const [menuOpen, setMenuOpen] = useState(true);
  const [menuAnimationDone, setMenuAnimationDone] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [globalHotkey, setGlobalHotkey] = useState<string>("");
  const [errorConfigMessage, setErrorConfigMessage] = useState<string[]>([]);
  const [showErrorConfigDialog, setShowErrorConfigDialog] = useState(false);
  const [openAddDataTypeDialog, setOpenAddDataTypeDialog] = useState(false);
  const [newDataType, setNewDataType] = useState("");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [dropPreviousIndex, setDropPreviousIndex] = useState<boolean>(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userInfo, setUserInfo] = useState<UserInfo>({});
  const [isScrollingByClick, setIsScrollingByClick] = useState(false);
  const [modelList, setModelList] = useState<OllamaModel[]>([]);
  const [loading, setLoading] = useState(false);
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  const supabase = createClient(supabaseUrl, supabaseAnonKey);
  const [systemDarkTheme, setSystemDarkTheme] = useState(
    window.matchMedia("(prefers-color-scheme: dark)").matches
  );

  // 使用 Intl.DisplayNames 获取语言的本地化显示名称
  const getLocaleName = (locale: string) => {
    try {
      // 使用目标语言的语言名称
      const targetLocale = locale.split("-")[0];
      return new Intl.DisplayNames([targetLocale], { type: "language" }).of(
        targetLocale
      );
    } catch (e) {
      // 如果不支持 Intl.DisplayNames，则返回原始语言代码
      console.error("getLocaleName error", e);
      return locale;
    }
  };

  // 从i18n获取支持的语言列表
  const locales = Object.keys(i18n.options.resources);

  // 监听系统主题变化
  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

    const handleThemeChange = (e: MediaQueryListEvent) => {
      setSystemDarkTheme(e.matches);
    };

    // 添加监听器
    mediaQuery.addEventListener("change", handleThemeChange);

    // 清理函数
    return () => {
      mediaQuery.removeEventListener("change", handleThemeChange);
    };
  }, []);

  const defaultLocale = navigator.language;
  const localeTheme = useTheme();
  const themeWithLocale = React.useMemo(
    () =>
      createTheme(
        {
          palette: {
            mode: systemDarkTheme ? "dark" : "light",
            background: {
              default: systemDarkTheme ? "#121212" : "#ffffff",
              paper: systemDarkTheme ? "#1e1e1e" : "#f5f5f5",
            },
            divider: systemDarkTheme
              ? "rgba(255, 255, 255, 0.12)"
              : "rgba(0, 0, 0, 0.12)",
          },
        },
        localesRaw[
          appConfig?.locale.replace("-", "") || defaultLocale.replace("-", "")
        ]
      ),
    [appConfig?.locale, localeTheme, systemDarkTheme]
  );

  const sectionsRef = useRef<HTMLDivElement[]>([]);

  const initSettingsRetry = () => {
    initSettings()
      .then(() => {
        console.log("init success");
      })
      .catch((err) => {
        console.error("init settings failed", err);
        setTimeout(() => {
          initSettingsRetry();
        }, 500);
      });
  };

  const initSettings = async () => {
    const res = await getConfig();
    if (res.data) {
      const updatedConfig = {
        ...res.data,
        ignorePath: res.data.ignorePath
          ? res.data.ignorePath.split(",").filter(Boolean)
          : [],
        disks: res.data.disks ? res.data.disks.split(",").filter(Boolean) : [],
      };
      setConfig(updatedConfig);
      console.log("coreConfigs", updatedConfig);
      getGpu().then((gpuRes) => {
        setGpuDevice(gpuRes.data);
        const usedGpu = Object.entries(gpuRes.data).filter((eachEntry) => {
          return eachEntry[1] === updatedConfig.gpuDevice;
        });
        if (usedGpu.length > 0) {
          setSelectedGpuDevice(usedGpu[0][0]);
        }
      });
    } else {
      setConfig(null);
    }

    const appConfigRes = await readAppConfig();

    if (appConfigRes) {
      setAppConfig(appConfigRes);
      setGlobalHotkey(
        appConfigRes.hotkey.modifiers + " + " + appConfigRes.hotkey.key
      );
      if (appConfigRes.locale) {
        i18n.changeLanguage(appConfigRes.locale);
      }
    } else {
      setAppConfig(null);
    }
    setSearchCacheKeyword("");
    handleChangeSearchCacheKeyword(null);

    const startupRes = await hasStartup();
    setStartup(startupRes);
  };

  useLayoutEffect(() => {
    initSettingsRetry();
  }, []);

  useEffect(() => {
    const newArr = [...resultCache].slice(
      resultCachePage * resultCachePageSize,
      resultCachePage * resultCachePageSize + resultCachePageSize
    );
    setResultCachePageResult(newArr);
  }, [resultCache, resultCachePage, resultCachePageSize]);

  const tabs = [
    {
      id: "account",
      label: t("settings.tabs.account"),
      icon: <AccountCircleIcon />,
    },
    {
      id: "general",
      label: t("settings.tabs.general"),
      icon: <SettingsIcon />,
    },
    { id: "index", label: t("settings.tabs.index"), icon: <StorageIcon /> },
    { id: "search", label: t("settings.tabs.search"), icon: <SearchIcon /> },
    {
      id: "dataType",
      label: t("settings.tabs.dataType"),
      icon: <DataUsageIcon />,
    },
    { id: "cache", label: t("settings.tabs.cache"), icon: <CachedIcon /> },
    { id: "hotkey", label: t("settings.tabs.hotkey"), icon: <HotkeyIcon /> },
    {
      id: "advanced",
      label: t("settings.tabs.advanced"),
      icon: <PermDataSettingIcon />,
    },
    {
      id: "about",
      label: t("settings.tabs.about"),
      icon: <InfoIcon />,
    },
  ];

  const updateIndex = (isDropPrevious: boolean) => {
    ask(t("settings.messages.confirmUpdateIndex"), {
      title: t("settings.messages.confirmUpdateIndexTitle"),
      kind: "warning",
    }).then(() => {
      dbStatusApi().then((res) => {
        if (res.data === "NORMAL") {
          updateIndexApi(isDropPrevious).then(() => {
            setSnackbarMessage(t("settings.messages.updatingIndex"));
            setOpenSnackbar(true);
          });
        } else if (res.data === "MANUAL_UPDATE" || res.data === "_TEMP") {
          setSnackbarMessage(t("settings.messages.alreadyUpdatingIndex"));
          setOpenSnackbar(true);
        } else if (res.data === "VACUUM") {
          setSnackbarMessage(t("settings.messages.stillOptimizing"));
          setOpenSnackbar(true);
        }
      });
    });
  };

  const handleScroll = () => {
    // 如果是点击触发的滚动,则忽略
    if (isScrollingByClick) return;

    const sections = sectionsRef.current;

    if (sections) {
      // 使用 settings-content 类名来获取滚动容器
      const scrollContainer = document.querySelector(".settings-content");
      if (!scrollContainer) return;

      // 获取滚动位置
      const scrollPosition = scrollContainer.scrollTop;

      // 找到当前可见的section
      let currentSection = 0;
      sections.forEach((section, index) => {
        if (!section) return;
        const sectionTop = section.offsetTop;
        if (scrollPosition >= sectionTop - 100) {
          currentSection = index;
        }
      });

      // 更新活动标签
      setActiveTab(currentSection);

      // 同步滚动左侧标签栏
      const tabsContainer = document.querySelector(".left-tabs");
      const activeTabEle =
        tabsContainer?.querySelectorAll(".left-tab")[activeTab];
      if (tabsContainer && activeTabEle) {
        activeTabEle.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
        });
      }
    }
  };

  useEffect(() => {
    // 使用相同的 settings-content 选择器
    const scrollContainer = document.querySelector(".settings-content");
    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", handleScroll);
      return () => {
        scrollContainer.removeEventListener("scroll", handleScroll);
      };
    }
  }, [activeTab, isScrollingByClick]);

  const handleDeleteCache = () => {
    // 生成一个dialog，确认是否删除
    ask(t("settings.messages.confirmDelete"), {
      title: t("settings.messages.confirmDeleteTitle"),
      kind: "warning",
    }).then((yes) => {
      if (yes) {
        // 删除选中的缓存
        const promises = selectedCache.map((path) => deleteCache(path));
        Promise.all(promises).then(() => {
          // 删除成功后，重新获取缓存列表
          handleChangeSearchCacheKeyword(null);
        });

        setSelectedCache([]);
        setSearchCacheKeyword("");
      }
    });
  };

  const handleChangeSearchCacheKeyword = (keywords: string) => {
    setSearchCacheKeyword(keywords);
    if (keywords) {
      getCache().then((res) => {
        if (res.data) {
          setResultCachePage(0);
          setResultCache(
            res.data.filter((path: string) => {
              return path.toLowerCase().includes(keywords.toLowerCase());
            })
          );
        }
      });
    } else {
      getCache().then((res) => {
        if (res.data) {
          setResultCachePage(0);
          setResultCache(res.data);
        }
      });
    }
  };

  const handleChangeSearchEngine = (event) => {
    const searchEngine = event.target.value;
    if (searchEngine) {
      setAppConfig({
        ...appConfig,
        default_search_engine: searchEngine,
      });
    }
  };

  const handleChangeGpuDevice = (device: string) => {
    if (device) {
      setSelectedGpuDevice(device);
      const newConfig = {
        ...config,
        gpuDevice: gpuDevice[device],
        isEnableGpuAccelerate: true,
      } as CoreConfig;
      setConfig(newConfig);
    } else {
      setSelectedGpuDevice("");
      const newConfig = {
        ...config,
        gpuDevice: "",
        isEnableGpuAccelerate: false,
      } as CoreConfig;
      setConfig(newConfig);
    }
  };

  const handleChangePage = (
    event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number
  ) => {
    setResultCachePage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setResultCachePageSize(parseInt(event.target.value) ?? 10);
    setResultCachePage(0);
  };

  const handleDiskSelect = (newDisk: string) => {
    if (newDisk && !config?.disks.includes(newDisk)) {
      setConfig({
        ...config,
        disks: [...config.disks, newDisk],
      } as CoreConfig);
    }
    document.getElementById("focus-element").focus();
    setShowAddDiskDialog(false);
  };

  const handleSetStartup = (checked) => {
    setStartup(checked);
    if (checked) {
      addStartup();
    } else {
      deleteStartup();
    }
  };

  const handleAddNewDisk = () => {
    getDisk().then((disks) => {
      setAvailableDisks(disks.data);
      setShowAddDiskDialog(true);
    });
  };

  const handleAddPriorityFolder = async () => {
    const selected = await openDialog({
      directory: true,
      multiple: false,
    });
    if (selected) {
      if (config) {
        setConfig({
          ...config,
          priorityFolder: selected,
        } as CoreConfig);
      }
    }
  };

  const handleAddSuffix = (dataType: string, suffix: string) => {
    const dataTypeIndex = config.dataTypeSuffixMap.findIndex(
      (item) => item.dataType === dataType
    );
    if (dataTypeIndex !== -1) {
      const newSuffixList = Array.from(
        new Set([...config.dataTypeSuffixMap[dataTypeIndex].suffix, suffix])
      );
      const newDataTypeSuffixMap = [...config.dataTypeSuffixMap];
      newDataTypeSuffixMap[dataTypeIndex].suffix = newSuffixList;
      setConfig({ ...config, dataTypeSuffixMap: newDataTypeSuffixMap });
    }
  };

  const handleDeleteSuffix = (dataType: string, suffix: string[]) => {
    const dataTypeIndex = config.dataTypeSuffixMap.findIndex(
      (item) => item.dataType === dataType
    );
    if (dataTypeIndex !== -1) {
      const filteredSuffixList = config.dataTypeSuffixMap[
        dataTypeIndex
      ].suffix.filter((item) => !suffix.includes(item));
      const newDataTypeSuffixMap = [...config.dataTypeSuffixMap];
      newDataTypeSuffixMap[dataTypeIndex].suffix = filteredSuffixList;
      setConfig({ ...config, dataTypeSuffixMap: newDataTypeSuffixMap });
    }
  };

  const handleMoveSuffix = (
    dataType: string,
    dragIndex: number,
    hoverIndex: number
  ) => {
    const dataTypeIndex = config.dataTypeSuffixMap.findIndex(
      (item) => item.dataType === dataType
    );
    if (dataTypeIndex !== -1) {
      const suffixList = config.dataTypeSuffixMap[dataTypeIndex].suffix;
      const [draggedSuffix] = suffixList.splice(dragIndex, 1);
      suffixList.splice(hoverIndex, 0, draggedSuffix);
      const newDataTypeSuffixMap = [...config.dataTypeSuffixMap];
      newDataTypeSuffixMap[dataTypeIndex].suffix = suffixList;
      setConfig({ ...config, dataTypeSuffixMap: newDataTypeSuffixMap });
    }
  };

  const handleAddIgnorePath = async () => {
    const selected = await openDialog({
      directory: true,
      multiple: true,
    });

    if (Array.isArray(selected)) {
      const newPathArr = selected;
      if (config) {
        const newIgnorePath = [...config.ignorePath];
        newPathArr.forEach((newPath) => {
          if (!newIgnorePath.includes(newPath)) {
            newIgnorePath.push(newPath);
          }
        });
        setConfig({
          ...config,
          ignorePath: [...newIgnorePath],
        } as CoreConfig);
      }
    }
  };

  const handleRemoveIgnorePath = (index: number) => {
    if (config) {
      const newIgnorePaths = config.ignorePath.filter((_, i) => i !== index);
      setConfig({ ...config, ignorePath: newIgnorePaths });
    }
  };

  const handleSetHotkey = (
    event,
    setKey: React.Dispatch<React.SetStateAction<string>>,
    allowMultiple: boolean
  ) => {
    event.preventDefault();
    const key = event.key.toUpperCase();
    if (key === "BACKSPACE" || key === "DELETE") {
      setKey("");
      return;
    }
    const modifiers = [];
    let pressKey = "";
    if (event.ctrlKey) modifiers.push("CONTROL");
    if (event.altKey) modifiers.push("ALT");
    if (event.shiftKey) modifiers.push("SHIFT");
    if (event.metaKey) modifiers.push("META");

    const code = event.code;
    if (
      key !== "CONTROL" &&
      key !== "ALT" &&
      key !== "SHIFT" &&
      key !== "META" &&
      allowMultiple
    ) {
      pressKey = code;
    }
    const hotkey = allowMultiple
      ? modifiers.join(" | ") + ` + ${pressKey}`
      : modifiers[0];
    setKey(hotkey || "");
    console.log("Hotkey set to:", hotkey);
  };

  const handleAddDataType = () => {
    if (
      newDataType &&
      !config.dataTypeSuffixMap.some((item) => item.dataType === newDataType)
    ) {
      const newConfig = {
        ...config,
        dataTypeSuffixMap: [
          ...config.dataTypeSuffixMap,
          { dataType: newDataType, suffix: [] },
        ],
      };
      setConfig(newConfig);
      setNewDataType("");
      setOpenAddDataTypeDialog(false);
    }
  };

  const handleDeleteDataType = (dataTypeToDelete: string) => {
    ask("Are you sure to delete the selected data types?", {
      title: "Delete Data Types",
    }).then((yes) => {
      if (yes) {
        const newConfig = {
          ...config,
          dataTypeSuffixMap: config.dataTypeSuffixMap.filter(
            (item) => item.dataType !== dataTypeToDelete
          ),
        };
        setConfig(newConfig);
      }
    });
  };

  const handleCloseSnackbar = (
    event: React.SyntheticEvent | Event,
    reason?: SnackbarCloseReason
  ) => {
    if (reason === "clickaway") {
      return;
    }

    setOpenSnackbar(false);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setIsScrollingByClick(true); // 设置标志表示正在进行点击触发的滚动

    sectionsRef.current[newValue].scrollIntoView({
      behavior: "smooth",
    });

    // 等待滚动动画完成后再重新启用滚动检测
    setTimeout(() => {
      setIsScrollingByClick(false);
    }, 1000);
  };

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) {
      return;
    }

    const { source, destination } = result;

    if (
      source.droppableId === "dataTypeList" &&
      destination.droppableId === "dataTypeList"
    ) {
      const newDataTypeSuffixMap = Array.from(config.dataTypeSuffixMap);
      const [movedDataType] = newDataTypeSuffixMap.splice(source.index, 1);
      newDataTypeSuffixMap.splice(destination.index, 0, movedDataType);
      setConfig({ ...config, dataTypeSuffixMap: newDataTypeSuffixMap });
    } else if (
      source.droppableId.startsWith("suffixList-") &&
      destination.droppableId.startsWith("suffixList-")
    ) {
      const dataType = source.droppableId.split("-")[1];
      const dataTypeIndex = config.dataTypeSuffixMap.findIndex(
        (item) => item.dataType === dataType
      );
      if (dataTypeIndex !== -1) {
        const suffixList = Array.from(
          config.dataTypeSuffixMap[dataTypeIndex].suffix
        );
        const [draggedSuffix] = suffixList.splice(source.index, 1);
        suffixList.splice(destination.index, 0, draggedSuffix);
        const newDataTypeSuffixMap = [...config.dataTypeSuffixMap];
        newDataTypeSuffixMap[dataTypeIndex].suffix = suffixList;
        setConfig({ ...config, dataTypeSuffixMap: newDataTypeSuffixMap });
      }
    }
  };

  const handleOllamaConfig = () => {
    try {
      // 在保存前确保 OLLAMA 配置对象存在
      const ollamaConfig = config.llmConfigs?.OLLAMA || {};
      const newConfig = {
        ...config,
        llmConfigs: {
          ...config.llmConfigs,
          OLLAMA: {
            ...ollamaConfig,
            address: ollamaConfig.address || "http://localhost:11434",
            modelType: ollamaConfig.modelType || "",
          },
        },
      };
      setConfig(newConfig);
    } catch (error) {
      console.error("Save config failed:", error);
    }
  };

  const saveAppConfig = (closeWindow: boolean) => {
    setErrorConfigMessage([]);
    const errorList = [];
    handleOllamaConfig();
    // 保存配置
    const updatedConfig = {
      ...config,
      ignorePath: config.ignorePath.join(","),
      disks: config.disks.join(","),
    };

    // 保存app配置
    let globalModifiers: string = null;
    let globalKey: string = null;
    const globalModifiersAndKey = globalHotkey?.split("+");
    if (globalModifiersAndKey.length > 1) {
      globalModifiers = globalModifiersAndKey[0]
        ?.split(" | ")
        .map((item) => item.trim())
        .join(" | ");
      globalKey = globalModifiersAndKey[1]?.trim();
      if (!globalModifiers || !globalKey) {
        const errorMessage = t("settings.messages.invalidGlobalHotkey");
        errorList.push(errorMessage);
      }
    } else {
      const errorMessage = t("settings.messages.invalidGlobalHotkey");
      errorList.push(errorMessage);
    }

    if (errorList.length > 0) {
      setErrorConfigMessage(errorList);
      setShowErrorConfigDialog(true);
      // 显示错误并退出
      return;
    }

    const newAppConfig = {
      ...appConfig,
      hotkey: {
        modifiers: globalModifiers,
        key: globalKey,
      },
    };
    console.log("newAppConfig", newAppConfig);
    setAppConfig(newAppConfig);
    // 保存app设置
    setAppConfigApi(newAppConfig)
      .then(() => {
        setConfigApi(updatedConfig)
          .then(() => {
            updateLlm()
              .then(() => {
                console.log("Update plugin service llm success");
              })
              .catch((err) => {
                console.error(err);
              });
            if (closeWindow) {
              getCurrentWindow().close();
            } else {
              setSnackbarMessage(t("settings.messages.saveSuccess"));
              setOpenSnackbar(true);
            }
          })
          .catch((err) => {
            console.error(err);
            setSnackbarMessage(t("settings.messages.saveFailed"));
            setOpenSnackbar(true);
          });
      })
      .catch((err) => {
        console.error(err);
        setSnackbarMessage(t("settings.messages.saveFailed"));
        setOpenSnackbar(true);
      });
  };

  const fetchLicense = async () => {
    const { data, error } = await supabase.from("licenses").select("*");
    if (error) {
      console.error(error);
    }
    console.log(data.map((item) => convertToCamelCase(item)));
    return data ? data.map((item) => convertToCamelCase(item)) : [];
  };

  useEffect(() => {
    onOpenUrl((url) => {
      console.log("Received URL:", url);
      // Handle aiverything://login/ URLs
      if (url[0].startsWith("aiverything://login/")) {
        // Extract tokens using URLSearchParams for proper parsing
        const urlObj = new URL(url[0]);
        const searchParams = new URLSearchParams(urlObj.search);
        const access_token = searchParams.get("access_token");
        const refresh_token = searchParams.get("refresh_token");

        if (access_token) {
          console.log("Auth tokens received, setting session");

          // Set the Supabase session with the received tokens
          supabase.auth
            .setSession({
              access_token,
              refresh_token: refresh_token || null,
            })
            .then(async ({ data, error }) => {
              if (error) {
                console.error("Error setting Supabase session:", error);
                setSnackbarMessage(t("settings.account.loginFailed"));
                setOpenSnackbar(true);
                return;
              }

              // Get user data
              const { data: userData, error: userError } =
                await supabase.auth.getUser();

              if (userError || !userData?.user) {
                console.error("Failed to get user data:", userError);
                setSnackbarMessage(t("settings.account.loginFailed"));
                setOpenSnackbar(true);
                return;
              }

              // Set user info in app state
              setIsLoggedIn(true);
              setUserInfo({
                avatar: userData.user.user_metadata?.avatar_url,
                username: userData.user.user_metadata?.name,
                email: userData.user.email,
                license: {
                  type: "Free",
                  expireDate: "Forever",
                },
              });

              setSnackbarMessage(t("settings.account.loginSuccess"));
              setOpenSnackbar(true);
            });
        }
      } else {
        console.log("Received URL:", url);
      }
    });
  }, []);

  useEffect(() => {
    // Check if a session exists
    supabase.auth
      .getSession()
      .then((session) => {
        if (session) {
          // Get user data
          supabase.auth.getUser().then(({ data, error }) => {
            if (error || !data?.user) {
              console.error("Failed to get user data:", error);
              return;
            }
            // Set user info in app state
            setIsLoggedIn(true);
            setUserInfo({
              avatar: data.user.user_metadata?.avatar_url,
              username: data.user.user_metadata?.name,
              email: data.user.email,
              license: {
                type: "Free",
                expireDate: "Forever",
              },
            });
          });
        }
      })
      .catch((error) => {
        console.error("Failed to get Supabase session:", error);
      });
  }, []);

  const handleLogout = async () => {
    supabase.auth.signOut();
    setIsLoggedIn(false);
    setUserInfo({});
  };

  const handleLogin = async () => {
    try {
      // Import the open function if not already imported at the top
      const { open } = await import("@tauri-apps/plugin-shell");
      const loginUrl = "https://aiverything.me/applogin";
      open(loginUrl);
      // Optional: Show a notification or status message
      setSnackbarMessage(t("settings.account.loginRedirect"));
      setOpenSnackbar(true);
    } catch (error) {
      console.error("Failed to open browser:", error);
      setSnackbarMessage(t("settings.messages.browserOpenFailed"));
      setOpenSnackbar(true);
    }
  };

  const fetchModels = async () => {
    try {
      setLoading(true);
      const address =
        config?.llmConfigs?.OLLAMA?.address || "http://localhost:11434";
      const response = await listModels(address);
      const models = response.models || [];
      setModelList(models);
    } catch (error) {
      console.error("Failed to fetch Ollama models:", error);
      setModelList([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 使用 setTimeout 实现防抖
    let timeoutId: NodeJS.Timeout;

    if (config?.llmConfigs?.OLLAMA?.address) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        fetchModels();
      }, 500);
    }

    // 清理 timeout
    return () => {
      clearTimeout(timeoutId);
    };
  }, [config?.llmConfigs?.OLLAMA?.address]);

  return (
    <ThemeProvider theme={themeWithLocale}>
      <div className="flex flex-col h-screen w-full dark:bg-gray-900">
        <div className="flex flex-start items-center pb-4 border-b mt-2">
          <IconButton onClick={() => setMenuOpen(!menuOpen)}>
            {menuOpen ? <MenuOpenIcon /> : <MenuIcon />}
          </IconButton>
          <h1 className="text-2xl font-bold">{t("settings.title")}</h1>
        </div>
        {/* 菜单栏和设置界面 */}
        <div className="flex flex-1 overflow-x-hidden">
          <Collapse
            orientation="horizontal"
            onEnter={() => setMenuAnimationDone(false)}
            onExited={() => setMenuAnimationDone(true)}
            in={menuOpen}
            collapsedSize={100}
          >
            <div className="overflow-y-auto h-full">
              <Tabs
                orientation="vertical"
                variant="scrollable"
                value={activeTab}
                onChange={handleTabChange}
                className="border-r left-tabs"
                style={{
                  overflowX: "hidden",
                  whiteSpace: "nowrap",
                }}
              >
                {tabs.map((tab) => (
                  <Tab
                    key={tab.id}
                    label={
                      (menuOpen || !menuAnimationDone) && (
                        <span
                          style={{
                            opacity: menuOpen ? 1 : 0,
                            transition: "opacity 0.3s",
                          }}
                        >
                          {tab.label}
                        </span>
                      )
                    }
                    icon={tab.icon}
                    iconPosition="start"
                    className="left-tab"
                    style={{
                      justifyContent: "flex-start",
                      overflow: "hidden",
                      whiteSpace: "nowrap",
                      textOverflow: "ellipsis",
                      textAlign: "left",
                    }}
                  />
                ))}
              </Tabs>
            </div>
          </Collapse>
          {/* 设置界面 */}
          <div className="flex flex-col flex-1 overflow-y-auto py-2">
            {/* 设置内容界面 */}
            <div className="flex flex-col flex-1 overflow-y-auto py-2 justify-between settings-content">
              <DragDropContext onDragEnd={handleDragEnd}>
                <div ref={(el) => (sectionsRef.current[0] = el)} id="account">
                  <TabContent
                    title={t("settings.account.title")}
                    config={config}
                  >
                    <div className="flex flex-col p-6">
                      <Paper elevation={3} className="p-8">
                        <div className="flex items-center space-x-12">
                          {/* 头像部分 */}
                          <div className="relative group">
                            <div className="w-32 h-32 rounded-full overflow-hidden bg-gray-100 shadow-md transition-transform duration-300 transform group-hover:scale-105">
                              {isLoggedIn ? (
                                <img
                                  src={userInfo.avatar}
                                  alt="User avatar"
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
                                  <PersonIcon
                                    sx={{ fontSize: 64 }}
                                    className="text-gray-400"
                                  />
                                </div>
                              )}
                            </div>
                          </div>

                          {/* 用户信息部分 */}
                          <div className="flex-grow">
                            {isLoggedIn ? (
                              <Stack spacing={3}>
                                <div className="flex justify-between items-center">
                                  <div>
                                    <Typography
                                      variant="h5"
                                      className="font-semibold"
                                    >
                                      {userInfo.username}
                                    </Typography>
                                    <Typography
                                      variant="body2"
                                      color="text.secondary"
                                      sx={{ mt: 0.5 }}
                                    >
                                      {userInfo.email}
                                    </Typography>
                                  </div>
                                  <Button
                                    variant="outlined"
                                    color="error"
                                    onClick={() => {
                                      // 添加确认对话框
                                      ask(t("settings.account.confirmLogout"), {
                                        title: t(
                                          "settings.account.confirmLogoutTitle"
                                        ),
                                      }).then((yes) => {
                                        if (yes) {
                                          handleLogout();
                                        }
                                      });
                                    }}
                                    startIcon={<LogoutIcon />}
                                    sx={{
                                      borderRadius: 2,
                                      textTransform: "none",
                                    }}
                                  >
                                    {t("settings.account.logout")}
                                  </Button>
                                </div>
                                <div className="space-y-4">
                                  <Stack
                                    direction="row"
                                    spacing={2}
                                    alignItems="center"
                                  >
                                    <Paper
                                      className="w-full"
                                      variant="outlined"
                                      sx={{
                                        p: 1.5,
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 2,
                                        borderRadius: 2,
                                        bgcolor: (theme) =>
                                          theme.palette.primary.main + "08", // 添加非常淡的主题色背景
                                        borderColor: (theme) =>
                                          theme.palette.primary.main + "30", // 半透明的主题色边框
                                      }}
                                    >
                                      <Chip
                                        label={userInfo.license?.type}
                                        color="primary"
                                        size="small"
                                        sx={{
                                          fontWeight: 500,
                                          fontSize: "0.85rem",
                                          height: 28,
                                        }}
                                      />
                                      <Typography
                                        variant="body2"
                                        sx={{
                                          display: "flex",
                                          alignItems: "center",
                                          gap: 0.5,
                                          color: "text.secondary",
                                          fontSize: "0.85rem",
                                        }}
                                      >
                                        <AccessTimeIcon
                                          sx={{
                                            fontSize: "1rem",
                                            opacity: 0.8,
                                          }}
                                        />
                                        {t("settings.account.expireDate", {
                                          date: userInfo.license?.expireDate,
                                        })}
                                      </Typography>
                                    </Paper>
                                  </Stack>
                                </div>
                              </Stack>
                            ) : (
                              <div className="flex flex-col items-start space-y-4">
                                <Typography
                                  variant="h6"
                                  color="text.secondary"
                                  gutterBottom
                                >
                                  {t("settings.account.tip")}
                                </Typography>
                                <Button
                                  variant="contained"
                                  color="primary"
                                  size="large"
                                  onClick={handleLogin}
                                  startIcon={<LoginIcon />}
                                  sx={{
                                    px: 4,
                                    py: 1.5,
                                    borderRadius: 2,
                                    textTransform: "none",
                                    fontSize: "1.1rem",
                                    boxShadow: 2,
                                    "&:hover": {
                                      boxShadow: 4,
                                    },
                                  }}
                                >
                                  {t("settings.account.login")}
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </Paper>
                    </div>
                  </TabContent>
                </div>
                <div ref={(el) => (sectionsRef.current[1] = el)} id="general">
                  <TabContent
                    title={t("settings.general.title")}
                    config={appConfig}
                  >
                    {config && appConfig && (
                      <FormGroup>
                        {/* 添加开机启动项 */}
                        <FormControl>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={startup}
                                onChange={(event) =>
                                  handleSetStartup(event.target.checked)
                                }
                              />
                            }
                            label={t("settings.general.startup")}
                          />
                          <FormHelperText>
                            {t("settings.general.startupDescription")}
                          </FormHelperText>
                        </FormControl>

                        {/* 是否贴靠任务栏 */}
                        <FormControl>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={appConfig.attach_explorer}
                                onChange={(event) => {
                                  setAppConfig({
                                    ...appConfig,
                                    attach_explorer: event.target.checked,
                                  });
                                }}
                              />
                            }
                            label={t("settings.general.attachExplorer")}
                          />
                          <FormHelperText>
                            {t("settings.general.attachExplorerDescription")}
                          </FormHelperText>
                        </FormControl>
                      </FormGroup>
                    )}
                    {/* 语言设置 */}
                    <div className="my-4">
                      <Stack direction="row" spacing={2}>
                        <FormControl style={{ width: 300 }}>
                          <Autocomplete
                            options={locales}
                            value={appConfig?.locale}
                            disableClearable
                            onChange={(event: any, newValue: string | null) => {
                              i18n.changeLanguage(newValue);
                              setAppConfig({
                                ...appConfig,
                                locale: newValue,
                              });
                            }}
                            // 使用 Intl.DisplayNames 获取语言显示名称
                            getOptionLabel={(option) => getLocaleName(option)}
                            renderInput={(params) => (
                              <TextField
                                {...params}
                                label={t("settings.general.language")}
                                fullWidth
                              />
                            )}
                          />
                          <FormHelperText>
                            {t("settings.general.languageDescription")}
                          </FormHelperText>
                        </FormControl>
                      </Stack>
                    </div>
                    {/* 搜索引擎设置 */}
                    <div className="my-4">
                      <Stack direction="row" spacing={2}>
                        <FormControl>
                          <InputLabel id="search-engine-label">
                            {t("settings.general.searchEngine")}
                          </InputLabel>
                          <Select
                            labelId="search-engine-label"
                            id="search-engine"
                            value={
                              appConfig?.search_engine[
                                appConfig?.search_engine.findIndex(
                                  (eachEngine) =>
                                    eachEngine.name ===
                                    appConfig?.default_search_engine.name
                                )
                              ]
                            }
                            onChange={handleChangeSearchEngine}
                            label={t("settings.general.searchEngine")}
                          >
                            {appConfig?.search_engine.map((eachEngine) => (
                              <MenuItem
                                key={eachEngine.name}
                                value={eachEngine}
                              >
                                <Stack direction="row" spacing={2}>
                                  <div className="place-content-center">
                                    {eachEngine.name === "Google" && (
                                      <FaGoogle />
                                    )}
                                    {eachEngine.name === "Bing" && <BsBing />}
                                    {eachEngine.name === "DuckDuckGo" && (
                                      <SiDuckduckgo />
                                    )}
                                    {eachEngine.name === "Baidu" && <SiBaidu />}
                                    {eachEngine.name !== "Google" &&
                                      eachEngine.name !== "Bing" &&
                                      eachEngine.name !== "DuckDuckGo" &&
                                      eachEngine.name !== "Baidu" && (
                                        <SearchIcon />
                                      )}
                                  </div>
                                  <div>{eachEngine.name}</div>
                                </Stack>
                              </MenuItem>
                            ))}
                          </Select>
                          <FormHelperText>
                            {t("settings.general.searchEngineDescription")}
                          </FormHelperText>
                        </FormControl>
                      </Stack>
                    </div>
                  </TabContent>
                </div>
                <div ref={(el) => (sectionsRef.current[2] = el)} id="index">
                  <TabContent title={t("settings.index.title")} config={config}>
                    {/* 索引设置 */}
                    <div className="my-4">
                      <h3 className="font-medium mb-2 font-bold flex items-center">
                        {t("settings.index.indexingConfigs")}
                        <SettingLabel type="reindex" />
                      </h3>
                      <FormHelperText>
                        {t("settings.index.indexingConfigsDescription")}
                      </FormHelperText>

                      <div className="flex flex-wrap gap-2">
                        {config?.disks.map((disk, index) => (
                          <div
                            key={index}
                            className="flex items-center bg-gray-100 dark:bg-gray-800 rounded-full px-3 py-1"
                          >
                            <span>{disk}</span>
                            <button
                              onClick={() => {
                                const newDisks = config.disks.filter(
                                  (_, i) => i !== index
                                );
                                setConfig({ ...config, disks: newDisks });
                              }}
                              className="ml-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                      <Stack direction="row" spacing={2} className="mt-2">
                        <FormControl>
                          <Button
                            variant="contained"
                            onClick={handleAddNewDisk}
                            className="text-white px-4 py-2 rounded-r"
                          >
                            {t("settings.index.addDisk")}
                          </Button>
                          <FormHelperText>
                            {t("settings.index.addDiskDescription")}
                          </FormHelperText>
                        </FormControl>

                        <FormControl>
                          <Button
                            variant="contained"
                            color="success"
                            className="text-white px-4 py-2 rounded-r"
                            onClick={() => {
                              updateIndex(dropPreviousIndex);
                            }}
                          >
                            {/* 更新文件索引 */}
                            {t("settings.index.updateIndex")}
                          </Button>
                          <FormHelperText>
                            {t("settings.index.updateIndexDescription")}
                          </FormHelperText>
                        </FormControl>

                        <FormControl>
                          <FormControlLabel
                            label={t("settings.index.isDropPrevious")}
                            control={
                              <Checkbox
                                checked={dropPreviousIndex}
                                onChange={(event) =>
                                  setDropPreviousIndex(event.target.checked)
                                }
                              />
                            }
                          />
                        </FormControl>
                      </Stack>
                      <Dialog
                        open={showAddDiskDialog}
                        onClose={() => {
                          document.getElementById("focus-element").focus();
                          setShowAddDiskDialog(false);
                        }}
                      >
                        <DialogTitle>
                          {t("settings.index.selectDisk")}
                        </DialogTitle>
                        <List>
                          {availableDisks.map((disk) => (
                            <ListItem key={disk}>
                              <ListItemButton
                                onClick={() => handleDiskSelect(disk)}
                              >
                                <ListItemText primary={disk} />
                              </ListItemButton>
                            </ListItem>
                          ))}
                        </List>
                      </Dialog>
                      <Stack direction="row" spacing={2} className="mt-4">
                        <TextField
                          margin="normal"
                          id="outlined-basic"
                          label={t("settings.index.maxCache")}
                          type="number"
                          value={config?.cacheNumLimit}
                        />
                        <TextField
                          margin="normal"
                          id="outlined-basic"
                          label={t("settings.index.checkFileChange")}
                          type="number"
                          value={config?.updateTimeLimit}
                        />
                      </Stack>
                    </div>
                    <div className="my-4">
                      <h3 className="font-medium mb-2 font-bold">
                        {t("settings.index.ignoreConfigs")}
                        <SettingLabel type="reindex" />
                      </h3>
                      <FormHelperText className="mb-2">
                        {t("settings.index.ignoreConfigsDescription")}
                      </FormHelperText>
                      <IgnorePathList
                        paths={config?.ignorePath || []}
                        onAdd={handleAddIgnorePath}
                        onRemove={handleRemoveIgnorePath}
                      />
                    </div>
                  </TabContent>
                </div>
                <div ref={(el) => (sectionsRef.current[3] = el)} id="search">
                  <TabContent
                    title={t("settings.search.title")}
                    config={config}
                  >
                    {/* GPU加速以及优先搜索文件夹设置 */}
                    <div className="my-4">
                      <h3 className="font-medium mb-2 font-bold">
                        {t("settings.search.searchConfigs")}
                      </h3>
                      <FormGroup>
                        <FormControl sx={{ mb: 2 }}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={
                                  config ? config.isEnableFuzzyMatch : false
                                }
                                onChange={(event) =>
                                  setConfig({
                                    ...config,
                                    isEnableFuzzyMatch: event.target.checked,
                                  })
                                }
                              />
                            }
                            label={t("settings.search.fuzzyMatch")}
                          />
                          <FormHelperText>
                            {t("settings.search.fuzzyMatchDescription")}
                          </FormHelperText>
                        </FormControl>

                        <FormControl fullWidth>
                          <InputLabel id="gpu-select-label">
                            <div className="flex items-center">
                              {t("settings.search.gpuDevice")}
                              <SettingLabel type="restart" />
                            </div>
                          </InputLabel>
                          <Select
                            disabled={!gpuDevice}
                            labelId="gpu-select-label"
                            label={
                              <div className="flex items-center">
                                {t("settings.search.gpuDevice")}
                                <SettingLabel type="restart" />
                              </div>
                            }
                            id="gpu-select"
                            value={selectedGpuDevice}
                            onChange={(event) =>
                              handleChangeGpuDevice(event.target.value)
                            }
                          >
                            {Object.keys(gpuDevice).map((device) => (
                              <MenuItem key={device} value={device}>
                                {device}
                              </MenuItem>
                            ))}
                            <MenuItem value={""}>None</MenuItem>
                          </Select>
                          <FormHelperText>
                            {t("settings.search.gpuDeviceDescription")}
                          </FormHelperText>
                        </FormControl>
                      </FormGroup>
                      <Grid2 container spacing={2} className="mt-2">
                        <Grid2 size={10}>
                          <FormControl fullWidth>
                            <TextField
                              className="w-full"
                              disabled
                              id="outlined-disabled"
                              label={
                                config?.priorityFolder
                                  ? t("settings.search.priorityFolderClear")
                                  : t("settings.search.priorityFolder")
                              }
                              value={config?.priorityFolder || ""}
                              onDoubleClick={() => {
                                if (config) {
                                  setConfig({ ...config, priorityFolder: "" });
                                }
                              }}
                            />
                            <FormHelperText>
                              {t("settings.search.priorityFolderDescription")}
                            </FormHelperText>
                          </FormControl>
                        </Grid2>
                        <Grid2 size={2}>
                          <Fab
                            color="primary"
                            aria-label="add"
                            onClick={handleAddPriorityFolder}
                          >
                            <AddIcon />
                          </Fab>
                        </Grid2>
                      </Grid2>
                    </div>
                  </TabContent>
                </div>
                <div ref={(el) => (sectionsRef.current[4] = el)} id="dataType">
                  <TabContent
                    title={t("settings.dataType.title")}
                    config={config}
                  >
                    {/* 后缀优先级设置 */}
                    <div className="my-4">
                      <h3 className="font-medium mb-2 font-bold">
                        {t("settings.dataType.suffixMap")}
                        <SettingLabel type="reindex" />
                      </h3>
                      {/* 配置类型和后缀优先级 */}
                      <Droppable droppableId="dataTypeList">
                        {(provided) => (
                          <TableContainer>
                            <Table aria-label="data type suffix map">
                              <TableHead>
                                <TableRow
                                  sx={{ "& > *": { borderBottom: "unset" } }}
                                >
                                  <TableCell></TableCell>
                                  <TableCell>
                                    <Grid2 container>
                                      <Grid2 size={8}>
                                        <strong>
                                          {t("settings.dataType.dataType")}
                                        </strong>
                                      </Grid2>
                                      <Grid2 size={2}>
                                        <Button
                                          variant="contained"
                                          onClick={() =>
                                            setOpenAddDataTypeDialog(true)
                                          }
                                        >
                                          {t("settings.dataType.add")}
                                        </Button>
                                      </Grid2>
                                    </Grid2>
                                  </TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody
                                {...provided.droppableProps}
                                ref={provided.innerRef}
                              >
                                {config?.dataTypeSuffixMap.map(
                                  (dataTypeRow, index) => (
                                    <Draggable
                                      key={dataTypeRow.dataType}
                                      draggableId={dataTypeRow.dataType}
                                      index={index}
                                    >
                                      {(provided, snapshot) => (
                                        <SuffixRow
                                          dataType={dataTypeRow.dataType}
                                          suffixList={dataTypeRow.suffix}
                                          deleteSuffix={handleDeleteSuffix}
                                          addSuffix={handleAddSuffix}
                                          moveSuffix={handleMoveSuffix}
                                          deleteDataType={handleDeleteDataType}
                                          provided={provided}
                                          snapshot={snapshot}
                                          appConfig={appConfig}
                                        />
                                      )}
                                    </Draggable>
                                  )
                                )}
                                {provided.placeholder}
                              </TableBody>
                            </Table>
                          </TableContainer>
                        )}
                      </Droppable>
                    </div>
                  </TabContent>
                </div>
                <div ref={(el) => (sectionsRef.current[5] = el)} id="cache">
                  <TabContent title={t("settings.cache.title")} config={config}>
                    {/* 缓存列表 */}
                    <div className="my-4">
                      <h3 className="font-medium mb-2 font-bold">
                        {t("settings.cache.cacheList")}
                      </h3>
                      <Grid2 container spacing={2} className="mt-2">
                        <Grid2 size={10}>
                          <TextField
                            className="w-full"
                            margin="normal"
                            id="outlined-basic"
                            label={t("settings.cache.searchCache")}
                            value={searchCacheKeyword || ""}
                            onChange={(event) =>
                              handleChangeSearchCacheKeyword(event.target.value)
                            }
                          />
                        </Grid2>
                        <Grid2 size={2} className="mt-2 place-content-center">
                          <Button
                            variant="outlined"
                            color="error"
                            className="text-white px-4 py-2 rounded-r"
                            onClick={handleDeleteCache}
                          >
                            {t("settings.buttons.delete")}
                          </Button>
                        </Grid2>
                      </Grid2>
                      <Box sx={{ width: "100%" }}>
                        <TableContainer>
                          <Table aria-label="cache table">
                            <TableHead>
                              <TableRow>
                                <TableCell padding="checkbox">
                                  <Checkbox
                                    color="primary"
                                    indeterminate={
                                      selectedCache.length > 0 &&
                                      resultCachePageResult.some(
                                        (path) => !selectedCache.includes(path)
                                      )
                                    }
                                    checked={
                                      resultCachePageResult.length > 0 &&
                                      selectedCache.length > 0 &&
                                      resultCachePageResult.every((path) =>
                                        selectedCache.includes(path)
                                      )
                                    }
                                    onChange={(event) => {
                                      if (event.target.checked) {
                                        setSelectedCache(
                                          Array.from(
                                            new Set([
                                              ...selectedCache,
                                              ...resultCachePageResult,
                                            ])
                                          )
                                        );
                                      } else {
                                        setSelectedCache(
                                          selectedCache.filter(
                                            (item) =>
                                              !resultCachePageResult.includes(
                                                item
                                              )
                                          )
                                        );
                                      }
                                    }}
                                    inputProps={{
                                      "aria-label": "select cache",
                                    }}
                                  />
                                </TableCell>
                                <TableCell>
                                  {t("settings.cache.path")}
                                </TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {resultCachePageResult.map((path, index) => (
                                <TableRow key={path}>
                                  <TableCell padding="checkbox">
                                    <Checkbox
                                      color="primary"
                                      checked={
                                        selectedCache.includes(path) || false
                                      }
                                      onChange={(event) => {
                                        const checked = event.target.checked;
                                        if (checked) {
                                          setSelectedCache(
                                            Array.from(
                                              new Set([...selectedCache, path])
                                            )
                                          );
                                        } else {
                                          setSelectedCache(
                                            selectedCache.filter(
                                              (item) => item !== path
                                            )
                                          );
                                        }
                                      }}
                                    />
                                  </TableCell>
                                  <TableCell>{path}</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                            <TableFooter>
                              <TableRow>
                                <TablePagination
                                  rowsPerPageOptions={[10, 15, 30]}
                                  count={resultCache.length}
                                  page={resultCachePage}
                                  rowsPerPage={resultCachePageSize}
                                  onPageChange={handleChangePage}
                                  onRowsPerPageChange={handleChangeRowsPerPage}
                                />
                              </TableRow>
                            </TableFooter>
                          </Table>
                        </TableContainer>
                      </Box>
                    </div>
                  </TabContent>
                </div>
                <div ref={(el) => (sectionsRef.current[6] = el)} id="hotkey">
                  <TabContent
                    title={t("settings.hotkey.title")}
                    config={config}
                  >
                    {/* 快捷键设置 */}
                    {appConfig && (
                      <FormGroup>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={appConfig?.enable_double_ctrl || false}
                              onChange={(e) => {
                                setAppConfig({
                                  ...appConfig,
                                  enable_double_ctrl: e.target.checked,
                                });
                              }}
                            />
                          }
                          label={t("settings.hotkey.enableDoubleCtrl")}
                        />
                      </FormGroup>
                    )}

                    <div className="my-2">
                      <FormControl fullWidth>
                        <TextField
                          label={
                            <div className="flex items-center">
                              {t("settings.hotkey.globalHotkey")}
                              <SettingLabel type="restart" />
                            </div>
                          }
                          value={globalHotkey}
                          onKeyDown={(event) =>
                            handleSetHotkey(event, setGlobalHotkey, true)
                          }
                        />
                        <FormHelperText>
                          {t("settings.hotkey.globalHotkeyDescription")}
                        </FormHelperText>
                      </FormControl>
                    </div>
                  </TabContent>
                </div>
                <div ref={(el) => (sectionsRef.current[7] = el)} id="advanced">
                  <TabContent
                    title={t("settings.advanced.title")}
                    config={config}
                  >
                    <div className="my-4">
                      <Stack spacing={2} className="mt-2">
                        {/* LLM Settings */}
                        <Typography variant="h6" gutterBottom>
                          {t("settings.advanced.llmSettings")}
                        </Typography>
                        <FormHelperText>
                          {t("settings.advanced.llmSettingsDescription")}
                        </FormHelperText>

                        <FormControl fullWidth>
                          <InputLabel id="llm-select-label">
                            {t("settings.advanced.llmProvider")}
                          </InputLabel>
                          <Select
                            labelId="llm-select-label"
                            value={config?.llm || "NONE"}
                            label={t("settings.advanced.llmProvider")}
                            onChange={(event) => {
                              setConfig({
                                ...config,
                                llm: event.target.value,
                              });
                            }}
                          >
                            {Object.keys(config?.llmConfigs || {}).map(
                              (provider) => (
                                <MenuItem key={provider} value={provider}>
                                  {provider}
                                </MenuItem>
                              )
                            )}
                          </Select>
                          <FormHelperText>
                            {t("settings.advanced.llmProviderDescription")}
                          </FormHelperText>
                        </FormControl>

                        {/* OLLAMA specific settings */}
                        <Collapse in={config?.llm === "OLLAMA"}>
                          <Stack spacing={2} sx={{ mt: 2 }}>
                            <TextField
                              fullWidth
                              label={t("settings.advanced.ollamaAddress")}
                              value={config?.llmConfigs?.OLLAMA?.address || ""}
                              onChange={(event) => {
                                setConfig({
                                  ...config,
                                  llmConfigs: {
                                    ...config.llmConfigs,
                                    OLLAMA: {
                                      ...config.llmConfigs.OLLAMA,
                                      address: event.target.value,
                                    },
                                  },
                                });
                              }}
                            />
                            <FormHelperText>
                              {t("settings.advanced.ollamaAddressDescription")}
                            </FormHelperText>
                            {/* Ollama api auth key */}
                            <TextField
                              fullWidth
                              label={t("settings.advanced.ollamaApiKey")}
                              value={config?.llmConfigs?.OLLAMA?.apiKey || ""}
                              onChange={(event) => {
                                setConfig({
                                  ...config,
                                  llmConfigs: {
                                    ...config.llmConfigs,
                                    OLLAMA: {
                                      ...config.llmConfigs.OLLAMA,
                                      apiKey: event.target.value,
                                    },
                                  },
                                });
                              }}
                            />
                            <FormHelperText>
                              {t("settings.advanced.ollamaApiKeyDescription")}
                            </FormHelperText>
                            {/* Ollama model options */}
                            <FormControl
                              fullWidth
                              margin="normal"
                              sx={{
                                "& .MuiOutlinedInput-root": {
                                  borderRadius: 2,
                                  transition: "all 0.2s",
                                  "&:hover": {
                                    borderColor: "primary.main",
                                  },
                                },
                              }}
                            >
                              <InputLabel>
                                {t("settings.advanced.ollamaModel")}
                              </InputLabel>
                              <Select
                                value={
                                  config?.llmConfigs?.OLLAMA?.modelType || ""
                                }
                                label={t("settings.advanced.ollamaModel")}
                                onChange={(event) => {
                                  setConfig({
                                    ...config,
                                    llmConfigs: {
                                      ...config.llmConfigs,
                                      OLLAMA: {
                                        ...config.llmConfigs.OLLAMA,
                                        modelType: event.target.value,
                                      },
                                    },
                                  });
                                }}
                                onOpen={() => {
                                  fetchModels();
                                }}
                                disabled={loading}
                              >
                                {loading ? (
                                  <MenuItem disabled>
                                    <CircularProgress
                                      size={20}
                                      sx={{ mr: 1 }}
                                    />
                                    {t("common.loading")}
                                  </MenuItem>
                                ) : (
                                  modelList.map((model) => (
                                    <MenuItem
                                      key={model.name}
                                      value={model.name}
                                    >
                                      <Stack
                                        direction="row"
                                        spacing={1}
                                        alignItems="center"
                                      >
                                        <Typography>{model.name}</Typography>
                                        <Typography
                                          variant="caption"
                                          color="text.secondary"
                                        >
                                          ({model.size} MB)
                                        </Typography>
                                        <Typography
                                          variant="caption"
                                          color="text.secondary"
                                        >
                                          {model.digest.substring(0, 8)}
                                        </Typography>
                                      </Stack>
                                    </MenuItem>
                                  ))
                                )}
                              </Select>
                              <FormHelperText>
                                {t("settings.advanced.ollamaModelDescription")}
                              </FormHelperText>
                            </FormControl>
                          </Stack>
                        </Collapse>

                        <FormControl>
                          <TextField
                            margin="normal"
                            id="waitForSearchTasksTimeoutInMills"
                            label={t(
                              "settings.advanced.waitForSearchTasksTimeoutInMills"
                            )}
                            type="number"
                            value={
                              config?.advancedConfigs
                                .waitForSearchTasksTimeoutInMills
                            }
                            onChange={(event) =>
                              setConfig({
                                ...config,
                                advancedConfigs: {
                                  ...config.advancedConfigs,
                                  waitForSearchTasksTimeoutInMills:
                                    parseInt(event.target.value) ?? 300000,
                                },
                              })
                            }
                          />
                          <FormHelperText>
                            {t(
                              "settings.advanced.waitForSearchTasksTimeoutInMillsDescription"
                            )}
                          </FormHelperText>
                        </FormControl>
                        <FormControl>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={
                                  config?.advancedConfigs.isDeleteUsnOnExit
                                }
                                onChange={(event) =>
                                  setConfig({
                                    ...config,
                                    advancedConfigs: {
                                      ...config.advancedConfigs,
                                      isDeleteUsnOnExit: event.target.checked,
                                    },
                                  })
                                }
                              />
                            }
                            label={
                              <div className="flex items-center">
                                {t("settings.advanced.isDeleteUsnOnExit")}
                                <SettingLabel type="restart" />
                              </div>
                            }
                          />
                          <FormHelperText>
                            {t(
                              "settings.advanced.isDeleteUsnOnExitDescription"
                            )}
                          </FormHelperText>
                        </FormControl>
                        <FormControl>
                          <TextField
                            margin="normal"
                            id="restartMonitorDiskThreadTimeoutInMills"
                            label={t(
                              "settings.advanced.restartMonitorDiskThreadTimeoutInMills"
                            )}
                            type="number"
                            value={
                              config?.advancedConfigs
                                .restartMonitorDiskThreadTimeoutInMills
                            }
                            onChange={(event) =>
                              setConfig({
                                ...config,
                                advancedConfigs: {
                                  ...config.advancedConfigs,
                                  restartMonitorDiskThreadTimeoutInMills:
                                    parseInt(event.target.value) ?? 600000,
                                },
                              })
                            }
                          />
                          <FormHelperText>
                            {t(
                              "settings.advanced.restartMonitorDiskThreadTimeoutInMillsDescription"
                            )}
                          </FormHelperText>
                        </FormControl>
                        <FormControl>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={
                                  config?.advancedConfigs.isReadPictureByLLM
                                }
                                onChange={(event) =>
                                  setConfig({
                                    ...config,
                                    advancedConfigs: {
                                      ...config.advancedConfigs,
                                      isReadPictureByLLM: event.target.checked,
                                    },
                                  })
                                }
                              />
                            }
                            label={
                              <div className="flex items-center">
                                {t("settings.advanced.isReadPictureByLLM")}
                                <SettingLabel type="restart" />
                              </div>
                            }
                          />
                          <FormHelperText>
                            {t(
                              "settings.advanced.isReadPictureByLLMDescription"
                            )}
                          </FormHelperText>
                        </FormControl>
                        <FormControl>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={
                                  config?.advancedConfigs.isEnableContentIndex
                                }
                                onChange={(event) =>
                                  setConfig({
                                    ...config,
                                    advancedConfigs: {
                                      ...config.advancedConfigs,
                                      isEnableContentIndex:
                                        event.target.checked,
                                    },
                                  })
                                }
                              />
                            }
                            label={
                              <div className="flex items-center">
                                {t("settings.advanced.isEnableContentIndex")}
                                <SettingLabel type="restart" />
                              </div>
                            }
                          />
                          <FormHelperText>
                            {t(
                              "settings.advanced.isEnableContentIndexDescription"
                            )}
                          </FormHelperText>
                        </FormControl>
                        {/* 索引缓存配置 */}
                        <div className="my-4">
                          <h3 className="font-medium mb-2 font-bold flex items-center">
                            {t("settings.advanced.cacheConfigs")}
                            <SettingLabel type="restart" />
                          </h3>
                          <FormHelperText>
                            {t("settings.advanced.cacheConfigsDescription")}
                          </FormHelperText>
                          <Stack spacing={2}>
                            <FormControl>
                              <TextField
                                margin="normal"
                                id="minCacheBlockNumber"
                                label={t(
                                  "settings.advanced.minCacheBlockNumber"
                                )}
                                type="number"
                                value={
                                  config?.advancedConfigs.minCacheBlockNumber
                                }
                                onChange={(event) =>
                                  setConfig({
                                    ...config,
                                    advancedConfigs: {
                                      ...config.advancedConfigs,
                                      minCacheBlockNumber:
                                        parseInt(event.target.value) ?? 100,
                                    },
                                  })
                                }
                              />
                              <FormHelperText>
                                {t(
                                  "settings.advanced.minCacheBlockNumberDescription"
                                )}
                              </FormHelperText>
                            </FormControl>
                            <FormControl>
                              <TextField
                                margin="normal"
                                id="maxCacheBlockNumber"
                                label={t(
                                  "settings.advanced.maxCacheBlockNumber"
                                )}
                                type="number"
                                value={
                                  config?.advancedConfigs.maxCacheBlockNumber
                                }
                                onChange={(event) =>
                                  setConfig({
                                    ...config,
                                    advancedConfigs: {
                                      ...config.advancedConfigs,
                                      maxCacheBlockNumber:
                                        parseInt(event.target.value) ?? 5000,
                                    },
                                  })
                                }
                              />
                              <FormHelperText>
                                {t(
                                  "settings.advanced.maxCacheBlockNumberDescription"
                                )}
                              </FormHelperText>
                            </FormControl>
                            <FormControl>
                              <TextField
                                margin="normal"
                                id="minGpuCacheBlockNumber"
                                label={t(
                                  "settings.advanced.minGpuCacheBlockNumber"
                                )}
                                type="number"
                                value={
                                  config?.advancedConfigs.minGpuCacheBlockNumber
                                }
                                onChange={(event) =>
                                  setConfig({
                                    ...config,
                                    advancedConfigs: {
                                      ...config.advancedConfigs,
                                      minGpuCacheBlockNumber:
                                        parseInt(event.target.value) ?? 3000,
                                    },
                                  })
                                }
                              />
                              <FormHelperText>
                                {t(
                                  "settings.advanced.minGpuCacheBlockNumberDescription"
                                )}
                              </FormHelperText>
                            </FormControl>
                            <FormControl>
                              <TextField
                                margin="normal"
                                id="maxGpuCacheBlockNumber"
                                label={t(
                                  "settings.advanced.maxGpuCacheBlockNumber"
                                )}
                                type="number"
                                value={
                                  config?.advancedConfigs.maxGpuCacheBlockNumber
                                }
                                onChange={(event) =>
                                  setConfig({
                                    ...config,
                                    advancedConfigs: {
                                      ...config.advancedConfigs,
                                      maxGpuCacheBlockNumber:
                                        parseInt(event.target.value) ?? 20000,
                                    },
                                  })
                                }
                              />
                              <FormHelperText>
                                {t(
                                  "settings.advanced.maxGpuCacheBlockNumberDescription"
                                )}
                              </FormHelperText>
                            </FormControl>
                          </Stack>
                        </div>
                        <Divider />
                        <Typography variant="h6" gutterBottom>
                          {t("settings.advanced.debugMode")}
                        </Typography>
                        <FormHelperText>
                          {t("settings.advanced.debugModeDescription")}
                        </FormHelperText>

                        <FormControl>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={appConfig?.debug_mode?.enable || false}
                                onChange={(event) =>
                                  setAppConfig({
                                    ...appConfig,
                                    debug_mode: {
                                      ...appConfig.debug_mode,
                                      enable: event.target.checked,
                                    },
                                  })
                                }
                              />
                            }
                            label={
                              <div className="flex items-center">
                                {t("settings.advanced.enableDebugMode")}
                                <SettingLabel type="restart" />
                              </div>
                            }
                          />
                          <FormHelperText>
                            {t("settings.advanced.enableDebugModeDescription")}
                          </FormHelperText>
                          {appConfig?.debug_mode?.enable && (
                            <Alert severity="info" sx={{ mt: 1 }}>
                              {t("settings.advanced.pluginServiceDebugPort", {
                                port: 35005,
                              })}
                            </Alert>
                          )}
                        </FormControl>

                        <Grid2 container spacing={2}>
                          <Grid2 size={10}>
                            <FormControl fullWidth>
                              <TextField
                                className="w-full"
                                disabled={!appConfig?.debug_mode?.enable}
                                label={t("settings.advanced.jdkHome")}
                                value={appConfig?.debug_mode?.jdk_home || ""}
                                onChange={(event) =>
                                  setAppConfig({
                                    ...appConfig,
                                    debug_mode: {
                                      ...appConfig.debug_mode,
                                      jdk_home: event.target.value,
                                    },
                                  })
                                }
                              />
                              <FormHelperText>
                                {t("settings.advanced.jdkHomeDescription")}
                              </FormHelperText>
                            </FormControl>
                          </Grid2>
                          <Grid2 size={2}>
                            <Fab
                              color="primary"
                              disabled={!appConfig?.debug_mode?.enable}
                              aria-label="select jdk home"
                              onClick={async () => {
                                const selected = await openDialog({
                                  directory: true,
                                  multiple: false,
                                });
                                if (selected) {
                                  setAppConfig({
                                    ...appConfig,
                                    debug_mode: {
                                      ...appConfig.debug_mode,
                                      jdk_home: selected as string,
                                    },
                                  });
                                }
                              }}
                            >
                              <AddIcon />
                            </Fab>
                          </Grid2>
                        </Grid2>

                        {/* Java Agent 配置 */}
                        <Grid2 container spacing={2}>
                          <Grid2 size={10}>
                            <FormControl fullWidth>
                              <TextField
                                className="w-full"
                                disabled={!appConfig?.debug_mode?.enable}
                                label={t("settings.advanced.javaAgent")}
                                value={appConfig?.debug_mode?.java_agent || ""}
                                onChange={(event) =>
                                  setAppConfig({
                                    ...appConfig,
                                    debug_mode: {
                                      ...appConfig.debug_mode,
                                      java_agent: event.target.value,
                                    },
                                  })
                                }
                              />
                              <FormHelperText>
                                {t("settings.advanced.javaAgentDescription")}
                              </FormHelperText>
                            </FormControl>
                          </Grid2>
                          <Grid2 size={2}>
                            <Fab
                              color="primary"
                              disabled={!appConfig?.debug_mode?.enable}
                              aria-label="select java agent"
                              onClick={async () => {
                                const selected = await openDialog({
                                  multiple: false,
                                  filters: [
                                    {
                                      name: "Java Agent",
                                      extensions: ["jar"],
                                    },
                                  ],
                                });
                                if (selected) {
                                  setAppConfig({
                                    ...appConfig,
                                    debug_mode: {
                                      ...appConfig.debug_mode,
                                      java_agent: selected as string,
                                    },
                                  });
                                }
                              }}
                            >
                              <AddIcon />
                            </Fab>
                          </Grid2>
                        </Grid2>
                      </Stack>
                    </div>
                  </TabContent>
                </div>
                <div ref={(el) => (sectionsRef.current[8] = el)} id="about">
                  <TabContent title={t("settings.about.title")} config={config}>
                    <AboutContent />
                  </TabContent>
                </div>
              </DragDropContext>
            </div>
            {/* 保存取消按钮 */}
            <div>
              <div className="w-full p-4 border-t flex justify-end">
                <Stack direction="row" spacing={2}>
                  <Dialog
                    open={showErrorConfigDialog}
                    onClose={() => {
                      document.getElementById("focus-element").focus();
                      setShowErrorConfigDialog(false);
                    }}
                    aria-labelledby="hotkey-alert-dialog-title"
                    aria-describedby="hotkey-alert-dialog-description"
                  >
                    <DialogTitle id="hotkey-alert-dialog-title">
                      <Typography
                        variant="h6"
                        gutterBottom
                        component="div"
                        className="place-content-center"
                      >
                        Config Error
                      </Typography>
                    </DialogTitle>
                    <DialogContent>
                      <DialogContentText id="hotkey-alert-dialog-description">
                        {errorConfigMessage.map((message) => {
                          return (
                            <span key={message} className="text-red-500">
                              {message}
                            </span>
                          );
                        })}
                      </DialogContentText>
                    </DialogContent>
                    <DialogActions>
                      <Button
                        onClick={() => {
                          document.getElementById("focus-element").focus();
                          setShowErrorConfigDialog(false);
                        }}
                      >
                        Ok
                      </Button>
                    </DialogActions>
                  </Dialog>
                  <Button
                    id="focus-element"
                    variant="contained"
                    onClick={() => {
                      saveAppConfig(true);
                    }}
                    color="primary"
                  >
                    {t("settings.buttons.ok")}
                  </Button>
                  <Button
                    variant="contained"
                    onClick={() => {
                      saveAppConfig(false);
                    }}
                    color="success"
                  >
                    {t("settings.buttons.apply")}
                  </Button>
                  <Snackbar
                    open={openSnackbar}
                    autoHideDuration={3000}
                    onClose={handleCloseSnackbar}
                    message={snackbarMessage}
                  />
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={() => {
                      getCurrentWindow().close();
                    }}
                  >
                    {t("settings.buttons.cancel")}
                  </Button>
                </Stack>
              </div>
            </div>
          </div>
        </div>
        <Dialog
          open={openAddDataTypeDialog}
          onClose={() => setOpenAddDataTypeDialog(false)}
        >
          <DialogTitle>{t("settings.dataType.addDataType")}</DialogTitle>
          <DialogContent>
            <DialogContentText>
              {t("settings.dataType.enterDataType")}
            </DialogContentText>
            <TextField
              autoFocus
              margin="dense"
              id="newDataType"
              label="Data Type"
              type="text"
              fullWidth
              value={newDataType}
              onChange={(e) => setNewDataType(e.target.value)}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenAddDataTypeDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddDataType}>Add</Button>
          </DialogActions>
        </Dialog>
      </div>
    </ThemeProvider>
  );
};

export default SettingsLayout;
