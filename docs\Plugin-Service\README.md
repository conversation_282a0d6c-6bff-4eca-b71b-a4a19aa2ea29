# Aiverything 插件开发文档

欢迎来到 Aiverything 插件开发文档中心！这里包含了所有你需要了解的插件开发信息。

## 📚 文档目录

### 🚀 [快速入门](./快速入门.md)
- 5分钟创建你的第一个插件
- 环境搭建与基本配置
- 简单示例演示

### 📖 [插件开发指南](./插件开发指南.md)
- 完整的插件开发教程
- 项目结构详解
- 配置文件说明
- 高级功能介绍
- 最佳实践指南

### 🔧 [API 参考](./API参考.md)
- 完整的 API 文档
- 方法签名与参数说明
- 代码示例
- 配置类型说明

## 🎯 推荐阅读路径

### 新手开发者
1. 阅读 [快速入门](./快速入门.md) - 快速上手
2. 浏览 [插件开发指南](./插件开发指南.md) - 了解全貌
3. 参考 [API 参考](./API参考.md) - 查找具体方法

### 有经验的开发者
1. 快速浏览 [插件开发指南](./插件开发指南.md) - 了解架构
2. 重点阅读 [API 参考](./API参考.md) - 掌握所有 API
3. 参考 [快速入门](./快速入门.md) - 了解项目结构

## 💡 主要特性

- **🔥 热插拔支持** - 插件可以在运行时动态加载和卸载
- **⚙️ 配置管理** - 强大的配置系统，支持复杂数据结构
- **🌍 国际化** - 多语言支持
- **🖥️ 前端集成** - 支持 HTML/CSS/JavaScript 前端页面
- **📡 命令系统** - 灵活的命令注册和处理机制
- **🔧 Java 21** - 使用最新的 Java 技术栈

## 🛠️ 核心概念

### 插件生命周期
```
加载 → 初始化 → 激活 → 运行 → 退出 → 卸载
```

### 配置管理
- 支持 `string`、`number`、`boolean`、`object`、`array` 类型
- 动态配置更新
- 嵌套配置结构
- 配置验证

### 命令系统
- 命令注册与处理
- 参数传递
- 返回值处理

## 📁 项目结构概览

```
plugin-template/
├── src/main/
│   ├── java/                          # Java 源代码
│   └── resources/
│       ├── plugin.json               # 插件元数据
│       ├── settings.json             # 插件配置
│       ├── i18n/                     # 国际化资源
│       └── static/                   # 前端资源
├── src/test/                          # 测试代码
└── docs/                             # 文档 (当前目录)
```

## 🔗 相关链接

- [项目模板](../src/main/java/com/platform/plugin/template/TestPlugin.java) - 完整示例代码
- [配置示例](../src/main/resources/settings.json) - 配置文件示例
- [前端示例](../src/main/resources/static/index.html) - 前端页面示例

## ❓ 需要帮助？

如果你在开发过程中遇到问题：

1. 首先查看 [插件开发指南](./插件开发指南.md) 中的"常见问题"部分
2. 检查 [API 参考](./API参考.md) 中的方法说明
3. 参考项目中的示例代码
4. 联系开发团队获取技术支持
