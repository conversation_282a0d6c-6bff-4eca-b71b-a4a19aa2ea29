import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import "./i18n/i18n";
import SettingWindow from "./SettingWindow";
const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);
const setTheme = (isDark) => {
  document.documentElement.classList.remove("dark");
  if (isDark) {
    document.documentElement.classList.add("dark");
  }
};

setTheme(window.matchMedia("(prefers-color-scheme: dark)").matches);

window
  .matchMedia("(prefers-color-scheme: dark)")
  .addEventListener("change", (e) => {
    const newIsDark = e.matches;
    setTheme(newIsDark);
  });
root.render(<SettingWindow />);
