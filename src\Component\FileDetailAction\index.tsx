import React, { useState } from "react";
import {
  FaFile,
  FaFolderOpen,
  FaTerminal,
  FaCopy,
  FaClipboard,
  FaCheck,
} from "react-icons/fa"; // 使用 react-icons 库
import {
  showItemInFolder,
  openTerminalInPath,
  openFileWithoutAdmin,
} from "../../api/system";
import { addCache } from "../../api/aiverything";
import { useTranslation } from "react-i18next";
import { WebviewWindow } from "@tauri-apps/api/webviewWindow";

const FileDetailAction = ({ file }) => {
  const { t } = useTranslation();
  const [completedActions, setCompletedActions] = useState({
    openFile: false,
    openFileLocation: false,
    openTerminal: false,
    copyFileName: false,
    copyFilePath: false,
  });

  const hideSearchBar = () => {
    WebviewWindow.getByLabel("main").then((window) => {
      window.hide();
    });
  };

  const handleOpenFile = () => {
    console.log(file.path);
    addCache(file.path);
    openFileWithoutAdmin(file.path).then(() => {
      hideSearchBar();
      setCompletedActions({ ...completedActions, openFile: true });
    });
    setTimeout(() => {
      setCompletedActions((prevState) => ({ ...prevState, openFile: false }));
    }, 1000);
  };

  const handleOpenFileLocation = () => {
    showItemInFolder(file.path).then(() => {
      hideSearchBar();
      setCompletedActions({ ...completedActions, openFileLocation: true });
    });
    setTimeout(() => {
      setCompletedActions((prevState) => ({
        ...prevState,
        openFileLocation: false,
      }));
    }, 1000);
  };

  const handleOpenTerminal = () => {
    //to do open terminal
    openTerminalInPath(file.path)
      .then(() => {
        hideSearchBar();
        setCompletedActions({ ...completedActions, openTerminal: true });
      })
      .catch((err) => {
        console.error(err);
      });
    setTimeout(() => {
      setCompletedActions((prevState) => ({
        ...prevState,
        openTerminal: false,
      }));
    }, 1000);
  };

  const handleCopyFileName = () => {
    //todo Copy file
    navigator.clipboard.writeText(file.name).then(() => {
      setCompletedActions({ ...completedActions, copyFileName: true });
      setTimeout(() => {
        setCompletedActions((prevState) => ({
          ...prevState,
          copyFileName: false,
        }));
      }, 1000);
    });
  };

  const handleCopyFilePath = () => {
    navigator.clipboard.writeText(file.path).then(() => {
      setCompletedActions({ ...completedActions, copyFilePath: true });
      setTimeout(() => {
        setCompletedActions((prevState) => ({
          ...prevState,
          copyFilePath: false,
        }));
      }, 1000);
    });
  };

  const actions = [
    {
      onClick: handleOpenFile,
      title: t("searchBar.openFile"),
      icon: (
        <FaFile className="w-4 h-4 text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200" />
      ),
      completed: completedActions.openFile,
    },
    {
      onClick: handleOpenFileLocation,
      title: t("searchBar.openParentDirectory"),
      icon: (
        <FaFolderOpen className="w-4 h-4 text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200" />
      ),
      completed: completedActions.openFileLocation,
    },
    {
      onClick: handleOpenTerminal,
      title: t("searchBar.openInTerminal"),
      icon: (
        <FaTerminal className="w-4 h-4 text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200" />
      ),
      completed: completedActions.openTerminal,
    },
    {
      onClick: handleCopyFileName,
      title: t("searchBar.copyName"),
      icon: (
        <FaCopy className="w-4 h-4 text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200" />
      ),
      completed: completedActions.copyFileName,
    },
    {
      onClick: handleCopyFilePath,
      title: t("searchBar.copyPath"),
      icon: (
        <FaClipboard className="w-4 h-4 text-gray-700 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200" />
      ),
      completed: completedActions.copyFilePath,
    },
  ];

  return (
    <div className="flex space-x-4 mb-4">
      {actions.map((action, index) => (
        <button
          key={index}
          onClick={action.onClick}
          title={action.title}
          className="relative p-1 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-700 active:bg-gray-300 dark:active:bg-gray-600 shadow dark:shadow-gray-800"
        >
          {action.completed ? (
            <FaCheck className="w-4 h-4 text-green-500 transition-opacity" />
          ) : (
            action.icon
          )}
        </button>
      ))}
    </div>
  );
};

export default FileDetailAction;
