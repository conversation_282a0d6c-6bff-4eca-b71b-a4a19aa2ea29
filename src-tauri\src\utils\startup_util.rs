use std::collections::HashMap;
use std::fs::File;
use std::io::Write;
use std::os::windows::process::CommandExt;
use std::process::Command;
use std::str;

use super::bat_vbs;

const EXE_NAME: &str = "aiverything.exe";
const TASK_NAME: &str = "Aiverything";

/**
 * 检查是否开机启动
 *
 * @return 全为零代表包含开机启动且启动项有效，如果为1则为包含启动项但启动项无效，如果为2则为不存在启动项或检查失败，
 */
pub fn has_startup() -> i32 {
    let output = Command::new("cmd.exe")
        .creation_flags(0x08000000)
        .args(&[
            "/C", "chcp", "65001", "&", "schtasks", "/query", "/V", "/tn", TASK_NAME,
        ])
        .output()
        .expect("Failed to execute command");

    let success = output.status.success();
    if !success {
        //读取stderr
        eprintln!("Error: {}", String::from_utf8_lossy(&output.stderr));
        return 2;
    }

    let stdout = str::from_utf8(&output.stdout).expect("Invalid UTF-8 sequence");
    let lines: Vec<&str> = stdout.lines().collect();
    if lines.len() < 4 {
        return 2;
    }

    // let keys = lines[3];
    // let separator = lines[4];
    let results = lines[5];

    if !results.contains(TASK_NAME) {
        return 2;
    }
    0

    // let info_map = parse_results(separator, keys, results);
    // let empty_str = String::new();
    // let task_to_run = info_map.get("Task To Run").unwrap_or(&empty_str);

    // let task_to_run = task_to_run.replace("wscript.exe", "");
    // let task_to_run = task_to_run.trim();
    // let task_to_run = task_to_run.replace("\"", "");

    // if Path::new(&task_to_run).exists() {
    //     return 0;
    // }

    // delete_startup().unwrap();
    // 1
}

pub fn generate_startup_vbs() -> std::io::Result<()> {
    let current_dir_path = std::env::current_dir()?;
    let parent_path = current_dir_path.to_str().unwrap();
    let aiverything_exe = parent_path.to_string() + "\\" + EXE_NAME;
    let vbs_startup_file = current_dir_path.join("startup.vbs");
    if vbs_startup_file.exists() {
        return Ok(());
    }

    bat_vbs::generate_open_file_vbs(&aiverything_exe, vbs_startup_file)
        .expect("Failed to generate startup file");
    Ok(())
}

pub fn add_startup() -> std::io::Result<()> {
    let current_dir_path = std::env::current_dir()?;
    let parent_path = current_dir_path.to_str().unwrap();
    let aiverything_exe_path = current_dir_path.join(EXE_NAME);
    // let aiverything_exe = parent_path.to_string() + "\\" + EXE_NAME;
    // let vbs_startup_file = current_dir_path.join("startup.vbs");

    // bat_vbs::generate_open_file_vbs(&aiverything_exe, vbs_startup_file.to_owned())
    //     .expect("Failed to generate startup file");

    let startup_file_canonicalized = aiverything_exe_path.canonicalize()?;
    let startup_file_abs_path = bat_vbs::adjust_canonicalization(startup_file_canonicalized);

    let builder = schtask_xml_content().to_string();

    let xml_content = builder.replace("${CommandToExecute}", startup_file_abs_path.as_str());

    let xml_content = xml_content
        .replace("${ArgumentsToReplace}", "")
        .replace("${WorkingDirectoryToReplace}", parent_path);

    let xml_content = xml_content.replace("\n", "\r\n");
    let tmp_dir = std::env::temp_dir();

    let xml_path = tmp_dir.join("aiverything_schtasks-tmp.xml");

    {
        let mut file = File::create(&xml_path)?;
        // write BOM
        file.write_all(&[0xFF, 0xFE])?;
        let xml_content_utf16le = xml_content.encode_utf16().collect::<Vec<_>>();
        let mut byte_data = Vec::new();
        for code_unit in xml_content_utf16le {
            byte_data.extend(&code_unit.to_le_bytes());
        }
        file.write_all(&byte_data)?;
    }

    let xml_path_canonical = xml_path.canonicalize()?;
    let xml_path_str = xml_path_canonical.to_str().unwrap();

    Command::new("cmd.exe")
        .creation_flags(0x08000000)
        .args(&[
            "/C",
            "schtasks",
            "/create",
            "/xml",
            xml_path_str,
            "/tn",
            TASK_NAME,
        ])
        .spawn()?
        .wait()?;

    Ok(())
}

pub fn delete_startup() -> std::io::Result<()> {
    Command::new("cmd.exe")
        .creation_flags(0x08000000)
        .args(&["/c", "schtasks", "/delete", "/tn", TASK_NAME, "/f"])
        .spawn()?
        .wait()?;
    Ok(())
}

fn parse_results(separator: &str, keys: &str, results: &str) -> HashMap<String, String> {
    let separator_array: Vec<&str> = separator.split_whitespace().collect();
    let size = separator_array.len();
    let mut key_index = 0;
    let mut val_index = 0;
    let mut keys_array = vec![""; size];
    let mut results_array = vec![""; size];

    for i in 0..size {
        let str_length = separator_array[i].len();
        keys_array[i] = &keys[key_index..key_index + str_length];
        key_index += str_length + 1;
        results_array[i] = &results[val_index..val_index + str_length];
        val_index += str_length + 1;
    }

    let key_list: Vec<&str> = keys_array
        .iter()
        .filter(|&&x| !x.trim().is_empty())
        .map(|&x| x.trim())
        .collect();
    let result_list: Vec<&str> = results_array
        .iter()
        .filter(|&&x| !x.trim().is_empty())
        .map(|&x| x.trim())
        .collect();

    if key_list.len() == result_list.len() {
        return key_list
            .into_iter()
            .zip(result_list.into_iter())
            .map(|(k, v)| (k.to_string(), v.to_string()))
            .collect();
    }

    panic!("parse schtasks information failed.");
}

fn schtask_xml_content() -> &'static str {
    r#"<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2" xmlns="http://schemas.microsoft.com/windows/2004/02/mit/task">
  <Triggers>
    <LogonTrigger>
      <Enabled>true</Enabled>
      <Delay>PT10S</Delay>
    </LogonTrigger>
  </Triggers>
  <Principals>
    <Principal>
      <RunLevel>HighestAvailable</RunLevel>
    </Principal>
  </Principals>
  <Settings>
    <MultipleInstancesPolicy>IgnoreNew</MultipleInstancesPolicy>
    <DisallowStartIfOnBatteries>false</DisallowStartIfOnBatteries>
    <StopIfGoingOnBatteries>true</StopIfGoingOnBatteries>
    <AllowHardTerminate>true</AllowHardTerminate>
    <StartWhenAvailable>false</StartWhenAvailable>
    <RunOnlyIfNetworkAvailable>false</RunOnlyIfNetworkAvailable>
    <IdleSettings>
      <StopOnIdleEnd>true</StopOnIdleEnd>
      <RestartOnIdle>false</RestartOnIdle>
    </IdleSettings>
    <AllowStartOnDemand>true</AllowStartOnDemand>
    <Enabled>true</Enabled>
    <Hidden>false</Hidden>
    <RunOnlyIfIdle>false</RunOnlyIfIdle>
    <WakeToRun>false</WakeToRun>
    <ExecutionTimeLimit>PT72H</ExecutionTimeLimit>
    <Priority>7</Priority>
  </Settings>
  <Actions>
    <Exec>
      <Command>${CommandToExecute}</Command>
      <Arguments>${ArgumentsToReplace}</Arguments>
      <WorkingDirectory>${WorkingDirectoryToReplace}</WorkingDirectory>
    </Exec>
  </Actions>
</Task>"#
}
