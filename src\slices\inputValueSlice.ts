// inputValueSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';

interface InputValueState {
  value: string;
}

const initialState: InputValueState = {
  value: '',
};

export const inputValueSlice = createSlice({
  name: 'inputValue',
  initialState,
  reducers: {
    setInputValue: (state, action: PayloadAction<string>) => {
      state.value = action.payload;
    },
  },
});

export const { setInputValue } = inputValueSlice.actions;

export const selectInputValue = (state: RootState) => state.inputValue.value;

export default inputValueSlice.reducer;