# File-Engine-Core 文件监控系统开发文档

## 1. 概述

File-Engine-Core的文件监控系统基于Windows NTFS文件系统的USN（Update Sequence Number）日志功能，实现了高效的实时文件变化监控。该系统通过C++ JNI实现底层监控逻辑，与Java层无缝集成，为文件索引的增量更新提供支持。

### 1.1 核心特性

- **实时监控**: 基于USN日志的零延迟文件变化检测
- **高性能**: 系统级API调用，性能优于传统轮询方式
- **多磁盘支持**: 同时监控多个NTFS磁盘分区
- **路径缓存**: 智能缓存机制减少磁盘IO操作
- **多线程安全**: 并发安全的队列和缓存设计
- **内存优化**: LRU缓存算法控制内存占用

## 2. USN技术原理

### 2.1 USN日志概述

USN（Update Sequence Number）是NTFS文件系统的一个核心功能，用于跟踪文件系统中的所有变化。每当文件或目录发生变化时，NTFS会在USN日志中记录一条记录，包含变化类型、文件引用号、时间戳等信息。

### 2.2 USN记录结构

```cpp
typedef struct {
    DWORD RecordLength;           // 记录长度
    WORD MajorVersion;            // 主版本号
    WORD MinorVersion;            // 次版本号
    DWORDLONG FileReferenceNumber;  // 文件引用号
    DWORDLONG ParentFileReferenceNumber; // 父目录引用号
    USN Usn;                      // 序列号
    LARGE_INTEGER TimeStamp;      // 时间戳
    DWORD Reason;                 // 变化原因
    DWORD SourceInfo;             // 源信息
    DWORD SecurityId;             // 安全ID
    DWORD FileAttributes;         // 文件属性
    WORD FileNameLength;          // 文件名长度
    WORD FileNameOffset;          // 文件名偏移
    WCHAR FileName[1];            // 文件名
} USN_RECORD;
```

### 2.3 变化原因标志

系统监控以下文件变化类型：

- `USN_REASON_FILE_CREATE`: 文件创建
- `USN_REASON_FILE_DELETE`: 文件删除
- `USN_REASON_RENAME_NEW_NAME`: 重命名（新名称）
- `USN_REASON_RENAME_OLD_NAME`: 重命名（旧名称）
- `USN_REASON_CLOSE`: 文件关闭

## 3. 架构设计

### 3.1 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Java Layer                               │
├─────────────────────────────────────────────────────────────┤
│  FileMonitor.java (JNI Interface)                          │
│  - monitor(String path)                                     │
│  - stop_monitor(String path)                               │
│  - pop_add_file() / pop_del_file()                         │
├─────────────────────────────────────────────────────────────┤
│                  JNI Bridge                                 │
├─────────────────────────────────────────────────────────────┤
│                   C++ Layer                                 │
├─────────────────────────────────────────────────────────────┤
│  dllmain.cpp (JNI Implementation)                          │
│  - Global queues: file_added_queue, file_del_queue         │
│  - Watcher management: ntfs_changes_watcher_map            │
├─────────────────────────────────────────────────────────────┤
│  NTFSChangesWatcher.cpp (Core Logic)                       │
│  - USN Journal management                                   │
│  - Path reconstruction with caching                        │
│  - Multi-threaded file monitoring                          │
├─────────────────────────────────────────────────────────────┤
│                Windows NTFS USN Journal                    │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 核心组件

#### 3.2.1 NTFSChangesWatcher类

主要负责USN日志的读取和处理：

```cpp
class NTFSChangesWatcher {
private:
    char drive_letter_;                    // 监控的磁盘盘符
    HANDLE volume_;                        // 磁盘卷句柄
    volatile bool stop_flag;               // 停止标志
    cache_map_t frn_record_pfrn_map_;     // 路径缓存映射
    std::unique_ptr<USN_JOURNAL_DATA> journal_; // USN日志数据
    DWORDLONG journal_id_;                 // 日志ID
    USN last_usn_;                         // 最后处理的USN
    USN max_usn_;                          // 最大USN
    static const size_t kBufferSize = 1024 * 1024 / 2; // 缓冲区大小
    size_t remain_cache_number = MAX_USN_CACHE_SIZE;   // 剩余缓存数量
};
```

#### 3.2.2 全局队列管理

```cpp
using file_record_queue = concurrency::concurrent_queue<std::wstring>;

file_record_queue file_added_queue;    // 新增文件队列
file_record_queue file_del_queue;      // 删除文件队列
concurrency::concurrent_unordered_map<std::string, NTFSChangesWatcher*> 
    ntfs_changes_watcher_map;           // 监控器映射
std::shared_mutex watcher_map_lock;     // 读写锁
```

## 4. 核心流程详解

### 4.1 监控启动流程

```cpp
void monitor_path(const std::string& path) {
    NTFSChangesWatcher watcher(path[0]);  // 创建监控器
    {
        std::unique_lock ul(watcher_map_lock);
        ntfs_changes_watcher_map.insert(std::make_pair(path, &watcher));
    }
    watcher.WatchChanges(push_add_file, push_del_file); // 开始监控
    {
        std::unique_lock ul(watcher_map_lock);
        ntfs_changes_watcher_map.unsafe_erase(path);
    }
}
```

**关键步骤：**
1. 创建`NTFSChangesWatcher`实例
2. 注册到全局监控器映射
3. 调用`WatchChanges`进入监控循环
4. 监控结束后清理资源

### 4.2 USN日志初始化

```cpp
NTFSChangesWatcher::NTFSChangesWatcher(char drive_letter) {
    volume_ = OpenVolume(drive_letter_);              // 打开磁盘卷
    journal_ = std::make_unique<USN_JOURNAL_DATA>();
    
    if (!LoadJournal(volume_, journal_.get())) {      // 加载USN日志
        // 如果日志不存在，尝试创建
        if (CreateJournal(volume_)) {
            LoadJournal(volume_, journal_.get());
        }
    }
    
    max_usn_ = journal_->MaxUsn;
    journal_id_ = journal_->UsnJournalID;
    last_usn_ = journal_->NextUsn;  // 从当前位置开始监控
}
```

### 4.3 文件变化监控循环

```cpp
void NTFSChangesWatcher::WatchChanges(
    void (*file_added_callback_func)(const std::u16string&),
    void (*file_removed_callback_func)(const std::u16string&)) {
    
    const auto u_buffer = std::make_unique<char[]>(kBufferSize);
    const auto read_journal_query = GetWaitForNextUsnQuery(last_usn_);

    while (!stop_flag) {
        // 等待新的USN记录
        WaitForNextUsn(read_journal_query.get());
        
        // 读取并处理变化
        last_usn_ = ReadChangesAndNotify(read_journal_query->StartUsn,
                                       u_buffer.get(),
                                       file_added_callback_func,
                                       file_removed_callback_func);
        read_journal_query->StartUsn = last_usn_;
    }
}
```

### 4.4 USN记录处理

```cpp
USN NTFSChangesWatcher::ReadChangesAndNotify(USN low_usn, char* buffer,
    void (*file_added_callback_func)(const std::u16string&),
    void (*file_removed_callback_func)(const std::u16string&)) {
    
    // 读取USN记录
    if (!ReadJournalRecords(journal_query.get(), buffer, byte_count)) {
        return low_usn;
    }

    auto record = reinterpret_cast<USN_RECORD*>(buffer + sizeof(USN));
    const auto record_end = reinterpret_cast<USN_RECORD*>(buffer + byte_count);

    for (; record < record_end; record = NextRecord(record)) {
        const auto reason = record->Reason;
        std::u16string full_path;
        
        // 过滤同时创建和删除的记录
        if ((reason & USN_REASON_FILE_CREATE) && (reason & USN_REASON_FILE_DELETE)) {
            continue;
        }
        
        // 处理不同类型的文件变化
        if ((reason & USN_REASON_FILE_DELETE) && (reason & USN_REASON_CLOSE)) {
            show_record(full_path, record);
            if (full_path.find(recycle_bin_u16) == std::u16string::npos) {
                file_removed_callback_func(full_path);
            }
        }
        // ... 其他变化类型处理
    }
    
    return *reinterpret_cast<USN*>(buffer);
}
```

## 5. 路径重构与缓存机制

### 5.1 路径重构原理

USN记录只包含文件名和父目录引用号，需要通过递归查询构建完整路径：

```cpp
void NTFSChangesWatcher::show_record(std::u16string& full_path, USN_RECORD* record) {
    cache_map_t temp_usn_cache;
    full_path += GetFilename(record);  // 获取文件名
    
    // 检查父目录是否在缓存中
    if (auto val = frn_record_pfrn_map_.find(record->ParentFileReferenceNumber);
        val != frn_record_pfrn_map_.end()) {
        // 缓存命中，直接构建路径
        full_path = val->second.first.first + sep + full_path;
        val->second.first.second = GetTickCount64(); // 更新使用时间
    } else {
        // 缓存未命中，递归查询父目录
        DWORDLONG file_parent_id = record->ParentFileReferenceNumber;
        do {
            // 通过MFT枚举API查询父目录信息
            MFT_ENUM_DATA_V0 med;
            med.StartFileReferenceNumber = file_parent_id;
            // ... 查询逻辑
            
            const auto parent_record = GetParentRecord(file_parent_id);
            const auto file_name = GetFilename(parent_record);
            full_path = file_name + sep + full_path;
            file_parent_id = parent_record->ParentFileReferenceNumber;
            
            // 将中间结果保存到临时缓存
            temp_usn_cache.insert(MakePair(parent_record));
        } while (file_parent_id != 0);
    }
    
    // 添加驱动器前缀
    full_path = drive_prefix + full_path;
    
    // 将临时缓存数据转移到主缓存
    if (remain_cache_number > temp_usn_cache.size()) {
        save_usn_cache_to_map(temp_usn_cache);
    }
}
```

### 5.2 LRU缓存算法

系统实现了基于时间戳的LRU缓存算法：

```cpp
using cache_map_t = concurrency::concurrent_unordered_map<
    DWORDLONG,  // 文件引用号作为key
    std::pair<
        std::pair<std::u16string, DWORDLONG>,  // 路径和使用时间戳
        DWORDLONG                               // 父引用号
    >
>;

// 缓存清理逻辑
if (remain_cache_number <= 0) {
    std::vector<CacheEntry*> tmp_vec;
    for (auto&& each : frn_record_pfrn_map_) {
        tmp_vec.emplace_back(&each);
    }
    
    // 按使用时间排序
    std::ranges::sort(tmp_vec.begin(), tmp_vec.end(),
        [](const CacheEntry* left, const CacheEntry* right) {
            return left->second.first.second < right->second.first.second;
        });
    
    // 删除最不常用的缓存项
    for (unsigned i = 0; i < MAX_USN_CACHE_SIZE / 1000; ++i) {
        frn_record_pfrn_map_.unsafe_erase(tmp_vec[i]->first);
        ++remain_cache_number;
    }
}
```

## 6. 多线程安全设计

### 6.1 并发队列

使用Intel TBB的并发队列确保线程安全：

```cpp
using file_record_queue = concurrency::concurrent_queue<std::wstring>;

bool pop_add_file(std::wstring& record) {
    return file_added_queue.try_pop(record);
}

void push_add_file(const std::u16string& record) {
    const std::wstring wstr(reinterpret_cast<LPCWSTR>(record.c_str()));
    file_added_queue.push(wstr);
}
```

### 6.2 读写锁保护

```cpp
std::shared_mutex watcher_map_lock;

void monitor(const char* path) {
    std::shared_lock sl(watcher_map_lock);  // 读锁
    auto watcher_iter = ntfs_changes_watcher_map.find(path_str);
    // ... 读取操作
}

void monitor_path(const std::string& path) {
    std::unique_lock ul(watcher_map_lock);  // 写锁
    ntfs_changes_watcher_map.insert(std::make_pair(path, &watcher));
    // ... 写入操作
}
```

## 7. 性能优化策略

### 7.1 缓冲区管理

- **大缓冲区**: 使用512KB缓冲区批量读取USN记录
- **内存对齐**: 使用FILE_FLAG_NO_BUFFERING标志确保扇区对齐
- **预分配**: 使用智能指针预分配缓冲区，避免频繁内存分配

### 7.2 路径缓存优化

- **容量控制**: 最大缓存100万条路径记录
- **命中率优化**: 文件夹路径优先缓存，提高命中率
- **时间窗口**: 设置10秒超时避免路径重构死循环

### 7.3 过滤机制

```cpp
// 过滤回收站文件
static const std::u16string recycle_bin_u16(L"$RECYCLE.BIN");
if (full_path.find(recycle_bin_u16) == std::u16string::npos) {
    file_removed_callback_func(full_path);
}

// 过滤临时文件
String tempPath = System.getProperty("java.io.tmpdir");
if (!addFilePath.contains(tempPath)) {
    // 处理文件变化
}
```

## 8. Java集成接口

### 8.1 JNI接口定义

```java
public enum FileMonitor {
    INSTANCE;

    public native void monitor(String path);
    public native void stop_monitor(String path);
    public native boolean is_monitor_stopped(String path);
    public native String pop_add_file();
    public native String pop_del_file();
    public native void delete_usn_on_exit(String path);
}
```

### 8.2 使用示例

```java
// 启动监控
ThreadPoolUtil.getInstance().executeTask(() -> 
    FileMonitor.INSTANCE.monitor("C:\\"), false);

// 处理文件变化
while (eventManagement.notMainExit()) {
    String addFilePath = FileMonitor.INSTANCE.pop_add_file();
    String deleteFilePath = FileMonitor.INSTANCE.pop_del_file();
    
    if (addFilePath != null) {
        addFileToDatabase(addFilePath);
    }
    if (deleteFilePath != null) {
        removeFileFromDatabase(deleteFilePath);
    }
    
    TimeUnit.MILLISECONDS.sleep(1);
}
```

## 9. 错误处理与监控

### 9.1 异常处理

```cpp
// USN日志创建失败处理
if (!LoadJournal(volume_, journal_.get())) {
    if (CreateJournal(volume_)) {
        return LoadJournal(volume_, journal_.get());
    }
    fprintf(stderr, "Failed to load journal, Error code: %lu\n", GetLastError());
    return false;
}

// 路径重构超时保护
if (const auto loop_time = std::chrono::duration_cast<std::chrono::milliseconds>
    (std::chrono::system_clock::now().time_since_epoch()) - start_loop; 
    loop_time.count() > 10000) {
    return;  // 10秒超时退出
}
```

### 9.2 优雅停止机制

```cpp
void NTFSChangesWatcher::StopWatch() {
    stop_flag = true;
    
    // 创建退出标志文件
    auto exit_file = drive_letter_ + std::string(":\\") + file_monitor_exit_flag;
    if (!is_file_exist(exit_file)) {
        FILE* fp = nullptr;
        fopen_s(&fp, exit_file.c_str(), "w");
        if (fp != nullptr) {
            fclose(fp);
        }
    }
    remove(exit_file.c_str());  // 立即删除标志文件
}
```

## 10. 部署与调试

### 10.1 系统要求

- **操作系统**: Windows 10/11，Windows Server 2016+
- **文件系统**: NTFS（必须）
- **权限**: 管理员权限（推荐）
- **依赖**: Visual C++ 2022 Runtime

### 10.2 编译配置

```xml
<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
  <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
  <LanguageStandard>stdcpp20</LanguageStandard>
  <DisableSpecificWarnings>4996</DisableSpecificWarnings>
</PropertyGroup>
```

### 10.3 调试技巧

```cpp
// 启用调试输出
#define TEST
#ifdef TEST
#include <iostream>
std::cout << "monitoring " << path << std::endl;
#endif

// 性能监控
log.info("从缓存中读取 {}", key);
log.warn("等待USN记录超时");
```

## 参考资料

- [NTFS Change Journal](https://docs.microsoft.com/en-us/windows/win32/fileio/change-journal-records)
- [DeviceIoControl Function](https://docs.microsoft.com/en-us/windows/win32/api/ioapiset/nf-ioapiset-deviceiocontrol)
- [Intel TBB Concurrent Queue](https://spec.oneapi.io/versions/latest/elements/oneTBB/source/containers/concurrent_queue_cls.html)
