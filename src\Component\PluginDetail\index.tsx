import { useTranslation } from "react-i18next";
import PluginDetailTable from "../PluginDetailTable";
import { Button } from "@mui/material";
import StorefrontIcon from "@mui/icons-material/Storefront";
import { PLUGIN_STORE_URL } from "../../utils/constants";
import { open } from "@tauri-apps/plugin-shell";
import { WebviewWindow } from "@tauri-apps/api/webviewWindow";

const PluginDetail = ({ plugin, searchKeyword = "" }) => {
  const { t } = useTranslation();

  if (!plugin) {
    if (!searchKeyword) {
      return (
        <div className="w-full h-answer p-4 flex flex-col justify-center items-center">
          <StorefrontIcon sx={{ fontSize: 60, color: "text.secondary" }} />
          <div className="text-gray-400 mb-4">
            <Button
              variant="contained"
              startIcon={<StorefrontIcon />}
              onClick={() => {
                // 跳转到插件商店的逻辑
                open(PLUGIN_STORE_URL);
                WebviewWindow.getByLabel("main").then((window) => {
                  window.hide();
                });
              }}
            >
              {t("pluginSettings.store.title")}
            </Button>
          </div>
          {t("searchBar.noPluginInstalled")}
        </div>
      );
    }
    return (
      <div className="w-full h-answer p-4 flex flex-col justify-center items-center">
        <div className="text-gray-400 mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="64"
            height="64"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            <line x1="8" y1="11" x2="14" y2="11"></line>
          </svg>
        </div>
        {t("searchBar.noPluginFound", { keyword: searchKeyword })}
      </div>
    );
  }

  const iconSrc = plugin.icon;
  const sizeClass = "w-64 h-32 mb-4 mt-8";

  const iconElement = (
    <div className={`${sizeClass}`}>
      {iconSrc ? (
        <img src={iconSrc} alt="" className="w-full h-full object-contain" />
      ) : (
        <div className="bg-gray-200 dark:bg-gray-700"></div>
      )}
    </div>
  );

  return (
    <div className="w-full h-answer p-2 flex flex-col items-center overflow-hidden">
      {iconElement}
      <div className="relative">
        <div className="text-lg font-bold max-w-60 truncate mb-4 dark:text-gray-200">
          <span title={plugin.name}>{plugin.name}</span>
        </div>
      </div>
      <PluginDetailTable plugin={plugin} />
    </div>
  );
};

export default PluginDetail;
