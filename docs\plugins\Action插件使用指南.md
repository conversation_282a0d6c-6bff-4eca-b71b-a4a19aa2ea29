# Action插件详细使用指南

## 概述

Action插件是一个功能强大的任务自动化工具，它允许您创建和管理一系列可执行任务。通过直观的界面，您可以配置多个动作(Action)，每个动作包含多个有序执行的任务(Task)，实现复杂的自动化流程。

## 核心概念

### 1. 动作(Action)
- **定义**：动作是一个完整的自动化流程，包含一个或多个任务
- **组成**：名称、UUID、任务列表、启动选项
- **特点**：可以独立执行、编辑、删除

### 2. 任务(Task)
- **定义**：动作中的单个执行单元
- **组成**：执行路径、参数列表、工作目录、执行选项
- **执行顺序**：按照添加顺序依次执行

## 主要功能特性

### 🚀 任务自动化
- **顺序执行**：任务按添加顺序依次执行
- **条件中止**：支持任务失败时中止整个动作
- **等待机制**：可设置等待任务完成后再执行下一个

### 🎯 多文件类型支持
- **URL快捷方式**：支持.url文件，直接打开网页链接
- **Windows快捷方式**：支持.lnk文件，通过系统资源管理器打开
- **可执行文件**：支持任何可执行程序，包括.exe、.bat、.cmd等

### ⚙️ 灵活配置
- **工作目录**：可自定义任务执行的工作目录
- **命令行参数**：支持添加多个命令行参数
- **启动选项**：支持插件启动时自动执行特定动作

### 🌍 用户体验
- **多语言**：支持中文和英文界面
- **暗色模式**：支持明暗主题切换
- **响应式设计**：适配不同屏幕尺寸

## 详细使用步骤

### 1. 创建新动作

1. **打开插件界面**
   - 在插件列表中找到"Actions - Quick launcher"
   - 点击进入插件界面
![Actions plugin](./pictures/Actions%20plugin.png)

2. **添加动作**
   - 点击右上角的"添加Action"按钮
   - 在弹出的对话框中输入动作名称
   - 可选择是否勾选"启动时自动执行"
![Actions plugin settings](./pictures/Actions%20plugin%20settings.png)
![Add actions](./pictures/Add%20actions.png)

3. **配置动作属性**
   - **动作名称**：为动作设置一个易于识别的名称
   - **启动时自动执行**：勾选后，插件加载时会自动执行此动作

### 2. 配置任务

#### 添加任务
1. 在动作编辑界面点击"添加任务"按钮
2. 配置任务参数：

#### 基本配置
- **执行路径**（必填）：
  - 可执行文件的完整路径
  - 支持格式：.exe、.bat、.cmd、.url、.lnk等
  - 示例：`C:\Program Files\Notepad++\notepad++.exe`

- **工作目录**（可选）：
  - 任务执行时的工作目录
  - 如果不设置，默认使用执行文件的上级目录
  - 示例：`D:\MyProjects\`

#### 高级配置
- **参数列表**：
  - 点击"添加参数"按钮添加命令行参数
  - 可添加多个参数，每个参数独立设置
  - 示例：`-multiInst -notabbar -nosession`

- **执行选项**：
  - **等待完成**：勾选后会等待当前任务完成再执行下一个
  - **失败时中止Action**：勾选后如果任务失败，整个动作会立即停止

### 3. 管理动作

#### 执行动作
- 点击动作右侧的"执行"按钮
- 系统会按顺序执行动作中的所有任务

#### 编辑动作
- 点击"编辑"按钮打开编辑界面
- 可以修改动作名称、启动选项
- 可以添加、删除、修改任务

#### 删除动作
- 点击"删除"按钮
- 确认删除后，动作及其所有任务将被永久删除

### 4. 界面功能说明

#### 主界面元素
- **动作列表**：显示所有已创建的动作
- **动作信息**：显示动作名称、UUID、自动启动状态
- **任务预览**：显示前几个任务的基本信息
- **操作按钮**：执行、编辑、删除功能

#### 任务显示逻辑
- 默认显示前4个任务
- 超过4个任务时，显示"显示更多"按钮
- 点击可展开或收起完整任务列表

## 实际应用场景

### 1. 开发环境启动
```
动作名称：启动开发环境
任务1：启动数据库服务
任务2：启动后端服务
任务3：启动前端开发服务器
任务4：打开IDE
```

### 2. 日常工作流程
```
动作名称：工作日启动
任务1：打开邮件客户端
任务2：启动团队协作工具
任务3：打开项目管理系统
任务4：运行数据同步脚本
```

### 3. 系统维护
```
动作名称：系统清理
任务1：清理临时文件
任务2：整理磁盘碎片
任务3：更新系统补丁
任务4：重启系统服务
```

## 高级技巧

### 1. 参数传递
- 使用环境变量在任务间传递信息
- 通过工作目录设置统一的执行环境
- 利用批处理文件整合复杂逻辑

### 2. 错误处理
- 合理设置"失败时中止Action"选项
- 对关键任务启用"等待完成"
- 在批处理中添加错误检查逻辑

### 3. 性能优化
- 对于独立的任务，不启用"等待完成"
- 合理安排任务顺序，避免资源冲突

## 总结

Action插件为用户提供了一个强大而灵活的任务自动化解决方案。通过合理配置动作和任务，您可以：

- 🎯 **提高效率**：自动化重复性操作
- 🛡️ **减少错误**：标准化执行流程
- 🔄 **简化管理**：统一管理复杂的操作序列
- 🌟 **改善体验**：直观的界面和丰富的功能

无论是开发环境的快速启动，还是日常工作流程的自动化，Action插件都能为您提供强大的支持。
