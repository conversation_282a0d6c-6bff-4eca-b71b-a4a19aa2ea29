{"desktop-capability": {"identifier": "desktop-capability", "description": "", "local": true, "permissions": ["global-shortcut:default", "global-shortcut:default"], "platforms": ["macOS", "windows", "linux"]}, "main-capability": {"identifier": "main-capability", "description": "Capability for the main window", "local": true, "windows": ["main", "settings", "attach", "pluginSettings"], "permissions": ["core:default", "shell:allow-execute", "shell:allow-open", "shell:allow-spawn", "shell:default", "core:window:default", "core:window:allow-start-dragging", "core:window:allow-close", "core:window:allow-hide", "core:app:allow-default-window-icon", "core:webview:allow-create-webview-window", "core:window:allow-set-resizable", "core:window:allow-set-size", "core:window:allow-set-position", "core:window:allow-show", "core:window:allow-set-focus", "core:window:allow-unminimize", "dialog:default", "dialog:allow-open", "notification:allow-cancel", "notification:allow-check-permissions", "notification:allow-create-channel", "notification:allow-delete-channel", "notification:allow-is-permission-granted", "notification:allow-notify", "notification:allow-permission-state", "notification:allow-request-permission", "notification:allow-show", "process:default", "process:allow-exit", "process:allow-restart", "prevent-default:default", "deep-link:default", {"identifier": "shell:allow-execute", "allow": [{"args": ["/select,", {"validator": "*"}], "cmd": "explorer", "name": "showItemInFolder"}, {"args": ["/d", {"validator": "*"}], "cmd": "wt", "name": "openTerminalAtPath"}, {"args": [], "cmd": "explorer.exe", "name": "openExplorer"}, {"args": ["shell:RecycleBinFolder"], "cmd": "explorer.exe", "name": "openRecycleBin"}, {"args": ["shell:Downloads"], "cmd": "explorer.exe", "name": "openDownloads"}, {"args": [], "cmd": "control", "name": "openControlPanel"}, {"args": ["ms-settings:"], "cmd": "explorer.exe", "name": "openSystemSettings"}, {"args": ["ms-settings:about"], "cmd": "explorer.exe", "name": "openEnvVarSettings"}, {"args": ["ms-settings:accountsinfo"], "cmd": "explorer.exe", "name": "openUserAccounts"}, {"args": ["ms-settings:network"], "cmd": "explorer.exe", "name": "openNetworkSettings"}, {"args": ["ms-settings:powersleep"], "cmd": "explorer.exe", "name": "openPowerOptions"}, {"args": [], "cmd": "regedit.exe", "name": "openRegistryEditor"}, {"args": ["/c", "devmgmt.msc"], "cmd": "cmd.exe", "name": "openDeviceManager"}, {"args": ["/c", "diskmgmt.msc"], "cmd": "cmd.exe", "name": "openDiskManagement"}, {"args": ["/c", "services.msc"], "cmd": "cmd.exe", "name": "openServices"}, {"args": [], "cmd": "taskmgr.exe", "name": "openTaskManager"}, {"args": ["/c", "eventvwr.msc"], "cmd": "cmd.exe", "name": "openEventViewer"}, {"args": ["/c", "gpedit.msc"], "cmd": "cmd.exe", "name": "openGroupPolicy"}]}, "global-shortcut:allow-is-registered", "global-shortcut:allow-register", "global-shortcut:allow-unregister", "os:default", "updater:default"]}}