struct HotkeyListenerLib {
    register_hotkey_func:
        libloading::Symbol<'static, unsafe extern "C" fn(i32, i32, i32, i32, i32)>,
    get_key_status_func: libloading::Symbol<'static, unsafe extern "C" fn() -> u32>,
    start_listen_func: libloading::Symbol<'static, unsafe extern "C" fn()>,
    stop_listen_func: libloading::Symbol<'static, unsafe extern "C" fn()>,
    is_ctrl_key_double_clicked_func: libloading::Symbol<'static, unsafe extern "C" fn() -> u32>,
    is_shift_key_double_clicked_func: libloading::Symbol<'static, unsafe extern "C" fn() -> u32>,
}

impl HotkeyListenerLib {
    pub fn new(lib_name: &str) -> Result<Self, Box<dyn std::error::Error>> {
        unsafe {
            let lib = libloading::Library::new(lib_name)?;
            let lib = Box::leak(Box::new(lib));

            Ok(Self {
                register_hotkey_func: lib.get(b"registerHotKey")?,
                get_key_status_func: lib.get(b"getKeyStatus")?,
                start_listen_func: lib.get(b"startListen")?,
                stop_listen_func: lib.get(b"stopListen")?,
                is_ctrl_key_double_clicked_func: lib.get(b"isCtrlKeyDoubleClicked")?,
                is_shift_key_double_clicked_func: lib.get(b"isShiftKeyDoubleClicked")?,
            })
        }
    }
}

const HOTKEY_LISTENER_LIB_NAME: &str = "hotkeyListener.dll";

lazy_static::lazy_static! {
    static ref hotkey_listener_lib: HotkeyListenerLib = HotkeyListenerLib::new(HOTKEY_LISTENER_LIB_NAME)
        .expect("Failed to create API instance");
}

pub fn register_hotkey(
    key1: i32,
    key2: i32,
    key3: i32,
    key4: i32,
    key5: i32,
) -> Result<(), Box<dyn std::error::Error>> {
    unsafe {
        (hotkey_listener_lib.register_hotkey_func)(key1, key2, key3, key4, key5);
        Ok(())
    }
}

pub fn get_key_status() -> Result<u32, Box<dyn std::error::Error>> {
    unsafe { Ok((hotkey_listener_lib.get_key_status_func)()) }
}

pub fn start_listen() -> Result<(), Box<dyn std::error::Error>> {
    unsafe {
        std::thread::spawn(|| {
            (hotkey_listener_lib.start_listen_func)();
        });
        Ok(())
    }
}

pub fn stop_listen() -> Result<(), Box<dyn std::error::Error>> {
    unsafe {
        (hotkey_listener_lib.stop_listen_func)();
        Ok(())
    }
}

pub fn is_ctrl_key_double_clicked() -> Result<u32, Box<dyn std::error::Error>> {
    unsafe { Ok((hotkey_listener_lib.is_ctrl_key_double_clicked_func)()) }
}

pub fn is_shift_key_double_clicked() -> Result<u32, Box<dyn std::error::Error>> {
    unsafe { Ok((hotkey_listener_lib.is_shift_key_double_clicked_func)()) }
}

//帮我使用Windows API调用GetAsyncKeyState函数，并返回结果
pub fn get_async_key_state(key: i32) -> Result<i16, Box<dyn std::error::Error>> {
    //使用windows crate的GetAsyncKeyState函数
    let state = unsafe { windows::Win32::UI::Input::KeyboardAndMouse::GetAsyncKeyState(key) };
    Ok(state)
}

pub fn get_swap_button_state() -> Result<i32, Box<dyn std::error::Error>> {
    let swap_button = unsafe {
        windows::Win32::UI::WindowsAndMessaging::GetSystemMetrics(
            windows::Win32::UI::WindowsAndMessaging::SM_SWAPBUTTON,
        )
    };
    Ok(swap_button)
}
