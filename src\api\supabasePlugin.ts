import { createClient } from "@supabase/supabase-js";

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON_KEY
);

const supabasePluginApi = {
  async getByIdentifier(identifier: string) {
    try {
      const { data, error } = await supabase
        .from("plugin_info")
        .select("*")
        .eq("identifier", identifier)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      // 如果是未找到记录的错误，返回 null
      if (error?.code === "PGRST116") {
        return null;
      }
      // 其他错误则抛出
      throw error;
    }
  },
};

export default supabasePluginApi;
