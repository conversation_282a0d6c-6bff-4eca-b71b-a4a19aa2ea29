# Aiverything 设置配置完整指南

## 📋 目录
- [设置概述](#设置概述)
- [进入设置界面](#进入设置界面)
- [账户设置](#账户设置)
- [常规设置](#常规设置)
- [索引设置](#索引设置)
- [搜索设置](#搜索设置)
- [数据类型设置](#数据类型设置)
- [缓存设置](#缓存设置)
- [快捷键设置](#快捷键设置)
- [高级设置](#高级设置)
- [配置导入导出](#配置导入导出)
- [性能优化建议](#性能优化建议)
- [常见问题解答](#常见问题解答)

---

## 🎯 设置概述

Aiverything 提供了丰富的配置选项，让您可以根据个人需求和使用习惯进行个性化定制。通过合理配置，您可以：

- **提升搜索效率**：优化索引和搜索算法
- **个性化体验**：自定义界面语言、快捷键等
- **性能优化**：根据硬件配置调整参数
- **功能扩展**：配置AI功能和插件系统

![settings](./pictures/settings.png)

---

## 🚪 进入设置界面

### 访问方式

有多种方式进入设置界面：

1. **系统托盘方式**（推荐）
   - 右键点击系统托盘中的 Aiverything 图标
   - 选择"设置"菜单项

2. **搜索窗口方式**
   - 打开 Aiverything 搜索窗口
   - 点击右下角的设置图标

---

## 👤 账户设置

### 用户登录

- **登录功能**：支持用户账户登录
- **许可证管理**：查看和管理软件许可证
- **用户信息**：显示当前登录用户的基本信息

---

## ⚙️ 常规设置

### 启动设置

#### 开机启动
- **功能说明**：设置 Aiverything 是否随系统启动
- **推荐设置**：建议启用，确保随时可用

![add startup](./pictures/addstartup.png)

### 搜索引擎设置

#### 默认搜索引擎
当本地搜索无结果时，可使用网络搜索引擎：

| 搜索引擎 |
|----------|
| Google |
| Bing |
| 百度 |
| DuckDuckGo |

![search engine](./pictures/search%20engine.png)

### 语言设置

#### 界面语言
- **中文简体**：默认语言
- **English**：英文界面
- **日本語**：日文界面

![language settings](./pictures/language%20settings.png)

### 文件管理器集成

#### 贴靠资源管理器
- **功能说明**：将 Aiverything 附加到 Windows 资源管理器
- **使用效果**：在文件管理器中可以快速搜索当前目录

---

## 📁 索引设置

索引设置是 Aiverything 的核心配置，直接影响搜索性能和结果。

### 磁盘选择

#### 索引磁盘管理
- **选择磁盘**：勾选需要建立索引的磁盘
- **实时监控**：监控选中磁盘的文件变化

![disk settings](./pictures/disk%20settings.png)

#### 推荐配置
| 磁盘类型 | 是否索引 | 原因 |
|----------|----------|------|
| 数据盘(C:/D:/E:) | ✅ 推荐 | 用户文档和数据文件 |
| 外接存储 | ⚠️ 可选 | 根据使用频率决定 |
| 网络驱动器 | ❌ 不推荐 | 网络延迟影响性能 |

### 缓存大小配置

#### 索引缓存设置
- **缓存数量**：设置最大缓存文件数量，每次打开搜索结果时将会把结果存放到缓存，下次搜索将会优先进行匹配

### 文件变更检查

#### 监控设置
- **检查间隔**：设置文件变更检查的时间间隔

![cache and file monitor](./pictures/cache%20and%20filemonitor%20settings.png)

### 忽略目录设置

#### 系统默认忽略目录
- **$Recycle.Bin**：回收站目录
- **Windows**：Windows系统目录（部分）
- **WindowsApps**：UWP应用安装目录
- **Recent**：文件历史记录
- **ProgramData**：应用数据

#### 自定义忽略目录
- **添加目录**：手动添加不需要索引的目录
- **临时文件夹**：建议忽略临时文件目录

![ignore path settings](./pictures/ignore%20path%20settings.png)

#### 常见忽略目录建议
```
C:\Windows\Temp
C:\Users\<USER>\AppData\Local\Temp
node_modules
```

---

## 🔍 搜索设置

### 模糊匹配配置

#### 智能匹配算法
- **启用模糊匹配**：允许非精确匹配搜索

![fuzzy match settings](./pictures/fuzzy%20match%20settings.png)

### GPU 设备配置

#### GPU 加速设置
- **设备选择**：选择用于加速的GPU设备，支持CUDA已经OpenCL框架
- **内存监控**：当检测到GPU内存占用过多将会自动进行释放，不会影响到其他软件的运行

![gpu settings](./pictures/gpu%20settings.png)

### 优先文件夹设置

#### 搜索优先级配置
- **添加优先文件夹**：设置搜索时优先显示的目录

![priority folder empty](./pictures/priority%20empty%20settings.png)

![priority folder set](./pictures/priority%20set%20settings.png)

---

## 📊 数据类型设置

### 文件类型映射

#### 内置文件类型
Aiverything 内置了常见文件类型的分类：

| 类型 | 扩展名 | 图标 |
|------|--------|------|
| 文档 | .doc, .docx, .pdf, .txt | 📄 |
| 表格 | .xls, .xlsx, .csv | 📊 |
| 演示 | .ppt, .pptx | 📈 |
| 图片 | .jpg, .png, .gif, .bmp | 🖼️ |
| 视频 | .mp4, .avi, .mkv, .mov | 🎬 |
| 音频 | .mp3, .wav, .flac | 🎵 |
| 压缩 | .zip, .rar, .7z | 📦 |

![datatype settings](./pictures/datatype%20settings.png)

#### 自定义文件类型
- **添加新类型**：为特殊文件格式添加自定义分类

### 自定义分类配置

#### 创建新分类
1. **分类命名**：为新分类设置名称
2. **扩展名绑定**：指定属于该分类的文件扩展名

![datatype add settings](./pictures/datatype%20add%20settings.png)

![suffix list settings](./pictures/suffix%20list%20settings.png)

---

## 💾 缓存设置

### 缓存列表管理

#### 缓存文件查看
- **缓存列表**：显示所有已缓存的文件
- **批量操作**：支持批量删除、导出缓存项

![cache settings](./pictures/cache%20settings.png)

---

## ⌨️ 快捷键设置

### 全局快捷键配置

#### 主要快捷键设置
- **呼出搜索窗口**：默认 `Ctrl + Shift + Alt + A`
- **快速搜索**：双击 Ctrl 键快速呼出
- **附加模式搜索**：双击 Shift 键

#### 快捷键自定义步骤
1. **选择功能**：选择要设置快捷键的功能
2. **清除现有**：清除当前的快捷键设置
3. **录制新键**：按下新的快捷键组合
4. **保存应用**：保存设置并应用

### 文件操作快捷键

#### 操作快捷键配置
- **复制路径**：默认 `Alt + Enter`
- **打开父文件夹**：默认 `Ctrl + Enter`
- **管理员权限打开**：默认 `Shift + Enter`

### 双击功能设置

#### 双击 Ctrl 设置
- **启用/禁用**：控制双击 Ctrl 功能开关
- **响应时间**：设置双击的时间间隔阈值
- **冲突处理**：处理与其他应用的快捷键冲突

---

## 🔧 高级设置

### 大语言模型配置

#### Ollama 集成配置
- **服务地址**：配置 Ollama 服务的地址
- **模型管理**：管理本地的 AI 模型

![ollama model settings](./pictures/ollama%20models.png)

### 内容索引设置

#### 文件内容索引
- **启用内容索引**：索引文件内部的文本内容

![content index settings](./pictures/content%20match%20settings.png)

#### 内容搜索配置
- **全文搜索**：在文件内容中搜索关键词
- **搜索过滤器**：使用 `|c` 过滤器进行内容搜索
- **预览支持**：在搜索结果中显示内容预览
### 缓存块配置

#### 内存缓存参数
- **缓存块大小**：配置内存缓存块最少以及最多包含几个文件记录

![cache block size settings](./pictures/cache%20block%20size%20settings.png)

### 调试模式

#### 开发者选项
- **JVM远程调试**：开启后插件后端服务将启动35005端口用于远程调试
- **启用动态插件加载**：允许插件通过API进行动态加载

![debug mode settings](./pictures/debug%20mode%20settings.png)

#### JVM调试配置

##### JDK主目录设置
- **功能说明**：指定Java开发工具包(JDK)的安装路径
- **配置目的**：确保插件系统能够正确调用Java运行环境
- **路径示例**：`C:\Program Files\Java\jdk-21.0.x`
- **验证方法**：路径应包含`bin`、`lib`等标准JDK目录结构

##### Java Agent路径设置
- **功能说明**：配置Java代理(Agent)的JAR文件路径
- **调试支持**：用于支持插件的远程调试和性能监控
- **路径格式**：完整的`.jar`文件路径
- **注意事项**：确保Agent文件与JDK版本兼容

#### 远程调试功能

##### 调试端口配置
- **固定端口**：35005（系统自动配置，无需手动修改）
- **连接方式**：通过IDE（如IntelliJ IDEA、Eclipse）连接进行远程调试
- **安全考虑**：仅在开发环境中启用，生产环境建议关闭

##### 调试会话管理
- **启动调试**：开启调试模式后自动启动调试服务
- **连接状态**：设置界面显示当前调试连接状态
- **会话控制**：支持断开和重新连接调试会话

---

## ❓ 常见问题解答

### 设置问题

**Q: 修改设置后不生效怎么办？**

A: 按以下步骤排查：
1. **重启应用**：某些设置需要重启后生效
2. **权限检查**：确保有足够权限修改配置文件
3. **配置文件**：检查`config.json`、`core/user/settings.json`配置文件是否被只读保护

### 性能问题

**Q: 索引过程很慢怎么办？**

A: 优化方法：
1. **减少索引范围**：排除不必要的目录
2. **优化磁盘**：使用SSD存储索引

**Q: GPU 加速不工作？**

A: 检查步骤：
1. **驱动更新**：确保显卡驱动为最新版本
2. **设备选择**：在设置中选择正确的GPU设备
3. **兼容性**：确认显卡支持所需的计算能力

### 功能问题

**Q: AI 功能无法使用？**

A: 解决方案：
1. **网络连接**：检查网络连接是否正常
2. **服务配置**：确认Ollama配置正确
3. **模型下载**：确保所需的AI模型已下载

### JVM调试问题

**Q: 插件远程调试无法连接？**

A: 排查步骤：
1. **端口检查**：确认35005端口没有被其他程序占用
2. **JDK配置**：验证JDK主目录路径是否正确
3. **Agent路径**：检查Java Agent路径设置是否有效
4. **防火墙设置**：确认防火墙允许35005端口通信
5. **IDE配置**：检查IDE的远程调试配置是否正确

**Q: JDK主目录设置后不生效？**

A: 解决方法：
1. **路径验证**：确保路径指向有效的JDK安装目录
2. **权限检查**：确认Aiverything有读取JDK目录的权限
3. **版本兼容**：使用JDK 21或更高版本
4. **重启应用**：修改JDK路径后需要重启Aiverything

**Q: Java Agent加载失败？**

A: 检查要点：
1. **文件存在**：确认Agent JAR文件存在且路径正确
2. **文件完整性**：验证JAR文件没有损坏
3. **版本匹配**：确保Agent与JDK版本兼容
4. **文件权限**：检查Agent文件的读取权限

---

## 📞 获取支持

### 技术支持渠道

- **官方文档**：[https://aiverything.me/docs](https://aiverything.me/docs)
- **GitHub Issues**：[问题反馈](https://github.com/panwangwin/aiverything-official-forum/issues)
- **QQ交流群**：893463594
- **邮件支持**：<EMAIL>

### 反馈设置问题

在反馈设置相关问题时，请提供：
1. **设置截图**：相关设置页面的截图
2. **系统信息**：操作系统版本和硬件配置
3. **错误日志**：相关的错误日志信息
4. **操作步骤**：重现问题的详细步骤

---

*本指南涵盖了 Aiverything 的所有设置选项，建议根据个人需求和硬件配置进行合理调整。* 