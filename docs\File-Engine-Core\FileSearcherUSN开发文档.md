# FileSearcherUSN 开发文档

## 1. 概述

### 1.1 项目简介

FileSearcherUSN是File-Engine-Core项目中的文件索引组件，基于Windows NTFS文件系统的USN Journal（更新序列号日志）技术实现高性能文件索引。该组件通过直接读取NTFS卷的USN Journal来快速获取文件系统中的所有文件信息，并将索引数据存储到SQLite数据库中。

### 1.2 核心特性

- **高性能索引**: 基于NTFS USN Journal技术，直接从文件系统底层获取文件信息
- **多线程处理**: 采用生产者-消费者模式，支持多磁盘并行索引
- **智能分表存储**: 根据文件名ASCII码值自动分表存储，优化查询性能
- **路径缓存优化**: 实现父路径缓存机制，减少重复存储
- **文件优先级管理**: 支持基于文件后缀的优先级分类
- **加密数据库**: 支持SQLite数据库加密存储

### 1.3 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                    FileSearcherUSN架构                      │
├─────────────────────────────────────────────────────────────┤
│  Main Process                                               │
│  ├── 配置读取 (MFTSearchInfo.dat)                          │
│  ├── 多线程管理 (每个磁盘一个线程)                          │
│  └── 优先级映射初始化                                      │
├─────────────────────────────────────────────────────────────┤
│  Volume Processing (per disk)                              │
│  ├── USN Journal 初始化                                    │
│  ├── 生产者线程 (USN数据读取和处理)                        │
│  ├── 消费者线程 (数据库写入)                               │
│  └── 线程池 (文件存在性检查)                               │
├─────────────────────────────────────────────────────────────┤
│  Data Storage                                               │
│  ├── SQLite数据库 (每磁盘一个数据库)                       │
│  ├── 分表存储 (list0-list40 + folder表)                   │
│  └── 加密存储支持                                          │
└─────────────────────────────────────────────────────────────┘
```

## 2. 核心组件详解

### 2.1 主要数据结构

#### 2.1.1 参数结构体

```cpp
using parameter = struct parameter {
    char disk{'\0'};                    // 磁盘盘符
    std::vector<std::string> ignorePath; // 忽略路径列表
    sqlite3* db{nullptr};               // SQLite数据库连接
};
```

#### 2.1.2 记录结构体

```cpp
typedef struct record_struct_ {
    int ascii = 0;                       // 文件名ASCII码和
    unsigned parent_path_id = 0;         // 父路径缓存ID
    std::string file_name;               // 文件名
    LONGLONG file_size = 0;              // 文件大小
    DWORD file_attribute = INVALID_FILE_ATTRIBUTES; // 文件属性
    LONGLONG modify_time = 0;            // 修改时间
} RECORD_STRUCT;
```

#### 2.1.3 USN记录结构体

```cpp
typedef struct pfrn_name_ {
    DWORDLONG pfrn = 0;                  // 父文件引用号
    std::string filename;                // 文件名
    LONGLONG modify_time = 0;            // 修改时间
    DWORD file_attribute = INVALID_FILE_ATTRIBUTES; // 文件属性
} PFRN_NAME;
```

### 2.2 Volume类详解

Volume类是文件索引的核心处理类，负责单个磁盘的完整索引过程。

#### 2.2.1 类成员变量

```cpp
class volume {
private:
    char vol;                            // 磁盘盘符
    unsigned idGenerator = 0;            // ID生成器
    BS::thread_pool index_thread_pool;   // 索引线程池
    HANDLE hVol;                         // 磁盘句柄
    
    // 核心映射表
    Frn_Pfrn_Name_Map frnPfrnNameMap;           // FRN到文件信息的映射
    emhash8::HashMap<std::string, DWORDLONG> parentPathIdMap;     // 父路径到ID的映射
    
    // 路径缓存系统
    std::vector<std::string> parentPathCacheVec;                 // 父路径缓存list
    emhash8::HashMap<std::string, unsigned> parentPathCacheIndexMap; // 父路径到缓存索引的映射
    std::shared_mutex parentPathMutex;                           // 路径缓存互斥锁
    
    // 数据库相关
    sqlite3* db;                         // 数据库连接
    sqlite3_stmt* stmt0-stmt40;         // 预编译语句(list0-list40)
    sqlite3_stmt* stmt_folder;          // 文件夹表预编译语句
    
    USN_JOURNAL_DATA ujd{};              // USN Journal数据
    std::vector<std::string>* ignore_path_vector_; // 忽略路径
    PriorityMap* priority_map_;          // 优先级映射
};
```

#### 2.2.2 核心处理流程

**1. 初始化阶段**

```cpp
void volume::init_volume() {
    if (get_handle() && create_usn() && get_usn_info() && get_usn_journal()) {
        read_parent_path_id();  // 读取已存在的父路径ID映射
        
        // 启动异步搜索处理
        search_internal_async();
    }
}
```

**2. USN Journal处理**

```cpp
bool volume::get_usn_journal() {
    MFT_ENUM_DATA med;
    med.StartFileReferenceNumber = 0;
    med.LowUsn = 0;
    med.HighUsn = ujd.NextUsn;
    
    CHAR* buffer = new CHAR[BUF_LEN];
    
    while (true) {
        // 读取USN记录
        if (0 == DeviceIoControl(hVol, FSCTL_ENUM_USN_DATA, &med, 
                                sizeof med, buffer, BUF_LEN, &usn_data_size, nullptr)) {
            break;
        }
        
        // 处理USN记录
        auto usn_record = reinterpret_cast<PUSN_RECORD>(buffer + sizeof(USN));
        while (dw_ret_bytes > 0) {
            // 提取文件信息并存储到frnPfrnNameMap
            const std::wstring cfile_name(usn_record->FileName, 
                                        usn_record->FileNameLength / 2);
            
            PFRN_NAME pfrn_name;
            pfrn_name.filename = to_utf8(cfile_name);
            pfrn_name.file_attribute = usn_record->FileAttributes;
            pfrn_name.pfrn = usn_record->ParentFileReferenceNumber;
            pfrn_name.modify_time = usn_record->TimeStamp.QuadPart;
            
            frnPfrnNameMap.emplace_unique(usn_record->FileReferenceNumber, pfrn_name);
            
            // 移动到下一个记录
            usn_record = reinterpret_cast<PUSN_RECORD>(
                reinterpret_cast<PCHAR>(usn_record) + usn_record->RecordLength);
            dw_ret_bytes -= usn_record->RecordLength;
        }
        
        med.StartFileReferenceNumber = *reinterpret_cast<DWORDLONG*>(buffer);
    }
}
```

### 2.3 多线程处理机制

#### 2.3.1 生产者-消费者模式

系统采用生产者-消费者模式来处理文件索引：

```cpp
// 生产者线程：处理USN数据
std::thread producer_thread([this, &working_queue, &is_producer_exit] {
    auto&& start_iter = frnPfrnNameMap.begin();
    const auto& end_iter = frnPfrnNameMap.end();
    
    while (start_iter != end_iter) {
        // 重构文件完整路径
        std::string result_path;
        get_path(start_iter->first, result_path);
        const auto record = vol + result_path;
        
        // 提交到线程池进行并行处理
        index_thread_pool.submit_task([=] {
            if (!is_ignore(record)) {
                // 检查文件存在性
                bool file_exist = is_file_exist(string2wstring(record));
                if (file_exist) {
                    // 构造记录结构体并推入队列
                    RECORD_STRUCT record_struct;
                    // ... 填充记录信息
                    working_queue.push(record_struct);
                }
            }
        });
        ++start_iter;
    }
    index_thread_pool.wait();
    is_producer_exit.store(true);
});

// 消费者线程：数据库写入
std::thread consumer_thread([this, &working_queue, &is_producer_exit] {
    init_all_prepare_statement();  // 初始化所有预编译语句
    
    while (!is_producer_exit.load() || !working_queue.empty()) {
        RECORD_STRUCT record_struct;
        if (working_queue.try_pop(record_struct)) {
            collect_result_to_result_map(record_struct);
            
            // 定期提交事务，避免内存占用过高
            if (++count > SAVE_TO_DATABASE_RECORD_CHECKPOINT) {
                count = 0;
                finalize_all_statement();
                init_all_prepare_statement();
            }
        } else {
            std::this_thread::yield();
        }
    }
    finalize_all_statement();
});
```

### 2.4 路径重构算法

#### 2.4.1 路径重构原理

由于USN Journal中的文件记录只包含文件名和父文件引用号(PFRN)，需要通过递归查找来重构完整路径：

```cpp
void volume::get_path(DWORDLONG frn, std::string& output_path) {
    const auto end = frnPfrnNameMap.end();
    
    while (true) {
        const auto it = frnPfrnNameMap.find(frn);
        if (it == end) {
            output_path = ":" + output_path;  // 到达根目录
            return;
        }
        
        // 向前拼接路径
        output_path = "\\" + it->second.filename + output_path;
        frn = it->second.pfrn;  // 递归到父目录
    }
}
```

#### 2.4.2 路径缓存优化

为了减少重复存储相同的父路径，系统实现了路径缓存机制：

```cpp
unsigned volume::get_parent_path_cache_id(const std::string& parent_path) {
    unsigned parent_path_id_find = 0;
    bool found = false;
    
    // 先尝试读锁查找
    {
        std::shared_lock slck(this->parentPathMutex);
        const auto& parent_path_iter = parentPathCacheIndexMap.find(parent_path);
        if (parent_path_iter != parentPathCacheIndexMap.end()) {
            parent_path_id_find = parent_path_iter->second;
            found = true;
        }
    }
    
    // 如果未找到，使用写锁添加新路径
    if (!found) {
        std::unique_lock ulck(this->parentPathMutex);
        parent_path_id_find = idGenerator;
        parentPathCacheVec.emplace_back(parent_path);
        parentPathCacheIndexMap.emplace_unique(parent_path, parent_path_id_find);
        idGenerator++;
    }
    
    return parent_path_id_find;
}
```

## 3. 数据库设计

### 3.1 分表策略

系统根据文件名的ASCII码值将文件记录分散存储到41个表中（list0-list40），以优化查询性能：

```cpp
void volume::collect_result_to_result_map(const RECORD_STRUCT& record_struct) {
    int ascii_group = record_struct.ascii / 100;
    if (ascii_group > MAX_TABLE_NUM) {
        ascii_group = MAX_TABLE_NUM;
    }
    
    const int priority = get_priority_by_name(record_struct.file_attribute, 
                                            record_struct.file_name);
    const auto& parent_path = get_path_by_cache_id(record_struct.parent_path_id);
    
    save_result(parent_path, record_struct.file_name, record_struct.ascii, 
               ascii_group, priority, record_struct.file_size, record_struct.modify_time);
}
```

### 3.2 表结构设计

#### 3.2.1 文件表结构 (list0-list40)

```sql
CREATE TABLE IF NOT EXISTS list0 (
    ASCII INT,                    -- 文件名ASCII码和
    NAME TEXT UNIQUE,             -- 文件名
    FILE_SIZE INTEGER,            -- 文件大小
    MODIFY_DATE INTEGER,          -- 修改时间
    PRIORITY INT,                 -- 优先级
    FOLDER_ID INTEGER,            -- 父文件夹ID
    PRIMARY KEY("ASCII","NAME","PRIORITY")
);
```

#### 3.2.2 文件夹表结构

```sql
CREATE TABLE IF NOT EXISTS "folder" (
    "ID" INTEGER,                 -- 自增主键
    "PATH" TEXT UNIQUE,           -- 文件夹路径
    PRIMARY KEY("ID" AUTOINCREMENT)
);
```

### 3.3 数据库优化配置

```cpp
// SQLite性能优化配置
sqlite3_exec(p.db, "PRAGMA TEMP_STORE=MEMORY;", nullptr, nullptr, nullptr);
sqlite3_exec(p.db, "PRAGMA cache_size=262144;", nullptr, nullptr, nullptr);
sqlite3_exec(p.db, "PRAGMA auto_vacuum=0;", nullptr, nullptr, nullptr);
sqlite3_exec(p.db, "PRAGMA mmap_size=268435456;", nullptr, nullptr, nullptr);
sqlite3_exec(p.db, "PRAGMA synchronous=OFF;", nullptr, nullptr, nullptr);
sqlite3_exec(p.db, "PRAGMA journal_mode=MEMORY;", nullptr, nullptr, nullptr);
```

## 4. 文件优先级系统

### 4.1 优先级映射

系统通过文件后缀名来确定文件的搜索优先级：

```cpp
int volume::get_priority_by_name(const DWORD file_attribute, 
                                const std::string& file_name) const {
    // 文件夹特殊处理
    if (file_attribute != INVALID_FILE_ATTRIBUTES && 
        file_attribute & FILE_ATTRIBUTE_DIRECTORY) {
        return get_priority_by_suffix("dirPriority");
    }
    
    // 提取文件后缀
    auto&& suffix = file_name.substr(file_name.find_last_of('.') + 1);
    transform(suffix.begin(), suffix.end(), suffix.begin(), tolower);
    
    return get_priority_by_suffix(suffix);
}

int volume::get_priority_by_suffix(const std::string& suffix) const {
    auto&& iter = priority_map_->find(suffix);
    if (iter == priority_map_->end()) {
        return get_priority_by_suffix("defaultPriority");  // 默认优先级
    }
    return iter->second;
}
```

### 4.2 优先级初始化

```cpp
bool init_priority_map(PriorityMap& priority_map, const char* priority_db_path) {
    sqlite3* cache_db;
    sqlite3_open(priority_db_path, &cache_db);
    
    // 设置数据库加密密钥
    auto&& key = encrypt::getkey();
    std::string pragma_command = "PRAGMA key = '" + key + "';";
    sqlite3_exec(cache_db, pragma_command.c_str(), nullptr, nullptr, nullptr);
    
    // 查询优先级映射表
    const string sql = "select * from priority;";
    char** p_result;
    int row, column;
    sqlite3_get_table(cache_db, sql.c_str(), &p_result, &row, &column, &error);
    
    // 构建优先级映射
    auto i = 2;
    for (const auto total = column * row + 2; i < total; i += 2) {
        const string suffix(p_result[i]);
        const string priority_val(p_result[i + 1]);
        priority_map.emplace_unique(suffix, stoi(priority_val));
    }
    
    priority_map.emplace_unique("dirPriority", -1);  // 文件夹默认优先级
    
    sqlite3_free_table(p_result);
    sqlite3_close(cache_db);
    return true;
}
```

## 5. 配置与启动流程

### 5.1 配置文件格式

系统通过`MFTSearchInfo.dat`文件读取配置信息：

```
第一行: 磁盘盘符列表 (用逗号分隔，如: C,D,E)
第二行: 输出数据库路径 (如: D:\FileEngine\data)
第三行: 忽略路径列表 (用逗号分隔，如: recycle,temp)
```

### 5.2 启动流程

```cpp
int main() {
    char disk_path[MAX_PATH_LENGTH]{0};
    char output[MAX_PATH_LENGTH]{0};
    char ignore_path[MAX_PATH_LENGTH]{0};
    
    // 1. 读取配置文件
    if (!get_search_info(disk_path, output, ignore_path)) {
        return 1;
    }
    
    // 2. 解析配置
    vector<string> disk_vector;
    vector<string> ignore_paths_vector;
    split_string(disk_path, disk_vector);
    split_string(ignore_path, ignore_paths_vector);
    
    // 3. SQLite多线程配置
    sqlite3_config(SQLITE_CONFIG_MULTITHREAD);
    sqlite3_config(SQLITE_CONFIG_MEMSTATUS, 0);
    
    // 4. 为每个磁盘创建处理线程
    vector<thread> threads;
    bool is_priority_map_initialized = false;
    
    for (auto& iter : disk_vector) {
        if (const auto disk = iter[0]; 'A' <= disk && disk <= 'Z') {
            parameter p;
            p.disk = disk;
            p.ignorePath = ignore_paths_vector;
            
            // 构建数据库路径: output\X.db
            char tmp_db_path[MAX_PATH_LENGTH]{0};
            strcpy_s(tmp_db_path, output);
            strcat_s(tmp_db_path, "\\");
            tmp_db_path[strlen(tmp_db_path)] = disk;
            strcat_s(tmp_db_path, ".db");
            
            // 打开数据库并设置加密
            sqlite3_open(tmp_db_path, &p.db);
            auto&& key = encrypt::getkey();
            std::string pragma_command = "PRAGMA key = '" + key + "';";
            sqlite3_exec(p.db, pragma_command.c_str(), nullptr, nullptr, nullptr);
            
            // 初始化优先级映射(只需初始化一次)
            if (!is_priority_map_initialized) {
                is_priority_map_initialized = true;
                tmp_db_path[strlen(tmp_db_path) - 4] = '\0';
                strcat_s(tmp_db_path, "cache.db");
                init_priority_map(suffix_priority_map, tmp_db_path);
            }
            
            // 数据库性能优化
            sqlite3_exec(p.db, "PRAGMA TEMP_STORE=MEMORY;", nullptr, nullptr, nullptr);
            sqlite3_exec(p.db, "PRAGMA cache_size=262144;", nullptr, nullptr, nullptr);
            // ... 其他优化配置
            
            // 启动磁盘索引线程
            threads.emplace_back(start_search, p);
        }
    }
    
    // 5. 等待所有线程完成
    for (auto& each_thread : threads) {
        if (each_thread.joinable()) {
            each_thread.join();
        }
    }
    
    return 0;
}
```

## 6. 性能优化策略

### 6.1 内存优化

- **路径缓存**: 通过父路径缓存避免重复存储相同路径
- **预编译语句**: 使用SQLite预编译语句提高数据库写入性能
- **内存映射**: 启用SQLite内存映射模式减少磁盘I/O

### 6.2 并发优化

- **多线程处理**: 每个磁盘使用独立线程处理
- **生产者-消费者**: 解耦USN数据读取和数据库写入
- **线程池**: 使用BS::thread_pool进行文件存在性检查

### 6.3 数据库优化

- **分表存储**: 根据ASCII码值分表，提高查询效率
- **批量提交**: 每100万条记录提交一次事务
- **关闭同步**: 关闭SQLite同步模式提高写入速度

## 7. 错误处理与监控

### 7.1 错误处理机制

```cpp
// USN Journal初始化失败处理
if (!get_handle() || !create_usn() || !get_usn_info() || !get_usn_journal()) {
    fprintf(stderr, "fileSearcherUSN: init usn journal failed.\n");
    return;
}

// 数据库操作错误处理
if (SQLITE_OK != sqlite3_open(tmp_db_path, &p.db)) {
    fprintf(stderr, "fileSearcherUSN: error opening database");
    return 1;
}
```

### 7.2 进度监控

```cpp
void volume::init_volume() {
    try {
        printf("start to collect disk %c info.\n", this->get_disk_path());
        auto&& start_time = timeSinceEpochMillisec();
        
        search_internal_async();
        
        auto&& end_time = timeSinceEpochMillisec();
        printf("collect disk %c complete. time in mills: %llu\n", 
               this->get_disk_path(), end_time - start_time);
    } catch (exception& e) {
        fprintf(stderr, "fileSearcherUSN: %s\n", e.what());
    }
}
```

## 8. 开发指南

### 8.1 编译环境

- **操作系统**: Windows 10/11
- **编译器**: Visual Studio 2019或更高版本
- **依赖库**: SQLite3, BS::thread_pool
- **文件系统**: NTFS (USN Journal支持)

### 8.2 调试配置

```cpp
// 启用调试模式
#define TEST

#ifdef TEST
#include <iostream>
std::cout << "disk path: " << disk_path << endl;
std::cout << "output: " << output << endl;
std::cout << "ignore_path" << ignore_path << endl;
#endif
```

### 8.3 常见问题

**Q: USN Journal初始化失败**

A: 检查程序是否以管理员权限运行，USN Journal需要管理员权限访问。

**Q: 数据库加密失败**

A: 确保encryption.dll存在并且getkey函数可以正常调用。

**Q: 索引速度慢**

A: 检查磁盘I/O性能，考虑调整SAVE_TO_DATABASE_RECORD_CHECKPOINT参数。

---

## 附录

### A. 常量定义

```cpp
#define SAVE_TO_DATABASE_RECORD_CHECKPOINT 1000000  // 数据库提交检查点
#define MAX_TABLE_NUM 40                            // 最大表数量
constexpr size_t MAX_PATH_LENGTH = 500;             // 最大路径长度
constexpr auto BUF_LEN = sizeof(USN) * 0x100000;   // USN缓冲区大小
```

### B. 重要数据结构

- `parameter`: 磁盘处理参数
- `RECORD_STRUCT`: 文件记录结构
- `PFRN_NAME`: USN记录结构
- `PriorityMap`: 优先级映射表
- `Frn_Pfrn_Name_Map`: FRN到文件信息映射

### C. 核心函数列表

**主要函数:**
- `main()`: 程序入口点
- `start_search()`: 开始磁盘搜索
- `volume::init_volume()`: 磁盘卷初始化
- `volume::get_usn_journal()`: 获取USN日志
- `volume::get_path()`: 重构文件路径
- `init_priority_map()`: 初始化优先级映射

**工具函数:**
- `to_utf8()`: 宽字符转UTF-8
- `string2wstring()`: 字符串转宽字符串
- `get_file_name()`: 获取文件名
- `get_parent_path()`: 获取父路径
- `split_string()`: 字符串分割

---

*本文档版本: 1.0*  
*维护者: File-Engine-Core开发团队* 