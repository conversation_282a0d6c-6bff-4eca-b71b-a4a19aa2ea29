export const cropImage = (base64Image: string): Promise<string> => {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.src = base64Image;
        img.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                reject(new Error('Failed to get canvas context'));
                return;
            }

            // 设置 canvas 尺寸为图像尺寸
            canvas.width = img.width;
            canvas.height = img.height;

            // 绘制图像到 canvas
            ctx.drawImage(img, 0, 0);

            // 获取图像数据
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const { data, width, height } = imageData;

            // 找到图像的有效区域
            let top = 0, left = 0, right = width, bottom = height;
            const alphaThreshold = 80; // 透明像素检测阈值

            // 从上到下扫描
            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    const alpha = data[(y * width + x) * 4 + 3];
                    if (alpha > alphaThreshold) {
                        top = y;
                        break;
                    }
                }
                if (top > 0) break;
            }

            // 从下到上扫描
            for (let y = height - 1; y >= 0; y--) {
                for (let x = 0; x < width; x++) {
                    const alpha = data[(y * width + x) * 4 + 3];
                    if (alpha > alphaThreshold) {
                        bottom = y;
                        break;
                    }
                }
                if (bottom < height) break;
            }

            // 从左到右扫描
            for (let x = 0; x < width; x++) {
                for (let y = 0; y < height; y++) {
                    const alpha = data[(y * width + x) * 4 + 3];
                    if (alpha > alphaThreshold) {
                        left = x;
                        break;
                    }
                }
                if (left > 0) break;
            }

            // 从右到左扫描
            for (let x = width - 1; x >= 0; x--) {
                for (let y = 0; y < height; y++) {
                    const alpha = data[(y * width + x) * 4 + 3];
                    if (alpha > alphaThreshold) {
                        right = x;
                        break;
                    }
                }
                if (right < width) break;
            }

            // 裁剪图像
            const croppedWidth = right - left + 1;
            const croppedHeight = bottom - top + 1;
            const croppedCanvas = document.createElement('canvas');
            const croppedCtx = croppedCanvas.getContext('2d');

            if (!croppedCtx) {
                reject(new Error('Failed to get cropped canvas context'));
                return;
            }

            croppedCanvas.width = croppedWidth;
            croppedCanvas.height = croppedHeight;
            croppedCtx.drawImage(canvas, left, top, croppedWidth, croppedHeight, 0, 0, croppedWidth, croppedHeight);

            // 返回裁剪后的图像
            resolve(croppedCanvas.toDataURL());
        };

        img.onerror = (error) => {
            reject(new Error('Failed to load image'));
        };
    });
};