# Chat插件详细使用指南

## 概述

Chat插件是一个智能AI聊天工具，基于Ollama构建，为用户提供流畅的人工智能对话体验。它支持多会话管理、实时流式响应、聊天历史记录和Markdown渲染等功能，是一个功能完整的AI助手解决方案。

## 核心概念

### 1. AI聊天对话
- **定义**：与人工智能模型进行自然语言对话
- **特点**：支持上下文理解、流式响应、思考过程展示
- **技术基础**：基于Ollama AI平台

### 2. 会话管理
- **定义**：管理多个独立的聊天会话
- **功能**：创建、切换、删除、重命名会话
- **持久化**：会话和聊天记录自动保存

### 3. 流式响应
- **定义**：AI回复以逐字符流式方式展示
- **优势**：提供更好的实时交互体验
- **实现**：基于Token队列的异步处理

### 4. 思考过程
- **定义**：展示AI模型的思考过程
- **标识**：使用`<think></think>`标签包围
- **展示**：可折叠的思考内容面板

## 主要功能特性

### 🤖 智能对话
- **自然交流**：支持自然语言多轮对话
- **上下文理解**：维护对话上下文和历史记录
- **流式输出**：实时显示AI回复过程
- **思考展示**：可查看AI的思考过程

### 💬 会话管理
- **多会话支持**：同时管理多个独立对话
- **自动标题**：根据对话内容自动生成会话标题
- **会话切换**：快速在不同会话间切换
- **会话删除**：支持删除不需要的会话

### 📝 内容渲染
- **Markdown支持**：支持Markdown格式的内容渲染
- **代码高亮**：代码块的语法高亮显示
- **富文本展示**：支持链接、列表、表格等格式

### 🌍 用户体验
- **多语言支持**：支持中文和英文界面
- **响应式布局**：适配不同屏幕尺寸
- **实时交互**：流畅的用户交互体验

## 详细使用步骤

### 1. 启动插件

1. **打开插件界面**
   - 在插件列表中找到"Chat - Chat to AI"
   - 点击进入插件界面
![ollama settings](./pictures/ollama%20settings.png)
 
![chat plugin](./pictures/chat%20plugin.png)

2. **界面布局**
   - 左侧：会话菜单和列表
   - 右侧：聊天窗口和消息输入
   - 顶部：标题栏和语言切换

![chat plugin view](./pictures/chat%20plugin%20view.png)

### 2. 开始新对话

#### 创建新会话
1. **点击新对话**
   - 在左侧菜单点击"新对话"按钮
   - 系统会自动创建一个新的聊天会话

2. **发送第一条消息**
   - 在底部输入框输入您的问题或消息
   - 按Enter键或点击发送按钮

3. **自动生成标题**
   - 发送第一条消息后，系统会自动为会话生成标题
   - 标题基于对话内容，便于识别和管理

#### 消息输入
1. **文本输入**
   - 在底部圆形输入框中输入消息
   - 支持多行文本输入

2. **发送消息**
   - 点击右侧的发送按钮（纸飞机图标）
   - 或使用Enter键快速发送
   - 发送后消息立即显示在聊天区域

### 3. 查看AI回复

#### 流式响应体验
1. **实时显示**
   - AI回复以逐字符方式实时显示
   - 可以看到回复的生成过程
   - 底部显示加载动画直到回复完成

2. **思考过程**
   - 如果AI在回复中包含思考过程，会显示为可折叠面板
   - 点击"思考"面板可展开查看详细的思考内容
   - 思考内容帮助理解AI的推理过程

#### 内容渲染
1. **Markdown格式**
   - AI回复自动渲染Markdown格式
   - 支持标题、列表、代码块、链接等
   - 代码块具有语法高亮

2. **消息布局**
   - 用户消息显示在右侧（蓝色气泡）
   - AI回复显示在左侧（灰色气泡）
   - 每条消息都有相应的头像标识

### 4. 会话管理

#### 切换会话
1. **选择现有会话**
   - 在左侧会话列表中点击任意会话
   - 系统会自动加载该会话的聊天历史
   - 当前活跃会话会有蓝色高亮标识

2. **会话标识**
   - 每个会话显示自动生成的标题
   - 会话图标使用💬表情符号
   - 活跃会话有蓝色背景标识

#### 删除会话
1. **悬停操作**
   - 将鼠标悬停在会话条目上
   - 右侧会出现删除按钮（垃圾桶图标）

2. **确认删除**
   - 点击删除按钮
   - 系统会立即删除会话及其所有聊天记录
   - 如果删除的是当前会话，会自动切换到新对话

## 故障排除

### 常见问题

1. **AI无响应**
   - **问题**：发送消息后没有收到回复
   - **检查**：确认Ollama服务正在运行
   - **解决**：重启插件或检查AI服务状态

2. **会话丢失**
   - **问题**：重启后会话消失
   - **原因**：配置保存失败
   - **解决**：检查插件配置权限

3. **消息显示异常**
   - **问题**：消息格式错乱或显示不全
   - **解决**：刷新页面或重新加载插件
   - **预防**：确保网络连接稳定

4. **流式响应中断**
   - **问题**：AI回复中断或不完整
   - **检查**：网络连接和服务器状态
   - **解决**：重新发送消息

## 总结

Chat插件为用户提供了一个功能完整、体验流畅的AI聊天解决方案。通过智能的会话管理、实时的流式响应和丰富的内容渲染，用户可以：

- 🤖 **智能对话**：享受自然流畅的AI对话体验
- 💬 **多会话管理**：同时维护多个独立的对话主题
- 📝 **内容丰富**：获得格式化的Markdown回复内容
- 🔄 **实时交互**：体验流式响应的即时反馈
- 💾 **持久保存**：所有对话历史自动保存和恢复

无论是学习辅助、编程支持、创作帮助还是日常咨询，Chat插件都能为您提供专业可靠的AI助手服务。
