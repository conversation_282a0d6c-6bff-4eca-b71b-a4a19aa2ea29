import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../store";
const windowVibrancySlice = createSlice({
  name: "windowVibrancy",
  initialState: {
    value: false,
  },
  reducers: {
    setWindowVibrancy: (state, action: PayloadAction<boolean>) => {
      state.value = action.payload;
    },
  },
});

export const { setWindowVibrancy } = windowVibrancySlice.actions;
export const selectWindowVibrancy = (state: RootState) => state.windowVibrancy.value;
export default windowVibrancySlice.reducer;
