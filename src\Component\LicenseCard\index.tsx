import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>ooter,
  <PERSON><PERSON>eader,
} from "@/Component/ui/card";
import { Badge } from "@/Component/ui/badge";
import { CalendarDays, CheckCircle2, Clock, RefreshCcw } from "lucide-react";
import { cn } from "@/lib/utils";

interface LicenseProps {
  id: string;
  licenseType: string;
  status: string;
  expiresAt: string;
  createdAt: string;
  trial: boolean;
  autoRefresh: boolean;
}

export function LicenseCard({
  licenseType,
  status,
  expiresAt,
  createdAt,
  trial,
  autoRefresh,
}: LicenseProps) {
  return (
    <Card className="overflow-hidden transition-all hover:shadow-lg">
      <CardHeader className="space-y-1">
        <div className="flex items-center justify-between">
          <div className="text-xl font-semibold">{licenseType}</div>
          <div className="flex gap-2">
            {trial && <Badge variant="secondary">Trial</Badge>}
            <Badge variant={status === "active" ? "default" : "secondary"}>
              {status === "active" ? (
                <CheckCircle2 className="mr-1 h-3 w-3" />
              ) : null}
              {status}
            </Badge>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div
            className={cn(
              "flex items-center text-xs",
              autoRefresh ? "text-green-600" : "text-muted-foreground",
            )}
          >
            <RefreshCcw className="mr-1 h-3 w-3" />
            {autoRefresh ? "Auto-refresh enabled" : "Manual renewal"}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex h-[100px] items-center justify-center rounded-md bg-muted/50">
            <div className="text-4xl font-bold text-muted-foreground/30">
              AI
            </div>
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <Clock className="mr-2 h-4 w-4" />
            Created: {new Date(createdAt).toLocaleDateString()}
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <CalendarDays className="mr-2 h-4 w-4" />
            Expires: {new Date(expiresAt).toLocaleDateString()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
