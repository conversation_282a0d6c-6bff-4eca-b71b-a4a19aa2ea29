# Aiverything 开发文档

## 📋 目录
- [项目概述](#项目概述)
- [技术架构](#技术架构)
- [项目结构](#项目结构)
- [搜索流程详解](#搜索流程详解)
- [插件系统架构](#插件系统架构)
- [快捷键系统](#快捷键系统)
- [AI搜索架构](#ai搜索架构)
- [数据流转](#数据流转)
- [开发环境搭建](#开发环境搭建)
- [构建部署](#构建部署)
- [扩展开发](#扩展开发)

---

## 🌟 项目概述

**Aiverything** 是一个现代化的桌面文件搜索应用，采用多语言、多进程架构设计，实现高性能的文件索引和搜索功能。

### 核心特性
- **多进程架构**: React前端 + Tauri应用层 + File-Engine-Core搜索引擎 + Plugin-Service插件服务 + C++ DLL系统集成
- **GPU加速搜索**: File-Engine-Core集成GPU并行计算，提升搜索性能
- **AI集成**: 支持自然语言搜索和文档摘要
- **插件系统**: Plugin-Service提供完整的插件生态
- **实时索引**: File-Engine-Core负责文件系统监控和增量索引更新

---

## 🏗️ 技术架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 Frontend                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │ React + TS  │ │ Tauri View  │ │ Redux State Management  │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   应用层 Application                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │Tauri Rust  │ │Window Mgmt  │ │  System Integration     │ │
│  │ Backend    │ │             │ │                         │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    后端服务层 Backend Service               │
│  ┌─────────────────────────────┐ ┌─────────────────────────┐ │
│  │    File-Engine-Core.jar     │ │   Plugin-Service.jar    │ │
│  │      (Java 搜索引擎)         │ │     (Java 插件服务)      │ │
│  └─────────────────────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   系统层 System                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │C++ DLL     │ │GPU 加速模块  │ │  文件系统监控            │ │
│  │(快捷键监听) │ │             │ │                         │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈

#### 前端技术栈
- **React 18**: 用户界面构建
- **TypeScript**: 类型安全的JavaScript
- **Material-UI**: UI组件库
- **Redux Toolkit**: 状态管理
- **i18next**: 国际化支持
- **Vite**: 构建工具

#### 后端技术栈
- **Tauri**: Rust编写的跨平台应用框架
- **Rust**: 系统级编程语言，负责应用后端
- **Reqwest**: HTTP客户端库
- **Serde**: 序列化/反序列化

#### 后端服务
- **File-Engine-Core.jar**: Java编写的核心搜索引擎
  - 基于Lucene的全文搜索
  - GPU加速搜索算法
  - 文件索引和监控
  - SQLite数据库存储
- **Plugin-Service.jar**: Java编写的插件服务
  - 插件管理和加载
  - 插件API接口
  - 配置管理

#### 系统集成
- **Visual C++**: Windows系统集成
- **JNA**: Java原生接口
- **Windows API**: 系统级功能调用

---

## 📁 项目结构

```
aiverything/
├── src/                          # 前端源码
│   ├── api/                      # API接口层
│   │   ├── aiverything.ts        # 核心搜索API
│   │   ├── plugin.ts             # 插件系统API
│   │   └── system.ts             # 系统操作API
│   ├── Component/                # React组件
│   │   ├── MainAnwser/           # 主搜索界面
│   │   ├── FileList/             # 文件列表
│   │   ├── PluginList/           # 插件列表
│   │   └── ui/                   # 基础UI组件
│   ├── slices/                   # Redux状态切片
│   ├── i18n/                     # 国际化配置
│   └── utils/                    # 工具函数
├── src-tauri/                    # Tauri后端
│   ├── src/
│   │   ├── commands/             # Tauri命令处理
│   │   ├── api/                  # 后端API
│   │   ├── config/               # 配置管理
│   │   └── utils/                # 工具模块
│   ├── icons/                    # 应用图标
│   └── capabilities/             # 权限配置
├── C++/                          # C++ DLL模块
│   ├── getHandle/                # 窗口处理DLL
│   └── hotkeyListener/           # 快捷键监听DLL
├── core/                         # Java后端服务
│   ├── File-Engine-Core.jar     # 核心搜索引擎(独立Java服务)
│   ├── Plugin-Service.jar       # 插件服务(独立Java服务)
│   └── jre/                     # Java运行环境
└── docs/                         # 文档
```

### 关键文件说明

#### 前端核心文件
- `src/Component/MainAnwser/index.tsx`: 主搜索界面，处理搜索逻辑
- `src/api/aiverything.ts`: 核心API接口定义
- `src/store.ts`: Redux全局状态管理

#### 后端核心文件
- `src-tauri/src/main.rs`: Tauri应用入口点
- `src-tauri/src/commands/core_commands.rs`: 核心命令处理
- `src-tauri/src/api/lib.rs`: Java服务通信接口

#### C++ DLL模块
- `C++/hotkeyListener/`: 全局快捷键监听
- `C++/getHandle/`: 窗口句柄处理和系统集成

---

## 🔍 搜索流程详解

### 搜索架构流程

```
用户输入搜索关键词
         │
         ▼
   React UI 组件处理
         │
         ▼
   防抖处理 (200ms/400ms)
         │
         ▼
   调用 Tauri 命令
         │
         ▼
   Rust 后端转发请求
         │
         ▼
   File-Engine-Core.jar 处理搜索
         │
         ▼
   Lucene索引查询 + GPU加速
         │
         ▼
   返回搜索结果 UUID
         │
         ▼
   轮询获取搜索进度
         │
         ▼
   更新UI显示结果
```

### 搜索流程详细步骤

#### 1. 搜索触发
```typescript
// src/Component/MainAnwser/index.tsx
useLayoutEffect(() => {
  if (inputValue !== null && inputValue !== undefined && inputValue !== "") {
    // 立即设置loading状态
    setLoading(true);
    
    // 200ms后触发预搜索
    prepareTimeRef.current = setTimeout(() => {
      prepareSearch(inputValue, maxResultNum);
    }, 200);
    
    // 400ms后触发完整搜索
    timerRef.current = setTimeout(() => {
      searchAsync(inputValue, maxResultNum);
    }, 400);
  }
}, [inputValue]);
```

#### 2. 预搜索阶段
```rust
// src-tauri/src/commands/core_commands.rs
#[tauri::command]
pub async fn prepare_search(search_text: String, max_result_num: i32) -> Result<Value, String> {
    match aiverything_api.prepare_search(&search_text, max_result_num).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}
```

#### 3. 异步搜索阶段
```rust
#[tauri::command]
pub async fn search_async(search_text: String, max_result_num: i32) -> Result<Value, String> {
    match aiverything_api.search_async(&search_text, max_result_num).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}
```

#### 4. 结果轮询
```typescript
const fetchResultsUntilDone = async (uuid: string, retryTimes: number) => {
  const res = await getResult(uuid);
  const newData = transformCoreResponseToQueryResponse(res.data);
  setQueryData(newData);
  
  if (res.data.isDone || retryTimes === 0) {
    setLoading(false);
  } else {
    setTimeout(() => {
      fetchResultsUntilDone(uuid, retryTimes - 1);
    }, 100);
  }
};
```

### 搜索类型

#### 1. 普通文件搜索
- 直接输入关键词，搜索文件名
- 支持模糊匹配和精确匹配

#### 2. 高级搜索过滤器
```
格式: 关键词|过滤器
示例:
- test|f     # 只搜索文件
- test|d     # 只搜索目录
- test|case  # 区分大小写
- test|p     # 正则表达式
- test|c     # 内容搜索
```

#### 3. 缓存搜索
```
格式: :关键词
功能: 从最近使用的文件中搜索
```

#### 4. 插件搜索
```
格式: >插件名
功能: 搜索和启动插件
```

#### 5. AI搜索
```
格式: 自然语言描述
示例: "帮我找一下上周的工作报告"
```

---

## 🔌 插件系统架构

### 插件架构设计

```
前端插件界面
├── 插件列表 (PluginList)
├── 插件详情 (PluginDetail)
└── 插件页面 (PluginPage)
         │
         ▼
Tauri插件桥接
├── 插件命令 (plugin_commands.rs)
└── 插件API (plugin.ts)
         │
         ▼
Plugin-Service.jar
├── 插件管理器 (PluginManager)
├── 插件加载器 (PluginLoader)
└── 插件配置管理
         │
         ▼
插件实例
├── Actions插件
├── Terminal插件
└── 自定义插件
```

### 插件生命周期

#### 1. 插件发现
```typescript
// 输入 > 进入插件模式
if (inputValue.startsWith(">")) {
  const inputPluginName = inputValue.substring(1);
  fetchPluginInfoList(inputPluginName);
}
```

#### 2. 插件启动
```typescript
const onPluginClick = async (plugin: PluginInfo) => {
  if (plugin?.embeddedSupport) {
    // 嵌入式插件
    const shouldShowDirectly = await pluginEnter(plugin.identifier);
    setShowEmbeddedPage(shouldShowDirectly?.data);
    dispatch(setPluginInfo(plugin));
  } else {
    // 独立窗口插件
    const webview = new WebviewWindow(entryPageTitle, {
      url: plugin.entryPage,
      width: 800,
      height: 600,
    });
  }
};
```

---

## ⌨️ 快捷键系统

### 快捷键架构

```
C++ DLL层
├── hotkeyListener.dll
├── 快捷键监听 (Windows API)
└── 双击检测 (Double Click Detection)
         │
         ▼
Rust桥接层
├── hotkey_listener.rs
└── hotkey_commands.rs
         │
         ▼
前端响应层
├── 键盘事件处理
└── 快捷键配置
```

### 快捷键类型

#### 1. 全局快捷键
```rust
// src-tauri/src/main.rs
fn register_hotkey(app: &mut App, app_configs: Settings) -> Result<(), Box<dyn Error>> {
    let app_shortcut = Shortcut::new(Some(app_hotkey_config.modifiers), app_hotkey_config.key);
    app.global_shortcut().register(app_shortcut)?;
    Ok(())
}
```

#### 2. 双击快捷键
```cpp
// C++/hotkeyListener/hotkeyListener/dllmain.cpp
bool doubleClickCheck(const int vk, DoubleClickKeyStateSaver& saver) {
    bool ret = false;
    constexpr unsigned timeout = 120;
    if (isVirtualKeyPressed(vk)) {
        if (getTotalKeyPressedCount() == 2) {
            if (getCurrentMills() - saver.keyPressedTime < timeout && saver.isKeyReleasedAfterPress) {
                ret = true;
            }
        }
    }
    return ret;
}
```

#### 3. 文件操作快捷键
```typescript
// 搜索结果中的快捷键处理
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === "Enter" && selectedFile) {
    if (isCopyPathPressed) {
      navigator.clipboard.writeText(selectedFile.path);
    } else if (isOpenParentFolderPressed) {
      showItemInFolder(selectedFile.path);
    } else if (isOpenWithAdminPressed) {
      openFileWithAdmin(selectedFile.path);
    } else {
      openFileWithoutAdmin(selectedFile.path);
    }
  }
};
```

---

## 🤖 AI搜索架构

### AI集成架构

```
前端AI界面
├── AI搜索模式切换
├── 自然语言输入
└── AI结果展示
         │
         ▼
Tauri AI桥接
├── AI搜索命令
└── 文档摘要命令
         │
         ▼
Java AI服务
├── AI搜索引擎
├── 文档分析器
└── LLM接口
         │
         ▼
外部AI服务
├── Ollama服务
└── 其他LLM服务
```

### AI搜索流程

#### 1. AI模式切换
```typescript
// AI模式下的搜索逻辑
if (aiMode) {
  if (enterKeyState && aiSearchState !== 1) {
    searchAIAndShowResult(inputValue, maxResultNum);
  }
  return;
}
```

#### 2. AI搜索执行
```typescript
const searchAIAndShowResult = (searchText: string, maxResultNum: number) => {
  setLoading(true);
  setAiSearchState(1);
  searchAI(searchText, maxResultNum)
    .then((res) => res.data)
    .then((res) => {
      const newData = transformCoreResponseToQueryResponse(res);
      setQueryData(newData);
      setLoading(false);
      setAiSearchState(2);
    });
};
```

#### 3. 文档AI摘要
```typescript
// 文档摘要功能
export async function getSummaryStream(sessionId: string): Promise<EventSource> {
  const url = `${baseUrl}/summaryStream?sessionId=${sessionId}`;
  return new EventSource(url, { withCredentials: false });
}
```

---

## 🔄 数据流转

### 状态管理架构

```
Redux Store
├── inputValueSlice - 搜索输入
├── pluginInfoSlice - 插件信息
├── aiModeSlice - AI模式状态
└── windowVibrancySlice - 窗口效果
         │
         ▼
React组件
├── MainAnswer - 主搜索
├── FileList - 文件列表
├── PluginList - 插件列表
└── SettingWindow - 设置窗口
         │
         ▼
API层
├── aiverything.ts - 核心API
├── plugin.ts - 插件API
└── system.ts - 系统API
```

### 数据流转过程

#### 1. 搜索输入处理
```typescript
// 输入值变化触发搜索
const dispatch = useDispatch();
const inputValue = useSelector(selectInputValue);

// 输入处理
const handleInputChange = (value: string) => {
  dispatch(setInputValue(value));
};
```

#### 2. 搜索结果处理
```typescript
// 搜索结果数据转换
const transformCoreResponseToQueryResponse = (coreResponse: AiverthingCoreResponse): QueryResponse[] => {
  const queryResponses: QueryResponse[] = [];
  coreResponse?.data?.forEach((eachDataTypeResult) => {
    const dataArray = eachDataTypeResult.results.map((eachCoreData) =>
      transformCoreDataToData(eachCoreData, dataType)
    );
    queryResponses.push({
      datatype: dataType,
      data: dataArray,
    });
  });
  return queryResponses;
};
```

---

## 🛠️ 开发环境搭建

### 环境要求

#### 系统要求
- **操作系统**: Windows 10/11 (64位)
- **Node.js**: 18.0+ 
- **Rust**: 1.70+
- **Java**: JDK 21+
- **Visual Studio**: Build Tools 2019+

#### 开发工具
- **IDE**: VS Code / WebStorm / IntelliJ IDEA
- **插件**: Rust Analyzer, Tauri, TypeScript

### 安装步骤
请参考Github Actions workflow `build.yml`内的步骤执行

### 开发命令
```bash
# 启动Tauri开发模式
yarn tauri dev

# 构建前端
yarn tauri build
```

---

## 📚 参考资料

### 官方文档
- [Tauri 官方文档](https://tauri.app/)
- [React 官方文档](https://reactjs.org/)
- [Rust 官方文档](https://doc.rust-lang.org/)

### 技术规范
- [TypeScript 规范](https://www.typescriptlang.org/)
- [Material-UI 设计规范](https://mui.com/)
- [Redux Toolkit 最佳实践](https://redux-toolkit.js.org/)

### 开源项目
- [File-Engine](https://github.com/XUANXUQAQ/File-Engine)
- [Tauri Examples](https://github.com/tauri-apps/tauri/tree/dev/examples)

---

*本文档将随着项目发展持续更新，如有问题请提交Issue或联系开发团队。* 