use std::fs;

use schannel::{
    cert_context::CertContext,
    cert_store::{CertAdd, CertStore},
};

pub fn import_certificate(cert_path: &str) -> Result<(), String> {
    // 读取证书文件
    let cert_data =
        fs::read_to_string(cert_path).map_err(|e| format!("Failed to read certificate: {}", e))?;

    let root_store_result = CertStore::open_local_machine("Root");
    match root_store_result {
        Ok(mut root_store) => {
            let cert_context_res = CertContext::from_pem(&cert_data);
            match cert_context_res {
                Ok(cert_context) => {
                    let import_ret = root_store
                        .add_cert(&cert_context, CertAdd::ReplaceExistingInheritProperties);
                    match import_ret {
                        Ok(_) => Ok(()),
                        Err(e) => {
                            return Err(format!(
                                "Failed to import certificate to Root, <PERSON>rror {}",
                                e
                            ));
                        }
                    }
                }
                Err(e) => Err(format!("Failed to generate cert context, Error: {}", e)),
            }
        }
        Err(e) => {
            return Err(format!("Failed to open Root cert store, Error: {}", e));
        }
    }
}
