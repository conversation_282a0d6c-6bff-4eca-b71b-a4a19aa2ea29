use std::collections::HashSet;
use std::fs::File;
use std::io::{self, <PERSON><PERSON><PERSON><PERSON>, <PERSON>uf<PERSON><PERSON><PERSON>, Write};
use std::path::Path;

/// Trims whitespace from both ends of a string.
fn trim(s: &str) -> &str {
    s.trim()
}

/// Checks if a file exists at the given path.
fn is_file_exist<P: AsRef<Path>>(path: P) -> bool {
    path.as_ref().exists()
}

/// Initializes JVM parameters by combining default and custom parameters.
fn init_jvm_parameters(
    default_jvm_params: &Vec<String>,
    jvm_parameter_file_path: &str,
) -> io::Result<Vec<String>> {
    // 1. Parse default JVM parameters
    let default_jvm_parameters_set: HashSet<String> = default_jvm_params.iter().cloned().collect();

    // 2. Read custom JVM parameters from file
    let mut custom_vm_options = Vec::new();
    let mut is_all_default = true;

    if is_file_exist(jvm_parameter_file_path) {
        let file = File::open(jvm_parameter_file_path)?;
        let reader = BufReader::new(file);

        for line_result in reader.lines() {
            let line = line_result?;
            let vm_option = trim(&line);

            if !vm_option.is_empty() {
                if is_all_default && !default_jvm_parameters_set.contains(vm_option) {
                    is_all_default = false;
                }
                custom_vm_options.push(vm_option.to_string());
            }
        }
    }

    // 3. Construct the final JVM parameter string
    let vm_option_str = if is_all_default {
        default_jvm_params
    } else {
        &custom_vm_options
    };

    // 4. Write the final JVM parameters back to the file, each on a new line
    let mut output_stream = File::create(jvm_parameter_file_path)?;
    for param in vm_option_str {
        writeln!(output_stream, "{}", param)?;
    }

    // 5. Return the final JVM parameter string
    Ok(vm_option_str.to_owned())
}

pub fn init_default_jvm_parameters(jvm_parameter_file_path: &str) -> io::Result<Vec<String>> {
    let default_jvm_params = vec![
        "-Xms16M".to_string(),
        "-Xmx512M".to_string(),
        "-XX:MaxHeapFreeRatio=20".to_string(),
        "-XX:MinHeapFreeRatio=10".to_string(),
        "-XX:+CompactStrings".to_string(),
        "-XX:+HeapDumpOnOutOfMemoryError".to_string(),
        "-DFile_Engine_Check_Parent_Process=true".to_string(),
    ];

    // Initialize JVM parameters
    match init_jvm_parameters(&default_jvm_params, jvm_parameter_file_path) {
        Ok(final_params) => Ok(final_params),
        Err(e) => Err(e),
    }
}
