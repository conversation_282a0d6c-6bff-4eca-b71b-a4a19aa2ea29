# API 参考文档

## 插件基类方法

### 生命周期方法

#### `load(Map<String, Object> config)`
- **描述**: 插件加载时调用
- **参数**: 
  - `config`: 来自 `settings.json` 的配置映射
- **返回值**: 无
- **示例**:
```java
@Override
public void load(Map<String, Object> config) {
    System.out.println("插件配置: " + config);
    // 初始化插件逻辑
}
```

#### `afterLoaded()`
- **描述**: 当所有插件load方法被调用完成后该API被调用，如果插件依赖于其他的插件，可以在这里使用`checkPlugin`进行兼容性检查
- **参数**: 无
- **返回值**: 无
- **示例**:
```java
@Override
public void afterLoaded() {
    System.out.println("所有插件已加载完成");
}
```

#### `unload()`
- **描述**: 插件卸载时调用，用于清理资源
- **参数**: 无
- **返回值**: 无
- **示例**:
```java
@Override
public void unload() {
    // 清理资源
    System.out.println("插件正在卸载...");
}
```

#### `pluginEnter()`
- **描述**: 插件在前端被选中，进入插件页面时调用，返回值代表是否在进入插件时直接显示插件embedded界面
- **参数**: 无
- **返回值**: `boolean` - 返回 `true` 表示进入插件模式后直接展示插件embedded界面，否则只会显示搜索框，直到插件进行输入后才会进行展示
- **示例**:
```java
@Override
public boolean pluginEnter() {
    System.out.println("插件已激活");
    return true;
}
```

#### `pluginExit()`
- **描述**: 搜索框退出插件模式时调用
- **参数**: 无
- **返回值**: 无
- **示例**:
```java
@Override
public void pluginExit() {
    System.out.println("插件已退出");
}
```

#### `configsChanged(Map<String, Object> config)`
- **描述**: 插件配置发生变更时调用，当在插件设置页面进行设置的修改时调用，设置界面的修改会自动保存，不管修改任何配置都会立刻调用该API
- **参数**: 
  - `config`: 新的配置映射
- **返回值**: 无
- **示例**:
```java
@Override
public void configsChanged(Map<String, Object> config) {
    System.out.println("配置已更新: " + config);
    // 应用新配置
}
```

#### `isHotSwappable()`
- **描述**: 指示插件是否支持热插拔，如果你的插件有特殊需求或者限制可以设置为返回false
- **参数**: 无
- **返回值**: `boolean` - 返回 `true` 表示支持热插拔
- **示例**:
```java
@Override
public boolean isHotSwappable() {
    return true; // 支持热插拔
}
```

## 插件sdk内置函数

### 命令处理

#### `registerCommandHandler(String commandName, Function<Map<String, Object>, Object> handler)`
- **描述**: 注册命令处理器，注册后可以通过`invokeCommand`函数被其他插件调用，也可以插件内部使用`invokeCommand`自己进行调用。   
前端可以通过POST方法调用   
```javascript
const baseUrl = process.env.NODE_ENV === "development" ? 
"https://localhost:38884/plugin/invoke" : 
`${window.location.origin}/plugin/invoke`

async executeCommand() {
    const urlParams = new URLSearchParams();
    urlParams.set("pluginIdentifier", "examplePlugin");
    urlParams.set("command", "exampleCommand");
    const fullUrl = `${baseUrl}?${urlParams.toString()}`;
    const response = await fetch(fullUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.json();
  }
```
- **参数**: 
  - `commandName`: 命令名称
  - `handler`: 处理函数，接收参数映射并返回结果
- **返回值**: 无
- **示例**:
```java
registerCommandHandler("myCommand", paramsMap -> {
    String param = (String) paramsMap.get("param");
    return "处理结果: " + param;
});
```

#### `removeCommandHandler(String command)`
- **描述**: 移除已注册的命令处理器
- **参数**: 
  - `command`: 要移除的命令名称
- **返回值**: 无
- **示例**:
```java
removeCommandHandler("myCommand");
```

#### `checkPlugin(String pluginIdentifier)`
- **描述**: 检查指定插件是否存在，如果插件依赖于其他插件，可以在`afterLoaded()`API中调用该函数进行兼容性检查
- **参数**: 
  - `pluginIdentifier`: 插件标识符
- **返回值**: `boolean` - 插件是否存在
- **示例**:
```java
if (checkPlugin("anotherPlugin")) {
    System.out.println("插件存在");
}
```

#### `checkPlugin(String pluginIdentifier, String version)`
- **描述**: 检查指定版本的插件是否存在，如果插件依赖于其他插件，可以在`afterLoaded()`API中调用该函数进行兼容性检查
- **参数**: 
  - `pluginIdentifier`: 插件标识符
  - `version`: 插件版本，遵循semver规则
- **返回值**: `boolean` - 指定版本的插件是否存在
- **示例**:
```java
if (checkPlugin("anotherPlugin", "1.0.0")) {
    System.out.println("指定版本的插件存在");
}
```

#### `checkPluginCommand(String pluginIdentifier, String command)`
- **描述**: 检查插件是否有指定命令，如果插件依赖于其他插件，可以在`afterLoaded()`API中调用该函数进行兼容性检查
- **参数**: 
  - `pluginIdentifier`: 插件标识符
  - `command`: 命令名称
- **返回值**: `boolean` - 插件是否有该命令
- **示例**:
```java
if (checkPluginCommand("anotherPlugin", "someCommand")) {
    System.out.println("插件有该命令");
}
```

#### `checkPluginCommand(String pluginIdentifier, String version, String command)`
- **描述**: 检查指定版本的插件是否有指定命令，如果插件依赖于其他插件，可以在`afterLoaded()`API中调用该函数进行兼容性检查
- **参数**: 
  - `pluginIdentifier`: 插件标识符
  - `version`: 插件版本
  - `command`: 命令名称
- **返回值**: `boolean` - 指定版本的插件是否有该命令
- **示例**:
```java
if (checkPluginCommand("anotherPlugin", "1.0.0", "someCommand")) {
    System.out.println("指定版本的插件有该命令");
}
```

#### `invokeCommand(String command, Map<String, Object> args, Consumer<Optional<T>> returnValueHandler)`
- **描述**: 调用本插件的命令,注册命令请调用上方`registerCommandHandler()`
- **参数**: 
  - `command`: 命令名称
  - `args`: 命令参数
  - `returnValueHandler`: 返回值处理器
- **返回值**: 无（通过回调处理返回值）
- **示例**:
```java
Map<String, Object> params = new HashMap<>();
params.put("param1", "value1");
invokeCommand("myCommand", params, result -> {
    result.ifPresent(value -> System.out.println("结果: " + value));
});
```

#### `invokeCommand(String pluginIdentifier, String command, Map<String, Object> args, Consumer<Optional<T>> returnValueHandler)`
- **描述**: 调用其他插件注册的命令
- **参数**: 
  - `pluginIdentifier`: 目标插件标识符
  - `command`: 命令名称
  - `args`: 命令参数，禁止传入null，如果不需要参数请传入`Collections.emptyMap()`
  - `returnValueHandler`: 返回值处理器
- **返回值**: 无（通过回调处理返回值）
- **示例**:
```java
Map<String, Object> params = new HashMap<>();
params.put("param1", "value1");
invokeCommand("anotherPlugin", "someCommand", params, result -> {
    result.ifPresent(value -> System.out.println("结果: " + value));
});
```

### 聊天功能

#### `getOllamaConfig()`
- **描述**: 获取Ollama配置
- **返回值**: `Optional<OllamaConfig>` - Ollama配置对象
- **示例**:
```java
Optional<OllamaConfig> config = getOllamaConfig();
config.ifPresent(c -> System.out.println("Ollama配置: " + c));
```

#### `openNewChatSession()`
- **描述**: 打开新的聊天会话
- **返回值**: `Optional<String>` - 会话ID
- **示例**:
```java
Optional<String> sessionId = openNewChatSession();
sessionId.ifPresent(id -> System.out.println("新会话ID: " + id));
```

#### `closeSession(String sessionId)`
- **描述**: 关闭聊天会话
- **参数**: 
  - `sessionId`: 会话ID
- **返回值**: 无
- **示例**:
```java
closeSession("session-123");
```

#### `saveSession(String name, String sessionId)`
- **描述**: 保存聊天会话
- **参数**: 
  - `name`: 会话名称
  - `sessionId`: 会话ID
- **返回值**: 无
- **示例**:
```java
saveSession("重要对话", "session-123");
```

#### `deleteSession(String name, String sessionId)`
- **描述**: 删除聊天会话
- **参数**: 
  - `name`: 会话名称
  - `sessionId`: 会话ID
- **返回值**: 无
- **示例**:
```java
deleteSession("旧对话", "session-123");
```

#### `loadSession(String name)`
- **描述**: 加载聊天会话
- **参数**: 
  - `name`: 会话名称
- **返回值**: `String` - 会话ID
- **示例**:
```java
String sessionId = loadSession("重要对话");
```

#### `addCustomRole(String roleName)`
- **描述**: 添加自定义角色
- **参数**: 
  - `roleName`: 角色名称
- **返回值**: 无
- **示例**:
```java
addCustomRole("专家助手");
```

#### `chat(String sessionId, String message)`
- **描述**: 同步聊天
- **参数**: 
  - `sessionId`: 会话ID
  - `message`: 消息内容
- **返回值**: `Optional<OllamaChatResponseModel>` - 聊天响应
- **示例**:
```java
Optional<OllamaChatResponseModel> response = chat("session-123", "你好");
response.ifPresent(r -> System.out.println("回复: " + r.getMessage()));
```

#### `chatMCP(String sessionId, String message)`
- **描述**: 使用MCP协议聊天
- **参数**: 
  - `sessionId`: 会话ID
  - `message`: 消息内容
- **返回值**: `Optional<String>` - 聊天响应字符串
- **示例**:
```java
Optional<String> response = chatMCP("session-123", "你好");
response.ifPresent(r -> System.out.println("MCP回复: " + r));
```

#### `chatAsync(String sessionId, String message, OllamaStreamHandler streamHandler, Consumer<OllamaChatResponseModel> callback)`
- **描述**: 异步聊天（流式响应）
- **参数**: 
  - `sessionId`: 会话ID
  - `message`: 消息内容
  - `streamHandler`: 流处理器
  - `callback`: 完成回调
- **返回值**: 无
- **示例**:
```java
chatAsync("session-123", "写一首诗", 
    new OllamaStreamHandler() {
        @Override
        public void accept(String chunk) {
            System.out.print(chunk);
        }
    },
    response -> System.out.println("\n聊天完成")
);
```

#### `chatAsync(String sessionId, OllamaChatRequest ollamaChatRequest, OllamaStreamHandler streamHandler, Consumer<OllamaChatResponseModel> callback)`
- **描述**: 异步聊天（自定义请求）
- **参数**: 
  - `sessionId`: 会话ID
  - `ollamaChatRequest`: 聊天请求对象
  - `streamHandler`: 流处理器
  - `callback`: 完成回调
- **返回值**: 无
- **示例**:
```java
OllamaChatRequest request = new OllamaChatRequest();
request.setMessage("你好");
chatAsync("session-123", request, streamHandler, callback);
```

#### `getChatHistory(String sessionId)`
- **描述**: 获取聊天历史
- **参数**: 
  - `sessionId`: 会话ID
- **返回值**: `Optional<List<OllamaChatMessage>>` - 聊天消息列表
- **示例**:
```java
Optional<List<OllamaChatMessage>> history = getChatHistory("session-123");
history.ifPresent(messages -> {
    messages.forEach(msg -> System.out.println(msg.getContent()));
});
```

#### `generateWithTools(String message)`
- **描述**: 使用工具生成内容
- **参数**: 
  - `message`: 消息内容
- **返回值**: `Optional<OllamaToolsResult>` - 工具生成结果
- **示例**:
```java
Optional<OllamaToolsResult> result = generateWithTools("帮我计算1+1");
result.ifPresent(r -> System.out.println("工具结果: " + r));
```

#### `generateWithImageBytes(String message, List<byte[]> images)`
- **描述**: 使用图片字节数组生成内容
- **参数**: 
  - `message`: 消息内容
  - `images`: 图片字节数组列表
- **返回值**: `Optional<OllamaResult>` - 生成结果
- **示例**:
```java
List<byte[]> imageBytes = Arrays.asList(imageData);
Optional<OllamaResult> result = generateWithImageBytes("描述这张图片", imageBytes);
```

#### `generateWithImage(String message, List<File> images)`
- **描述**: 使用图片文件生成内容
- **参数**: 
  - `message`: 消息内容
  - `images`: 图片文件列表
- **返回值**: `Optional<OllamaResult>` - 生成结果
- **示例**:
```java
List<File> imageFiles = Arrays.asList(new File("image.jpg"));
Optional<OllamaResult> result = generateWithImage("分析这张图片", imageFiles);
```

#### `registerApi(String apiName, String apiFunctionName, String apiDescription, String apiVersion, List<Args> args, Function<Map<String, Object>, String> func)`
- **描述**: 注册API工具函数供AI调用，实验性功能，谨慎使用
- **参数**: 
  - `apiName`: API名称
  - `apiFunctionName`: API函数名称
  - `apiDescription`: API描述
  - `apiVersion`: API版本
  - `args`: 参数定义列表
  - `func`: 函数实现
- **返回值**: 无
- **示例**:
```java
List<Args> args = Arrays.asList(
    new Args("query", "string", "搜索关键词")
);
registerApi("搜索工具", "search", "搜索互联网内容", "1.0", args, params -> {
    String query = (String) params.get("query");
    return "搜索结果: " + query;
});
```

### 基础配置管理
#### 配置类型说明

#### 基本类型

| 类型 | 描述 | 默认值示例 | 枚举支持 |
|------|------|------------|----------|
| `string` | 字符串 | `"默认文本"` | ✅ |
| `number` | 数字 | `123` | ✅ |
| `boolean` | 布尔值 | `true` | ✅ |

#### 复合类型

| 类型 | 描述 | 默认值示例 |
|------|------|------------|
| `object` | 对象 | `Map<String, ConfigComponent>` |
| `array` | 数组 | `List<ConfigComponent>` |

#### `getConfig(String configPath)`
- **描述**: 获取配置值
- **参数**: 
  - `configPath`: 配置路径，支持点分割的嵌套路径
- **返回值**: `Optional<Object>` - 配置值的 Optional 包装，Object可以被替换为泛型参数。**注意：泛型转换并没有自动类型映射的功能，因此如果是复杂配置将会被转换为Map或者List**
- **示例**:
```java
Optional<Object> config = getConfig("configName1");
Optional<Object> nestedConfig = getConfig("config.nested.value");
```

#### `getRawConfig(String configEntry, Locale locale)`
- **描述**: 获取原始配置值，即带有`type`，`description`和`defaultValue`的ConfigComponent对象。如果获取的是顶层配置，即直接传入空字符串，则返回的是Map类型，包含所有配置对象
- **参数**: 
  - `configEntry`: 配置路径
  - `locale`: 语言环境
- **返回值**: `Optional<T>` - 原始配置值
- **示例**:
```java
Optional<ConfigComponent> config = getRawConfig("configName", Locale.CHINESE);
Optional<Map<String, ConfigComponent>> topConfig = getRawConfig("", Locale.CHINESE);
```

### 高级配置管理

#### 配置对象生成

#### `generateConfigObject(String type, String description, Object defaultValue, Object currentValue, List<Object> enumValues, List<String> enumDescriptions)`
- **描述**: 核心函数，生成配置组件对象
- **参数**: 
  - `type`: 配置类型 (`"string"`, `"number"`, `"boolean"`, `"object"`, `"array"`)
  - `description`: 配置描述
  - `defaultValue`: 默认值
  - `currentValue`: 当前值，可为 `null`
  - `enumValues`: 枚举值列表，可为 `null`
  - `enumDescriptions`: 枚举描述列表，可为 `null`
- **返回值**: `ConfigEntity.ConfigComponent` - 配置组件对象
- **示例**:

**字符串类型**:
```java
ConfigEntity.ConfigComponent stringConfig = generateConfigObject(
    "string", "字符串配置", "默认值", null, null, null
);
```

**数字类型**:
```java
List<Object> enumValues = Arrays.asList(1, 2, 3, 4, 5);
List<String> enumDescs = Arrays.asList("一", "二", "三", "四", "五");
ConfigEntity.ConfigComponent numberConfig = generateConfigObject(
    "number", "数字配置", 1, null, enumValues, enumDescs
);
```

**布尔类型**:
```java
List<Object> boolEnum = Arrays.asList(true, false);
List<String> boolDescs = Arrays.asList("是", "否");
ConfigEntity.ConfigComponent boolConfig = generateConfigObject(
    "boolean", "布尔配置", true, null, boolEnum, boolDescs
);
```

**对象类型，每一个配置都需要使用ConfigComponent进行封装，再放入Map中**:
```java
Map<String, Object> objectValue = new LinkedHashMap<>();
// 上方生成的stringConfig，必须为ConfigComponent类型
objectValue.put("property1", stringConfig);
// 上方生成的numberConfig，必须为ConfigComponent类型
objectValue.put("property2", numberConfig);
// Object类型配置被封装为ConfigComponent类型
ConfigEntity.ConfigComponent objectConfig = generateConfigObject(
    "object", "对象配置", objectValue, null, null, null
);
```

**数组类型，每一个配置都需要使用ConfigComponent进行封装，再放入List中**:
```java
List<ConfigEntity.ConfigComponent> arrayValue = new ArrayList<>();
// 上方生成的stringConfig，必须为ConfigComponent类型
arrayValue.add(stringConfig);
// 上方生成的numberConfig，必须为ConfigComponent类型
arrayValue.add(numberConfig);
// Array类型配置被封装为ConfigComponent类型
ConfigEntity.ConfigComponent arrayConfig = generateConfigObject(
    "array", "数组配置", arrayValue, null, null, null
);
```
### 所有的配置，即使是一个数字也需要转换为ConfigComponent进行存储
**Array和Object类型的配置也可以互相嵌套，只需要保证一点，Map的每一个key都是String，value为ConfigComponent类型；List的每一个元素必须为ConfigComponent类型**

#### `convertToConfigObject(Object object)`
- **描述**: 将普通对象转换为配置组件对象，如果你需要存放复杂对象配置，但是不想手动使用`generateConfigObject`将每一个元素都转换成`ConfigComponent`类型，则可以调用该函数，该API将会自动递归将内部所有的元素都转换为ConfigComponent并返回直接使用到`addConfig()`以及其他配置API中。元素的type将会自动检测，description为`Auto generated config`。
- **参数**: 
  - `object`: 要转换的对象
- **返回值**: `ConfigEntity.ConfigComponent` - 配置组件对象
- **示例**:
```java
String value = "测试值";
ConfigEntity.ConfigComponent configObj = convertToConfigObject(value);
```

#### `setConfig(String configPath, ConfigEntity.ConfigComponent value)`
- **描述**: 设置配置值
- **参数**: 
  - `configPath`: 配置路径
  - `value`: 配置组件对象
- **返回值**: 无
- **示例**:
```java
ConfigEntity.ConfigComponent newConfig = generateConfigObject(
    "string", "描述", "新值", null, null, null
);
setConfig("configName1", newConfig);
```

#### `removeConfig(String configPath)`
- **描述**: 删除配置项
- **参数**: 
  - `configPath`: 配置路径，支持数组索引格式如 `array[0]`
- **返回值**: 无
- **示例**:
```java
removeConfig("configName1");
removeConfig("array[0]"); // 删除数组第一个元素
removeConfig("object.property"); // 删除对象属性
```

#### `addConfig(String parentPath, String key, ConfigEntity.ConfigComponent value)`
- **描述**: 向指定路径添加新的配置项，不常用，可使用下方的`addConfigToObject()`以及`addConfigToArray()`
- **参数**: 
  - `parentPath`: 父配置路径
  - `key`: 新配置项的键名
  - `value`: 配置组件对象
- **返回值**: 无
- **示例**:
```java
ConfigEntity.ConfigComponent newItem = generateConfigObject(
    "boolean", "新项", true, null, null, null
);
addConfig("parentConfig", "newKey", newItem);
```

#### `addConfigToObject(String parentConfigEntry, String newConfigEntry, ConfigEntity.ConfigComponent configObject)`
- **描述**: 向对象类型配置添加新的配置项
- **参数**: 
  - `parentConfigEntry`: 父配置路径
  - `newConfigEntry`: 新配置项键名
  - `configObject`: 配置组件对象
- **返回值**: 无
- **示例**:
```java
ConfigEntity.ConfigComponent newConfig = generateConfigObject(
    "string", "新配置", "默认值", null, null, null
);
addConfigToObject("parentConfig", "newKey", newConfig);
```

#### `addConfigToArray(String parentConfigEntry, ConfigEntity.ConfigComponent configObject)`
- **描述**: 向数组类型配置添加新元素
- **参数**: 
  - `parentConfigEntry`: 父配置路径
  - `configObject`: 配置组件对象
- **返回值**: 无
- **示例**:
```java
ConfigEntity.ConfigComponent newElement = generateConfigObject(
    "string", "新元素", "值", null, null, null
);
addConfigToArray("arrayConfig", newElement);
```

## 配置路径语法

### 基本路径
```java
getConfig("configName");           // 获取顶级配置
```

### 嵌套路径
```java
getConfig("parent.child");         // 获取嵌套对象属性
getConfig("parent.child.grandchild"); // 深层嵌套
```

### 数组索引
```java
getConfig("arrayConfig[0]");       // 获取数组第一个元素
getConfig("parent.array[1].property"); // 复杂路径
```

## 错误处理

所有配置操作都应该进行适当的错误处理：

```java
try {
    Optional<Object> config = getConfig("configName");
    if (config.isPresent()) {
        // 处理配置值
        Object value = config.get();
    } else {
        System.out.println("配置项不存在");
    }
} catch (Exception e) {
    System.err.println("获取配置失败: " + e.getMessage());
}
```

## 最佳实践

### 1. 配置验证
```java
@Override
public void load(Map<String, Object> config) {
    // 验证必需的配置项
    if (!config.containsKey("requiredConfig")) {
        throw new IllegalArgumentException("缺少必需的配置项: requiredConfig");
    }
}
```

### 2. 类型安全
```java
Optional<Object> config = getConfig("stringConfig");
if (config.isPresent() && config.get() instanceof String) {
    String stringValue = (String) config.get();
    // 使用字符串值
}
```

### 3. 默认值处理
```java
String value = (String) getConfig("optionalConfig").orElse("默认值");
```

## 注意事项

1. **配置对象必须使用 `generateConfigObject` 创建**
2. **配置路径区分大小写**
3. **数组索引从 0 开始**
4. **删除配置项前确保路径存在**
5. **配置变更会触发 `configsChanged` 方法** 