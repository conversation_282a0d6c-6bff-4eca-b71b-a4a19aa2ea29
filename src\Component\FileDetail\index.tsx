import { DataType } from "../MainAnwser";
import FileIcon from "../FileIcon";
import { open } from "@tauri-apps/plugin-shell";
import { useSelector } from "react-redux";
import { selectInputValue } from "../../slices/inputValueSlice";
import FileDetailTable from "../FileDetailTable";
import FileDetailAction from "../FileDetailAction";
import { useTranslation } from "react-i18next";
import { useState, useEffect, useRef, useMemo } from "react";
import {
  IconButton,
  Box,
  Tooltip,
  Skeleton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Alert,
} from "@mui/material";
import {
  summarizeAI,
  removeSummary,
  getSummaryStream,
  readAppConfig,
} from "../../api/aiverything";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { motion, AnimatePresence } from "framer-motion";
import SmartToyIcon from "@mui/icons-material/SmartToy";
import { ChevronLeftIcon } from "lucide-react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

// 添加一个 Map 来跟踪每个文件的生成状态
const generatingFiles = new Map<
  string,
  { sessionId: string; isGenerating: boolean; summary: string }
>();

interface SummaryFileStatus {
  token: string;
  done: boolean;
}

const FileDetail = ({ file }) => {
  const inputValue = useSelector(selectInputValue);
  const { t } = useTranslation();
  const [showAISummary, setShowAISummary] = useState(false);
  const [summary, setSummary] = useState("");
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);
  const activeFileRef = useRef<string>("");
  const [thinkingProcess, setThinkingProcess] = useState("");

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0,
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 300 : -300,
      opacity: 0,
    }),
  };

  const [[page, direction], setPage] = useState([0, 0]);

  const startSummary = async (filePath: string) => {
    // 检查文件是否正在生成中
    const generatingStatus = filePath
      ? generatingFiles.get(filePath)
      : undefined;
    if (generatingStatus?.isGenerating) {
      setSessionId(generatingStatus.sessionId);
      setIsGenerating(generatingStatus.isGenerating);
      setSummary(generatingStatus.summary);
      return;
    }

    setLoading(true);
    try {
      const result = await summarizeAI(filePath);
      const newSessionId = result.data;

      // 如果返回的 sessionId 为空，直接设置为失败状态
      if (!newSessionId) {
        setIsGenerating(false);
        setSummary("");
        return;
      }

      // 记录生成状态
      generatingFiles.set(filePath, {
        sessionId: newSessionId,
        isGenerating: true,
        summary: "",
      });
      setSessionId(newSessionId);
      setIsGenerating(true);
      setSummary("");
    } catch (error) {
      console.error("Start AI summarization failed:", error);
      setIsGenerating(false);
      setSummary("");
    } finally {
      setLoading(false);
    }
  };

  const fetchSummaryStream = async (sessionId: string, filePath: string) => {
    try {
      // 创建 EventSource 连接
      const eventSource = await getSummaryStream(sessionId);

      // 设置超时检查
      const timeoutId = setTimeout(() => {
        console.warn("Summary generation timeout after 10 minutes");
        eventSource.close();
        setIsGenerating(false);
        generatingFiles.delete(filePath);
      }, 600000); // 10分钟超时

      // 处理消息事件
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          const currentStatus = generatingFiles.get(activeFileRef.current);

          if (data.done) {
            // 摘要生成完成
            clearTimeout(timeoutId);
            eventSource.close();
            setIsGenerating(false);
            generatingFiles.set(filePath, {
              ...generatingFiles.get(filePath),
              isGenerating: false,
            });
          } else {
            // 更新摘要内容
            const newSummary = data.token;
            generatingFiles.set(filePath, {
              ...generatingFiles.get(filePath),
              summary: newSummary,
            });

            if (currentStatus?.sessionId === sessionId) {
              setSummary(newSummary);
            }
          }
        } catch (error) {
          console.error("Error parsing SSE message:", error);
        }
      };

      // 处理错误
      eventSource.onerror = (error) => {
        console.error("SSE connection error:", error);
        clearTimeout(timeoutId);
        eventSource.close();
        setIsGenerating(false);
        generatingFiles.delete(filePath);
      };

      // 处理连接打开
      eventSource.onopen = () => {
        console.log("SSE connection opened for summary generation");
      };
    } catch (error) {
      console.error("Failed to establish SSE connection:", error);
      setIsGenerating(false);
      generatingFiles.delete(filePath);
    }
  };

  const paginate = async (newDirection: number) => {
    const newPage = newDirection;

    if (newDirection > 0 && !showAISummary) {
      const status = generatingFiles.get(file.path);
      if (!status) {
        await startSummary(file.path);
      }
    }
    setPage([newPage, newDirection]);
    setShowAISummary(!showAISummary);
  };

  useEffect(() => {
    if (isGenerating && sessionId && file?.path) {
      // 确保只获取当前文件的摘要
      const currentStatus = generatingFiles.get(file.path);
      if (currentStatus?.sessionId === sessionId) {
        fetchSummaryStream(sessionId, file.path);
      }
    }
  }, [isGenerating, sessionId, file]);

  useEffect(() => {
    // 如果文件路径发生变化，则重置状态
    if (file?.path !== activeFileRef.current) {
      setShowAISummary(false);
    }

    // 检查文件是否正在生成中
    const generatingStatus = file?.path
      ? generatingFiles.get(file.path)
      : undefined;
    if (generatingStatus) {
      setSessionId(generatingStatus.sessionId);
      setIsGenerating(generatingStatus.isGenerating);
      setSummary(generatingStatus.summary);
    } else {
      setIsGenerating(false);
      setSessionId("");
      setSummary("");
    }
    if (file?.path) {
      activeFileRef.current = file.path;
    }
  }, [file]);

  useEffect(() => {
    return () => {
      // 输入变化时清理所有状态
      for (const [_, status] of generatingFiles) {
        if (status.sessionId) {
          removeSummary(status.sessionId).catch(console.error);
        }
      }
      generatingFiles.clear();
    };
  }, [inputValue]);

  // 提取思考过程并清理summary
  const processedSummary = useMemo(() => {
    if (!summary) return "";

    // 检查是否有完整的思考过程（有开始和结束标签）
    const completeThinkMatch = RegExp(/<think>([\s\S]*?)<\/think>/).exec(
      summary
    );

    // 检查是否有未完成的思考过程（只有开始标签）
    const incompleteThinkMatch = RegExp(/<think>([\s\S]*?)$/).exec(summary);

    if (completeThinkMatch?.[1]) {
      // 完整的思考过程
      setThinkingProcess(completeThinkMatch[1].trim());
      // 从summary中移除思考过程
      return summary.replace(/<think>[\s\S]*?<\/think>/g, "");
    } else if (incompleteThinkMatch?.[1]) {
      // 未完成的思考过程
      setThinkingProcess(incompleteThinkMatch[1].trim() + " ...");
      // 从summary中移除未完成的思考过程部分
      const beforeThink = summary.split("<think>")[0] || "";
      return beforeThink;
    } else {
      // 没有思考过程
      setThinkingProcess("");
      return summary;
    }
  }, [summary]);

  if (!file) {
    return (
      <div className="w-full h-answer p-4 flex flex-col justify-center items-center text-gray-600 dark:text-gray-300">
        {t("fileDetail.noFile")}
      </div>
    );
  }

  const isDocument =
    file.type === DataType.Doc || file.type === DataType.Developer;

  switch (file.type) {
    case DataType.SearchOnWeb:
      return (
        <div className="w-full h-answer p-2 flex flex-col items-center justify-center">
          <div className="w-16 h-16 mb-4">{file.icon}</div>
          <div className="text-lg font-bold mb-2 dark:text-gray-100">
            {t("searchBar.detail.searchOnWeb")}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-300 text-center mb-4">
            {t("searchBar.detail.searchBrowserPrompt", { inputValue })}
          </div>
          <button
            className="bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            onClick={async () => {
              const appConfig = await readAppConfig();
              const urlTemplate = appConfig?.default_search_engine
                ?.url_template as string;
              if (urlTemplate) {
                open(urlTemplate.replace("%s", inputValue));
              }
            }}
          >
            {t("searchBar.detail.searchNow")}
          </button>
        </div>
      );
    default:
      return (
        <div className="w-full h-answer p-2 flex flex-col items-center relative overflow-x-hidden">
          {isDocument && (
            <div className="absolute top-2 right-2 flex items-center z-[2000]">
              {showAISummary ? (
                <Tooltip title={t("common.back")} placement="left">
                  <IconButton
                    onClick={() => paginate(-1)}
                    disabled={loading}
                    sx={{
                      bgcolor: "background.paper",
                      color: "text.primary",
                      "&:hover": {
                        bgcolor: "action.hover",
                      },
                    }}
                  >
                    <ChevronLeftIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <Tooltip title={t("fileDetail.viewSummary")} placement="left">
                  <button
                    onClick={() => paginate(1)}
                    disabled={loading}
                    className="flex items-center gap-1 px-3 py-1.5 text-sm rounded-full bg-blue-500 text-white hover:bg-blue-600 transition-colors cursor-pointer dark:bg-blue-600 dark:hover:bg-blue-700"
                  >
                    <SmartToyIcon sx={{ fontSize: 16 }} />
                    {t("fileDetail.aiSummarize")}
                  </button>
                </Tooltip>
              )}
            </div>
          )}

          <AnimatePresence initial={false} custom={direction}>
            <motion.div
              key={page}
              custom={direction}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
              }}
              className="w-full absolute flex flex-col items-center"
            >
              {showAISummary ? (
                <div className="w-full h-full flex flex-col">
                  <h3 className="text-lg font-bold p-4 flex-shrink-0">
                    {t("fileDetail.aiSummary")}
                  </h3>
                  <Box
                    sx={{
                      flex: 1,
                      overflowY: "auto",
                      padding: "0 16px 16px 16px",
                      "&::-webkit-scrollbar": {
                        width: "8px",
                      },
                      "&::-webkit-scrollbar-track": {
                        background: "var(--scrollbar-track)",
                        borderRadius: "4px",
                      },
                      "&::-webkit-scrollbar-thumb": {
                        background: "var(--scrollbar-thumb)",
                        borderRadius: "4px",
                        "&:hover": {
                          background: "var(--scrollbar-thumb-hover)",
                        },
                      },
                    }}
                  >
                    {isGenerating ? (
                      <div className="flex flex-col gap-2">
                        {summary ? (
                          <>
                            {thinkingProcess && (
                              <Accordion className="mb-4">
                                <AccordionSummary
                                  expandIcon={<ExpandMoreIcon />}
                                  aria-controls="thinking-process-content"
                                  id="thinking-process-header"
                                >
                                  <Typography>
                                    {t("fileDetail.thinkingProcess")}
                                  </Typography>
                                </AccordionSummary>
                                <AccordionDetails>
                                  <pre className="overflow-x-auto whitespace-pre-wrap break-words bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-gray-200 p-4 rounded">
                                    {thinkingProcess}
                                  </pre>
                                </AccordionDetails>
                              </Accordion>
                            )}
                            <ReactMarkdown
                              remarkPlugins={[remarkGfm]}
                              components={{
                                code({ node, className, children, ...props }) {
                                  return (
                                    <code
                                      className={`${className} break-all text-gray-800 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded`}
                                      {...props}
                                    >
                                      {children}
                                    </code>
                                  );
                                },
                                pre({ node, children, ...props }) {
                                  return (
                                    <pre
                                      className="overflow-x-auto whitespace-pre-wrap break-words bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-gray-200 p-4 rounded"
                                      {...props}
                                    >
                                      {children}
                                    </pre>
                                  );
                                },
                                p({ node, children, ...props }) {
                                  return (
                                    <p
                                      className="text-gray-800 dark:text-gray-200 mb-2"
                                      {...props}
                                    >
                                      {children}
                                    </p>
                                  );
                                },
                                h1({ node, children, ...props }) {
                                  return (
                                    <h1
                                      className="text-gray-900 dark:text-gray-100 text-2xl font-bold mb-4"
                                      {...props}
                                    >
                                      {children}
                                    </h1>
                                  );
                                },
                                h2({ node, children, ...props }) {
                                  return (
                                    <h2
                                      className="text-gray-900 dark:text-gray-100 text-xl font-bold mb-3"
                                      {...props}
                                    >
                                      {children}
                                    </h2>
                                  );
                                },
                                h3({ node, children, ...props }) {
                                  return (
                                    <h3
                                      className="text-gray-900 dark:text-gray-100 text-lg font-bold mb-2"
                                      {...props}
                                    >
                                      {children}
                                    </h3>
                                  );
                                },
                                ul({ node, children, ...props }) {
                                  return (
                                    <ul
                                      className="text-gray-800 dark:text-gray-200 list-disc pl-6 mb-2"
                                      {...props}
                                    >
                                      {children}
                                    </ul>
                                  );
                                },
                                ol({ node, children, ...props }) {
                                  return (
                                    <ol
                                      className="text-gray-800 dark:text-gray-200 list-decimal pl-6 mb-2"
                                      {...props}
                                    >
                                      {children}
                                    </ol>
                                  );
                                },
                                li({ node, children, ...props }) {
                                  return (
                                    <li
                                      className="text-gray-800 dark:text-gray-200 mb-1"
                                      {...props}
                                    >
                                      {children}
                                    </li>
                                  );
                                },
                                blockquote({ node, children, ...props }) {
                                  return (
                                    <blockquote
                                      className="text-gray-800 dark:text-gray-200 border-l-4 border-gray-300 dark:border-gray-600 pl-4 my-2 italic"
                                      {...props}
                                    >
                                      {children}
                                    </blockquote>
                                  );
                                },
                                strong({ node, children, ...props }) {
                                  return (
                                    <strong
                                      className="text-gray-900 dark:text-gray-100 font-bold"
                                      {...props}
                                    >
                                      {children}
                                    </strong>
                                  );
                                },
                                em({ node, children, ...props }) {
                                  return (
                                    <em
                                      className="text-gray-800 dark:text-gray-200 italic"
                                      {...props}
                                    >
                                      {children}
                                    </em>
                                  );
                                },
                              }}
                            >
                              {processedSummary}
                            </ReactMarkdown>
                          </>
                        ) : (
                          <div className="flex flex-col gap-2">
                            <Skeleton variant="text" width="100%" height={20} />
                            <Skeleton variant="text" width="90%" height={20} />
                            <Skeleton variant="text" width="95%" height={20} />
                            <Skeleton variant="text" width="85%" height={20} />
                          </div>
                        )}
                      </div>
                    ) : summary ? (
                      summary === "File is empty" ? (
                        <div className="text-gray-500 dark:text-gray-400">
                          {t("fileDetail.fileEmpty")}
                        </div>
                      ) : (
                        <>
                          {thinkingProcess && (
                            <Accordion className="mb-4">
                              <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}
                                aria-controls="thinking-process-content"
                                id="thinking-process-header"
                              >
                                <Typography>
                                  {t("fileDetail.thinkingProcess")}
                                </Typography>
                              </AccordionSummary>
                              <AccordionDetails>
                                <pre className="overflow-x-auto whitespace-pre-wrap break-words bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-gray-200 p-4 rounded">
                                  {thinkingProcess}
                                </pre>
                              </AccordionDetails>
                            </Accordion>
                          )}
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm]}
                            components={{
                              code({ node, className, children, ...props }) {
                                return (
                                  <code
                                    className={`${className} break-all text-gray-800 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded`}
                                    {...props}
                                  >
                                    {children}
                                  </code>
                                );
                              },
                              pre({ node, children, ...props }) {
                                return (
                                  <pre
                                    className="overflow-x-auto whitespace-pre-wrap break-words bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-gray-200 p-4 rounded"
                                    {...props}
                                  >
                                    {children}
                                  </pre>
                                );
                              },
                              p({ node, children, ...props }) {
                                return (
                                  <p
                                    className="text-gray-800 dark:text-gray-200 mb-2"
                                    {...props}
                                  >
                                    {children}
                                  </p>
                                );
                              },
                              h1({ node, children, ...props }) {
                                return (
                                  <h1
                                    className="text-gray-900 dark:text-gray-100 text-2xl font-bold mb-4"
                                    {...props}
                                  >
                                    {children}
                                  </h1>
                                );
                              },
                              h2({ node, children, ...props }) {
                                return (
                                  <h2
                                    className="text-gray-900 dark:text-gray-100 text-xl font-bold mb-3"
                                    {...props}
                                  >
                                    {children}
                                  </h2>
                                );
                              },
                              h3({ node, children, ...props }) {
                                return (
                                  <h3
                                    className="text-gray-900 dark:text-gray-100 text-lg font-bold mb-2"
                                    {...props}
                                  >
                                    {children}
                                  </h3>
                                );
                              },
                              ul({ node, children, ...props }) {
                                return (
                                  <ul
                                    className="text-gray-800 dark:text-gray-200 list-disc pl-6 mb-2"
                                    {...props}
                                  >
                                    {children}
                                  </ul>
                                );
                              },
                              ol({ node, children, ...props }) {
                                return (
                                  <ol
                                    className="text-gray-800 dark:text-gray-200 list-decimal pl-6 mb-2"
                                    {...props}
                                  >
                                    {children}
                                  </ol>
                                );
                              },
                              li({ node, children, ...props }) {
                                return (
                                  <li
                                    className="text-gray-800 dark:text-gray-200 mb-1"
                                    {...props}
                                  >
                                    {children}
                                  </li>
                                );
                              },
                              blockquote({ node, children, ...props }) {
                                return (
                                  <blockquote
                                    className="text-gray-800 dark:text-gray-200 border-l-4 border-gray-300 dark:border-gray-600 pl-4 my-2 italic"
                                    {...props}
                                  >
                                    {children}
                                  </blockquote>
                                );
                              },
                              strong({ node, children, ...props }) {
                                return (
                                  <strong
                                    className="text-gray-900 dark:text-gray-100 font-bold"
                                    {...props}
                                  >
                                    {children}
                                  </strong>
                                );
                              },
                              em({ node, children, ...props }) {
                                return (
                                  <em
                                    className="text-gray-800 dark:text-gray-200 italic"
                                    {...props}
                                  >
                                    {children}
                                  </em>
                                );
                              },
                            }}
                          >
                            {processedSummary}
                          </ReactMarkdown>
                        </>
                      )
                    ) : (
                      <Alert severity="error">
                        {t("fileDetail.aiSummaryFailed")}
                      </Alert>
                    )}
                  </Box>
                </div>
              ) : (
                <div className="w-full p-2 flex flex-col items-center">
                  <FileIcon
                    filepath={file.path}
                    target={"Detail"}
                    isUwp={file.type === "UWP Apps"}
                  />
                  <div className="text-center">
                    <div className="text-lg font-bold max-w-60 truncate mb-4 dark:text-gray-100">
                      <span title={file.name}>{file.name}</span>
                    </div>
                  </div>
                  <FileDetailAction file={file} />
                  <FileDetailTable file={file} />
                </div>
              )}
            </motion.div>
          </AnimatePresence>
        </div>
      );
  }
};

export default FileDetail;
