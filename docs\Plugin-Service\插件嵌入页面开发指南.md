# Aiverything 插件嵌入页面开发指南

## 概述

本文档专注于 Aiverything 插件的嵌入页面前端开发，包括HTML页面开发、JavaScript通信、消息传递机制等前端技术的实现。

## 嵌入页面特点

嵌入页面直接在Aiverything搜索界面中显示，与主应用共享搜索上下文，适合轻量级的交互和展示功能。

## 配置设置

在 `plugin.json` 中启用嵌入页面支持：

```json
{
  "embeddedSupport": true,
  "embeddedPage": "index.html"
}
```

### 开发时配置技巧

**重要提示**：在开发嵌入页面时，可以将 `embeddedPage` 临时设置为 Vite 开发服务器地址，以便利用热更新功能进行快速开发和调试。

#### 开发配置示例：

```json
{
  "embeddedSupport": true,
  "embeddedPage": "http://localhost:5173/plugin/static/my-plugin-identifier/embedded"
}
```

#### 开发流程：

1. **配置 Vite 项目**：
   确保 `vite.config.js` 中配置了正确的 base 路径：
   ```javascript
   export default defineConfig({
     plugins: [vue()], // 或其他插件
     base: '/plugin/static/my-plugin-identifier/embedded',
     server: {
       port: 5173
     }
   })
   ```

2. **启动 Vite 开发服务器**：
   ```bash
   cd vite-project
   npm run dev
   # Vite 会在 http://localhost:5173 启动，并应用 base 路径
   ```

3. **修改 plugin.json 配置**：
   ```json
   {
     "embeddedSupport": true,
     "embeddedPage": "http://localhost:5173/plugin/static/my-plugin-identifier/embedded"  // 包含完整base路径
   }
   ```

4. **重新加载插件**：
   - 在 Aiverything 中重新加载插件
   - 现在嵌入页面将直接从 Vite 开发服务器加载
   - 享受热更新带来的快速开发体验

5. **生产环境配置**：
   开发完成后，记得将配置改回静态文件路径：
   ```json
   {
     "embeddedSupport": true,
     "embeddedPage": "embedded/index.html"  // 生产环境使用
   }
   ```

#### 注意事项：

- **完整路径**：开发服务器地址必须包含完整的 base 路径，与 `vite.config.js` 中的配置一致
- **端口号**：Vite 默认使用 5173 端口，如果被占用会自动选择其他端口，需要相应更新配置
- **base路径**：确保开发服务器地址中的路径与生产环境的路径结构一致
- **CORS 配置**：通常不需要额外的 CORS 配置，Aiverything 会正确处理跨域请求
- **热更新**：修改代码后页面会自动刷新，大大提高开发效率
- **调试工具**：可以直接使用浏览器的开发者工具进行调试
- **生产部署**：最终部署时必须使用构建后的静态文件

### 页面路径说明

**重要：** `embeddedPage` 的路径是相对于项目 `src/main/resources/static/` 文件夹的相对路径。

#### 路径配置示例：

- **配置为 `"index.html"`**：
  - 实际文件位置：`src/main/resources/static/index.html`
  - 这是最简单的配置，页面文件直接放在 static 根目录

- **配置为 `"embedded/index.html"`**：
  - 实际文件位置：`src/main/resources/static/embedded/index.html`
  - 推荐的组织方式，将嵌入页面放在专门的文件夹中

- **配置为 `"pages/embedded/main.html"`**：
  - 实际文件位置：`src/main/resources/static/pages/embedded/main.html`
  - 更复杂的目录结构，适合大型插件项目

#### 推荐的目录结构：

```
src/main/resources/static/
├── embedded/                    # 嵌入页面目录
│   ├── index.html              # 嵌入页面主文件
│   ├── style.css               # 嵌入页面样式
│   └── script.js               # 嵌入页面脚本
├── entry/                      # 独立页面目录（如果有）
│   ├── index.html
│   └── app.js
└── icon.png                    # 插件图标
```

#### plugin.json 配置示例：

```json
{
  "name": "My Embedded Plugin",
  "identifier": "my-embedded-plugin",
  "version": "1.0.0",
  "className": "com.platform.plugin.MyPlugin",
  "embeddedSupport": true,
  "embeddedPage": "embedded/index.html",
  "entryPage": "entry/index.html",
  "icon": "icon.png"
}
```

## 前端开发方式

### 推荐开发框架

**推荐使用 Vite 框架**进行开发，支持Vue、React等多种前端技术栈。Vite是一种新型前端构建工具，能够显著提升前端开发体验：

1. **Vite + Vue**：现代化开发体验，热更新快速
2. **Vite + React**：优秀的构建性能和开发体验  
3. **原生HTML/CSS/JavaScript**：仅适合非常简单的功能实现

### Vite开发配置

**推荐使用Vite框架**，配置简单，构建快速。根据[Vite官方文档](https://vite.dev/guide/)，Vite提供了丰富的内建功能和快速的模块热替换（HMR）。将构建后的 `dist` 文件夹内容放到对应的 `entryPage` 和 `embeddedPage` 路径下。

#### Vite配置要求

在 `vite.config.js` 中配置 `base` 路径为：`/plugin/static/{pluginIdentifier}/{static文件夹相对路径}`

**Vite配置示例（vite.config.js）**：
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue' // 如果使用Vue
// import react from '@vitejs/plugin-react' // 如果使用React

export default defineConfig({
  plugins: [vue()], // 或 [react()]
  
  // 嵌入页面配置
  base: '/plugin/static/my-plugin-identifier/embedded',
})
```

#### 路径配置说明

- `{pluginIdentifier}`：替换为你的插件标识符（plugin.json中的identifier字段）
- `{static文件夹相对路径}`：
  - 嵌入页面：`embedded`
  - 独立页面：`entry`
  - 具体取决于您在项目中static文件夹中创建的文件夹名

**配置示例**：
```javascript
// 如果插件identifier为 "my-calculator"
// src/main/resources/static中存在两个文件夹，一个embedded，一个entry
// 嵌入页面配置
base: '/plugin/static/my-calculator/embedded'

// 独立页面配置  
base: '/plugin/static/my-calculator/entry'
```

#### 快速创建项目

使用Vite官方命令创建项目：

```bash
npm create vite@latest

# 进入项目目录并安装依赖
cd my-plugin-frontend
npm install
```

#### 部署流程

1. **开发阶段**：在Vite项目中正常开发
2. **构建阶段**：使用正确的 base 路径配置进行构建
3. **部署阶段**：将 `dist` 文件夹内容复制到插件项目的对应路径

```bash
# 构建项目
npm run build

# 构建完成后的文件部署
cp -r vite-project/dist/* src/main/resources/static/embedded/
# 或
cp -r vite-project/dist/* src/main/resources/static/entry/
```

### 原生HTML开发示例

对于简单功能，仍可使用原生HTML开发：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>嵌入插件</title>
    <!-- 引用同目录下的CSS文件 -->
    <link rel="stylesheet" href="style.css">
    <!-- 或引用其他目录的CSS文件 -->
    <link rel="stylesheet" href="../assets/common.css">
</head>
<body>
    <div class="plugin-container">
        <div class="search-display">
            <input id="search-input" type="text" readonly>
        </div>
        <div class="content-area">
            <!-- 功能内容 -->
            <img src="../assets/images/logo.png" alt="插件Logo">
        </div>
    </div>
    <!-- 引用同目录下的JS文件 -->
    <script src="script.js"></script>
    <!-- 或引用其他目录的JS文件 -->
    <script src="../assets/utils.js"></script>
</body>
</html>
```

### Vite + Vue开发示例

#### 创建Vite Vue项目
```bash
npm create vite@latest my-plugin-frontend -- --template vue
cd my-plugin-frontend
npm install
```

#### Vue组件结构

```vue
<template>
  <div class="plugin-container">
    <div class="search-display">
      <input v-model="searchValue" type="text" readonly>
    </div>
    <div class="content-area">
      <!-- 插件功能内容 -->
      <button @click="changeInput('计算结果: 42')">发送结果到搜索框</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const searchValue = ref('')

const handleMessage = (event) => {
  const { type, value } = event.data
  
  if (type === 'inputChanged') {
    searchValue.value = value || ''
    // 处理搜索输入变化
    console.log('搜索内容变化:', value)
  }
}

const sendMessageToParent = (message) => {
  if (window.parent && window.parent !== window) {
    window.parent.postMessage(message, '*')
  }
}

const changeInput = (newValue) => {
  sendMessageToParent({
    type: 'changeInput',
    value: newValue
  })
}

onMounted(() => {
  window.addEventListener('message', handleMessage)
})

onUnmounted(() => {
  window.removeEventListener('message', handleMessage)
})
</script>
```

### Vite + React开发示例

#### 创建Vite React项目
```bash
npm create vite@latest my-plugin-frontend -- --template react
cd my-plugin-frontend
npm install
```

#### React组件结构

```jsx
import React, { useState, useEffect, useCallback } from 'react'

function EmbeddedPlugin() {
  const [searchValue, setSearchValue] = useState('')

  const sendMessageToParent = useCallback((message) => {
    if (window.parent && window.parent !== window) {
      window.parent.postMessage(message, '*')
    }
  }, [])

  const handleMessage = useCallback((event) => {
    const { type, value } = event.data
    
    if (type === 'inputChanged') {
      setSearchValue(value || '')
      // 处理搜索输入变化
      console.log('搜索内容变化:', value)
    }
  }, [])

  useEffect(() => {
    window.addEventListener('message', handleMessage)
    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [handleMessage])

  const changeInput = (newValue) => {
    sendMessageToParent({
      type: 'changeInput',
      value: newValue
    })
  }

  return (
    <div className="plugin-container">
      <div className="search-display">
        <input 
          value={searchValue} 
          type="text" 
          readOnly 
        />
      </div>
      <div className="content-area">
        {/* 插件功能内容 */}
        <button onClick={() => changeInput('计算结果: 42')}>
          发送结果到搜索框
        </button>
      </div>
    </div>
  )
}

export default EmbeddedPlugin
```

### 资源文件引用说明

在嵌入页面中引用其他资源文件时，路径是相对于当前HTML文件的相对路径：

#### 如果 embeddedPage 配置为 `"embedded/index.html"`：

- **当前页面位置**：`static/embedded/index.html`
- **引用同目录文件**：`href="style.css"` → `static/embedded/style.css`
- **引用上级目录文件**：`href="../icon.png"` → `static/icon.png`
- **引用其他目录文件**：`href="../assets/common.css"` → `static/assets/common.css`

#### 常见的资源引用示例：

```html
<!-- 样式文件 -->
<link rel="stylesheet" href="style.css">                    <!-- 同目录 -->
<link rel="stylesheet" href="../assets/theme.css">          <!-- 共享样式 -->

<!-- 脚本文件 -->
<script src="script.js"></script>                           <!-- 同目录 -->
<script src="../assets/utils.js"></script>                  <!-- 共享工具 -->

<!-- 图片文件 -->
<img src="icon.png" alt="图标">                             <!-- 同目录 -->
<img src="../assets/images/logo.png" alt="Logo">            <!-- 共享图片 -->

<!-- 字体文件 -->
<style>
@font-face {
    font-family: 'CustomFont';
    src: url('../assets/fonts/custom.woff2') format('woff2');
}
</style>
```

## JavaScript通信机制

嵌入页面与父窗口之间通过`postMessage`进行通信，支持以下事件：

### 监听父窗口消息

父组件会向嵌入页面发送`inputChanged`事件，当搜索输入框内容变化时触发：

```javascript
window.addEventListener('message', function(event) {
    const { type, value } = event.data;
    
    if (type === 'inputChanged') {
        handleSearchInputChanged(value);
    }
});

function handleSearchInputChanged(searchValue) {
    document.getElementById('search-input').value = searchValue || '';
    // 处理搜索输入变化，可以根据输入内容更新页面显示
    console.log('搜索内容变化:', searchValue);
}
```

### 向父窗口发送消息

嵌入页面可以向父组件发送`changeInput`事件来修改搜索框内容：

```javascript
function sendMessageToParent(message) {
    if (window.parent && window.parent !== window) {
        window.parent.postMessage(message, '*');
    }
}

// 修改搜索框内容
function changeSearchInput(newValue) {
    sendMessageToParent({
        type: 'changeInput',
        value: newValue
    });
}

// 使用示例
function onButtonClick() {
    changeSearchInput('新的搜索内容');
}
```

## 前端调用后端API

前端可以通过HTTP POST请求调用后端注册的命令处理器。

### 后端命令注册

首先在Java后端注册命令处理器：

```java
public class TestPlugin extends Plugin {
    @Override
    public void load(Map<String, Object> config) {
        // 注册测试命令 - 对应前端调用的 "testCommand"
        registerCommandHandler("testCommand", paramsMap -> {
            String param1 = (String) paramsMap.get("param1");
            String param2 = (String) paramsMap.get("param2");
            
            // 处理业务逻辑
            String result = "处理完成: " + param1 + ", " + param2;
            
            return Map.of("result", result, "success", true);
        });
    }
}
```

### API调用配置

```javascript
// API基础配置
const baseUrl = process.env.NODE_ENV === "development"
  ? "https://localhost:38884/plugin/invoke"  // 开发环境
  : `${window.location.origin}/plugin/invoke`; // 生产环境

const pluginIdentifier = "your-plugin-identifier"; // 替换为实际的插件标识符
```

### 调用后端命令

```javascript
// 通用API调用函数
function callPluginCommand(command, params = {}) {
  const queryParams = new URLSearchParams({
    pluginIdentifier: pluginIdentifier,
    command: command,
  }).toString();
  
  const fullUrl = `${baseUrl}?${queryParams}`;
  
  return fetch(fullUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(params),
  });
}

// 使用示例
async function executeCommand() {
  try {
    const response = await callPluginCommand("testCommand", {
      param1: "value1",  // 对应后端 paramsMap.get("param1")
      param2: "value2"   // 对应后端 paramsMap.get("param2")
    });
    
    if (response.ok) {
      const result = await response.json();
      // result.result 和 result.success 对应后端返回的 Map.of("result", result, "success", true)
      console.log("命令执行结果:", result.result);
      console.log("执行状态:", result.success);
      return result;
    } else {
      throw new Error(`HTTP错误: ${response.status}`);
    }
  } catch (error) {
    console.error("调用失败:", error);
    throw error;
  }
}
```

## 常见功能实现

### 搜索建议功能

```javascript
async function fetchSearchSuggestions(query) {
    try {
        const response = await fetch(`/api/suggestions?q=${encodeURIComponent(query)}`);
        const data = await response.json();
        return data.suggestions || [];
    } catch (error) {
        console.error('获取搜索建议失败:', error);
        return [];
    }
}

function displaySuggestions(suggestions) {
    const container = document.getElementById('suggestions');
    container.innerHTML = '';
    
    suggestions.forEach(suggestion => {
        const item = document.createElement('div');
        item.className = 'suggestion-item';
        item.textContent = suggestion.text;
        item.onclick = () => selectSuggestion(suggestion.text);
        container.appendChild(item);
    });
}

function selectSuggestion(text) {
    sendMessageToParent({
        type: 'changeInput',
        value: text
    });
}
```

## 性能优化

### 事件防抖

```javascript
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

const debouncedSearch = debounce(handleSearchInput, 300);
```

## 最佳实践

### 安全性
- 验证消息来源
- 净化用户输入
- 避免XSS攻击

### 用户体验
- 提供加载状态
- 优雅的错误处理
- 响应式设计

### 代码质量
- 模块化代码组织
- 详细的错误日志
- 全面的测试覆盖

## Vite开发完整工作流程

### Vite + Vue项目示例

#### 1. 创建Vite Vue项目
```bash
npm create vite@latest my-plugin-frontend -- --template vue
cd my-plugin-frontend
npm install
```

#### 2. 配置vite.config.js
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  
  // 配置base路径，替换为实际的插件标识符
  base: '/plugin/static/my-calculator/embedded',
  
  // 开发服务器配置
  server: {
    port: 5173,
    open: true
  }
})
```

#### 3. 开发阶段配置
```bash
# 启动开发服务器
npm run dev
```

然后修改插件的 `plugin.json`：
```json
{
  "embeddedSupport": true,
  "embeddedPage": "http://localhost:5173/plugin/static/my-plugin-identifier/embedded"  // 包含完整base路径
}
```

#### 4. 生产构建和部署
开发完成后：

1. **构建项目**：
   ```bash
   npm run build
   ```

2. **修改配置**：
   ```json
   {
     "embeddedSupport": true,
     "embeddedPage": "embedded/index.html"  // 生产环境使用
   }
   ```

3. **部署文件**：
   ```bash
   cp -r dist/* ../plugin-project/src/main/resources/static/embedded/
   ```

### Vite + React项目示例

#### 1. 创建Vite React项目
```bash
npm create vite@latest my-plugin-frontend -- --template react
cd my-plugin-frontend
npm install
```

#### 2. 配置vite.config.js
```javascript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  
  // 配置base路径，替换为实际的插件标识符
  base: '/plugin/static/my-calculator/embedded',
  
  server: {
    port: 5173,
    open: true
  }
})
```

#### 3. 开发阶段配置
```bash
# 启动开发服务器
npm run dev
```

然后修改插件的 `plugin.json`：
```json
{
  "embeddedSupport": true,
  "embeddedPage": "http://localhost:5173/plugin/static/my-plugin-identifier/embedded"  // 包含完整base路径
}
```

#### 4. 生产构建和部署
开发完成后按照Vue项目相同的步骤进行构建和部署。

### 开发最佳实践

1. **开发环境**：始终使用 `http://localhost:5173` 进行开发
2. **热更新**：利用Vite的热更新功能快速迭代
3. **调试工具**：使用浏览器开发者工具进行调试
4. **生产部署**：确保最终使用构建后的静态文件
5. **版本控制**：不要将开发服务器地址提交到版本控制系统

---

*本文档专注于嵌入页面开发，关于独立页面和后端开发请参考相应文档。* 