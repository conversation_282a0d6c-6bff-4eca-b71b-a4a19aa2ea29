import { useEffect, useLayoutEffect, useRef, useState } from "react";
import { PluginInfo } from "../MainAnwser";
import React from "react";

const PluginInfoItem: React.FC<PluginInfo> = (pluginInfo: PluginInfo) => {
  return (
    <React.Fragment>
      <img
        className={"w-12 h-12 mr-4"}
        src={pluginInfo.icon}
        alt="no plugin icon"
      />
      <div className="flex-1 min-w-0">
        <div className="font-semibold truncate">{pluginInfo.name}</div>
        <div className="text-sm text-gray-500 truncate">
          {pluginInfo.description}
        </div>
      </div>
    </React.Fragment>
  );
};

const PluginList = ({ data, onSelect, onItemClick }) => {
  const containerRef = useRef<any>(null);
  const [lastAction, setLastAction] = useState("keyboard"); // 新增状态来记录最后的操作来源
  const [selectedPlugin, setSelectedPlugin] = useState<PluginInfo>();
  const selectedPluginRef = useRef(null);
  const [selectedIndex, setSelectedIndex] = useState<number>();

  useLayoutEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Alt") {
        event.preventDefault();
      }
      setLastAction("keyboard"); // 设置最后的操作为键盘
      if (event.key === "ArrowUp") {
        setSelectedIndex((prevIndex) => {
          let newIndex = prevIndex - 1;
          if (newIndex < 0) {
            newIndex = 0;
          }
          return newIndex;
        });
      } else if (event.key === "ArrowDown") {
        setSelectedIndex((prevIndex) => {
          let newIndex = prevIndex + 1;
          if (newIndex >= data.length) {
            newIndex = data.length - 1;
          }
          return newIndex;
        });
      }
    };
    setSelectedIndex(0);
    const currentPlugin = data[0];
    setSelectedPlugin(currentPlugin);
    onSelect(currentPlugin);

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [data]);

  useEffect(() => {
    if (data && data.length > 0) {
      const selectedPlugin = data[selectedIndex];
      setSelectedPlugin(selectedPlugin);
      onSelect(selectedPlugin);
      if (selectedPluginRef.current) {
        // 使用 setTimeout 确保在 DOM 更新后再执行滚动操作
        setTimeout(() => {
          if (selectedPluginRef.current) {
            selectedPluginRef.current.scrollIntoView({
              behavior: lastAction == "keyboard" ? "auto" : "smooth",
              block: "nearest",
            });
          }
        }, 0);
      }
    }
  }, [selectedIndex, onSelect, lastAction]);

  if (!data || data.length === 0) {
    return null;
  }

  return (
    <ul
      ref={containerRef}
      className="w-full h-answer p-4 bg-main-query transition-opacity overflow-y-auto dark:bg-gray-800"
      style={{
        background: "transparent",
      }}
    >
      {data.map((plugin, pluginIndex) => (
        <li
          key={pluginIndex}
          ref={selectedPlugin === plugin ? selectedPluginRef : null}
          className={`flex items-center p-2 rounded cursor-pointer scroll-m-4 w-full ${
            selectedPlugin === plugin ? "bg-answer dark:bg-gray-700" : ""
          }`}
          onMouseMove={() => {
            setLastAction("mouse");
            setSelectedPlugin(plugin);
            onSelect(plugin);
            setSelectedIndex(pluginIndex);
          }}
          onClick={() => onItemClick(plugin)}
        >
          {PluginInfoItem(plugin)}
        </li>
      ))}
    </ul>
  );
};

export default PluginList;
