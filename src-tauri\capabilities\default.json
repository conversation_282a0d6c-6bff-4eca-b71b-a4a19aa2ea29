{"identifier": "main-capability", "description": "Capability for the main window", "local": true, "windows": ["main", "settings", "attach", "pluginSettings"], "permissions": ["core:default", "shell:allow-execute", "shell:allow-open", "shell:allow-spawn", "shell:default", "core:window:default", "core:window:allow-start-dragging", "core:window:allow-close", "core:window:allow-hide", "core:app:allow-default-window-icon", "core:webview:allow-create-webview-window", "core:window:allow-set-resizable", "core:window:allow-set-size", "core:window:allow-set-position", "core:window:allow-show", "core:window:allow-set-focus", "core:window:allow-unminimize", "dialog:default", "dialog:allow-open", "notification:allow-cancel", "notification:allow-check-permissions", "notification:allow-create-channel", "notification:allow-delete-channel", "notification:allow-is-permission-granted", "notification:allow-notify", "notification:allow-permission-state", "notification:allow-request-permission", "notification:allow-show", "process:default", "process:allow-exit", "process:allow-restart", "prevent-default:default", "deep-link:default", {"identifier": "shell:allow-execute", "allow": [{"name": "showItemInFolder", "cmd": "explorer", "args": ["/select,", {"validator": "*"}]}, {"name": "openTerminalAtPath", "cmd": "wt", "args": ["/d", {"validator": "*"}]}, {"name": "openExplorer", "cmd": "explorer.exe", "args": []}, {"name": "openRecycleBin", "cmd": "explorer.exe", "args": ["shell:RecycleBinFolder"]}, {"name": "openDownloads", "cmd": "explorer.exe", "args": ["shell:Downloads"]}, {"name": "openControlPanel", "cmd": "control", "args": []}, {"name": "openSystemSettings", "cmd": "explorer.exe", "args": ["ms-settings:"]}, {"name": "openEnvVarSettings", "cmd": "explorer.exe", "args": ["ms-settings:about"]}, {"name": "openUserAccounts", "cmd": "explorer.exe", "args": ["ms-settings:accountsinfo"]}, {"name": "openNetworkSettings", "cmd": "explorer.exe", "args": ["ms-settings:network"]}, {"name": "openPowerOptions", "cmd": "explorer.exe", "args": ["ms-settings:powersleep"]}, {"name": "openRegistryEditor", "cmd": "regedit.exe", "args": []}, {"name": "openDeviceManager", "cmd": "cmd.exe", "args": ["/c", "devmgmt.msc"]}, {"name": "openDiskManagement", "cmd": "cmd.exe", "args": ["/c", "diskmgmt.msc"]}, {"name": "openServices", "cmd": "cmd.exe", "args": ["/c", "services.msc"]}, {"name": "openTaskManager", "cmd": "taskmgr.exe", "args": []}, {"name": "openEventViewer", "cmd": "cmd.exe", "args": ["/c", "eventvwr.msc"]}, {"name": "openGroupPolicy", "cmd": "cmd.exe", "args": ["/c", "gpedit.msc"]}]}, "global-shortcut:allow-is-registered", "global-shortcut:allow-register", "global-shortcut:allow-unregister", "os:default", "updater:default"]}