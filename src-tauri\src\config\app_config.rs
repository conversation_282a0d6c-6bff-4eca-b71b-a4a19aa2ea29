use std::sync::Mutex;

use config::{Config, File};
use serde::{Deserialize, Serialize};
use tauri_plugin_global_shortcut::{Code, Modifiers};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Settings {
    pub attach_explorer: bool,
    pub locale: String,
    pub default_search_engine: SearchEngine,
    pub search_engine: Vec<SearchEngine>,
    pub hotkey: HotkeySettings,
    pub suffix_description: std::collections::HashMap<String, String>,
    pub debug_mode: DebugMode,
    pub enable_double_ctrl: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DebugMode {
    pub enable: bool,
    pub jdk_home: String,
    pub java_agent: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct HotkeySettings {
    pub modifiers: Modifiers,
    pub key: Code,
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct SearchEngine {
    pub name: String,
    pub url_template: String,
}

lazy_static::lazy_static! {
    static ref SETTINGS_OPTION: Mutex<Option<Settings>> = Mutex::new(None);
    static ref CONFIG_FILE: String = if cfg!(not(debug_assertions)) { "config.json".to_string() } else { "../config.json".to_string() };
}

pub fn set_config(app_settings: Settings) {
    let app_settings_json =
        serde_json::to_string_pretty(&app_settings).expect("Failed to serialize settings");
    println!("Set app config: {:?}", app_settings_json);
    std::fs::write(CONFIG_FILE.as_str(), app_settings_json)
        .expect("Failed to write default config");
    let mut settings_wrap = SETTINGS_OPTION.lock().expect("lock settings option failed");
    settings_wrap.replace(app_settings);
}

pub fn read_config() -> Result<Settings, config::ConfigError> {
    let mut settings_wrap = SETTINGS_OPTION.lock().expect("lock settings option failed");
    let settings_opt = settings_wrap.as_ref().or(None);
    match settings_opt {
        Some(app_settings) => Ok(app_settings.to_owned()),
        None => {
            if !std::path::Path::new(CONFIG_FILE.as_str()).exists() {
                let default_config = generate_default_config();
                let default_config_string = serde_json::to_string_pretty(&default_config)
                    .expect("Failed to serialize default config");
                std::fs::write(CONFIG_FILE.as_str(), default_config_string)
                    .expect("Failed to write default config");
                settings_wrap.replace(default_config.clone());
                return Ok(default_config);
            }

            let config = Config::builder()
                .add_source(File::with_name(&CONFIG_FILE))
                .build()?;
            let settings_result = config.try_deserialize::<Settings>();
            match settings_result {
                Ok(settings) => {
                    settings_wrap.replace(settings.clone());
                    Ok(settings)
                }
                Err(e) => {
                    eprintln!("Failed to read config: {}", e);
                    let default_config = generate_default_config();
                    let default_config_string = serde_json::to_string_pretty(&default_config)
                        .expect("Failed to serialize default config");
                    std::fs::write(CONFIG_FILE.as_str(), default_config_string)
                        .expect("Failed to write default config");
                    settings_wrap.replace(default_config.clone());
                    Ok(default_config)
                }
            }
        }
    }
}

fn generate_default_config() -> Settings {
    let mut map = std::collections::HashMap::new();
    map.insert("lnk".to_string(), "Shortcut file".to_string());
    map.insert("exe".to_string(), "Executable file".to_string());
    map.insert("mp3".to_string(), "MP3 audio file".to_string());
    map.insert("wav".to_string(), "WAVE audio file".to_string());
    map.insert(
        "flac".to_string(),
        "Free Lossless Audio Codec file".to_string(),
    );
    map.insert("aac".to_string(), "Advanced Audio Coding file".to_string());
    map.insert("ogg".to_string(), "Ogg Vorbis audio file".to_string());
    map.insert("m4a".to_string(), "MPEG-4 audio file".to_string());
    map.insert("wma".to_string(), "Windows Media Audio file".to_string());
    map.insert(
        "aiff".to_string(),
        "Audio Interchange File Format".to_string(),
    );
    map.insert("mid".to_string(), "MIDI file".to_string());
    map.insert("midi".to_string(), "MIDI file".to_string());
    map.insert("c".to_string(), "C source file".to_string());
    map.insert("h".to_string(), "C header file".to_string());
    map.insert("cpp".to_string(), "C++ source file".to_string());
    map.insert("hpp".to_string(), "C++ header file".to_string());
    map.insert("java".to_string(), "Java source file".to_string());
    map.insert("class".to_string(), "Java class file".to_string());
    map.insert("jar".to_string(), "Java Archive file".to_string());
    map.insert("py".to_string(), "Python script".to_string());
    map.insert("pyc".to_string(), "Compiled Python file".to_string());
    map.insert("js".to_string(), "JavaScript file".to_string());
    map.insert("mjs".to_string(), "JavaScript module file".to_string());
    map.insert("ts".to_string(), "TypeScript file".to_string());
    map.insert("tsx".to_string(), "TypeScript JSX file".to_string());
    map.insert("html".to_string(), "HTML file".to_string());
    map.insert("css".to_string(), "Cascading Style Sheets file".to_string());
    map.insert("scss".to_string(), "Sassy CSS file".to_string());
    map.insert("php".to_string(), "PHP script".to_string());
    map.insert("rb".to_string(), "Ruby script".to_string());
    map.insert("go".to_string(), "Go source file".to_string());
    map.insert("swift".to_string(), "Swift source file".to_string());
    map.insert("kt".to_string(), "Kotlin source file".to_string());
    map.insert("kts".to_string(), "Kotlin script file".to_string());
    map.insert("rs".to_string(), "Rust source file".to_string());
    map.insert("r".to_string(), "R script".to_string());
    map.insert("rmd".to_string(), "R Markdown file".to_string());
    map.insert("lua".to_string(), "Lua script".to_string());
    map.insert("pl".to_string(), "Perl script".to_string());
    map.insert("pm".to_string(), "Perl module".to_string());
    map.insert("sh".to_string(), "Shell script".to_string());
    map.insert("bash".to_string(), "Bash script".to_string());
    map.insert("ps1".to_string(), "PowerShell script".to_string());
    map.insert("sql".to_string(), "SQL script".to_string());
    map.insert("mat".to_string(), "MATLAB file".to_string());
    map.insert("hs".to_string(), "Haskell source file".to_string());
    map.insert(
        "lhs".to_string(),
        "Literate Haskell source file".to_string(),
    );
    map.insert("scala".to_string(), "Scala source file".to_string());
    map.insert("dart".to_string(), "Dart source file".to_string());
    map.insert("m".to_string(), "Objective-C source file".to_string());
    map.insert("s".to_string(), "Assembly source file".to_string());
    map.insert("asm".to_string(), "Assembly source file".to_string());
    map.insert("groovy".to_string(), "Groovy source file".to_string());
    map.insert("yaml".to_string(), "YAML file".to_string());
    map.insert("json".to_string(), "JSON file".to_string());
    map.insert("xml".to_string(), "XML file".to_string());
    map.insert("txt".to_string(), "Text file".to_string());
    map.insert("doc".to_string(), "Microsoft Word document".to_string());
    map.insert("docx".to_string(), "Microsoft Word document".to_string());
    map.insert("pdf".to_string(), "PDF document".to_string());
    map.insert("odt".to_string(), "OpenDocument text document".to_string());
    map.insert("rtf".to_string(), "Rich Text Format document".to_string());
    map.insert("md".to_string(), "Markdown file".to_string());
    map.insert("tex".to_string(), "LaTeX file".to_string());
    map.insert("wks".to_string(), "Microsoft Works file".to_string());
    map.insert("wps".to_string(), "Microsoft Works file".to_string());
    map.insert("jpg".to_string(), "JPEG image".to_string());
    map.insert("jpeg".to_string(), "JPEG image".to_string());
    map.insert(
        "png".to_string(),
        "Portable Network Graphics image".to_string(),
    );
    map.insert(
        "gif".to_string(),
        "Graphics Interchange Format image".to_string(),
    );
    map.insert("bmp".to_string(), "Bitmap image".to_string());
    map.insert("tiff".to_string(), "Tagged Image File Format".to_string());
    map.insert("svg".to_string(), "Scalable Vector Graphics".to_string());
    map.insert("psd".to_string(), "Adobe Photoshop document".to_string());
    map.insert("ai".to_string(), "Adobe Illustrator document".to_string());
    map.insert("ico".to_string(), "Icon file".to_string());
    map.insert(
        "eps".to_string(),
        "Encapsulated PostScript file".to_string(),
    );
    map.insert("raw".to_string(), "Raw image file".to_string());
    map.insert("xls".to_string(), "Microsoft Excel spreadsheet".to_string());
    map.insert(
        "xlsx".to_string(),
        "Microsoft Excel spreadsheet".to_string(),
    );
    map.insert("csv".to_string(), "Comma-separated values file".to_string());
    map.insert("ods".to_string(), "OpenDocument spreadsheet".to_string());
    map.insert("tsv".to_string(), "Tab-separated values file".to_string());
    map.insert(
        "xlsm".to_string(),
        "Microsoft Excel macro-enabled spreadsheet".to_string(),
    );
    map.insert("xltx".to_string(), "Microsoft Excel template".to_string());
    map.insert(
        "ppt".to_string(),
        "Microsoft PowerPoint presentation".to_string(),
    );
    map.insert(
        "pptx".to_string(),
        "Microsoft PowerPoint presentation".to_string(),
    );
    map.insert("odp".to_string(), "OpenDocument presentation".to_string());
    map.insert("key".to_string(), "Apple Keynote presentation".to_string());
    map.insert(
        "pps".to_string(),
        "Microsoft PowerPoint slideshow".to_string(),
    );
    map.insert(
        "pptm".to_string(),
        "Microsoft PowerPoint macro-enabled presentation".to_string(),
    );
    map.insert("mp4".to_string(), "MPEG-4 video file".to_string());
    map.insert("avi".to_string(), "Audio Video Interleave file".to_string());
    map.insert("mkv".to_string(), "Matroska video file".to_string());
    map.insert("mov".to_string(), "Apple QuickTime movie file".to_string());
    map.insert("wmv".to_string(), "Windows Media Video file".to_string());
    map.insert("flv".to_string(), "Flash Video file".to_string());
    map.insert("mpeg".to_string(), "MPEG video file".to_string());
    map.insert("mpg".to_string(), "MPEG video file".to_string());
    map.insert("webm".to_string(), "WebM video file".to_string());
    map.insert("vob".to_string(), "DVD Video Object file".to_string());

    let settings = Settings {
        attach_explorer: true,
        locale: "".to_string(),
        default_search_engine: SearchEngine {
            name: "Bing".to_string(),
            url_template: "https://www.bing.com/search?q=%s".to_string(),
        },
        search_engine: vec![
            SearchEngine {
                name: "Bing".to_string(),
                url_template: "https://www.bing.com/search?q=%s".to_string(),
            },
            SearchEngine {
                name: "Google".to_string(),
                url_template: "https://www.google.com/search?q=%s".to_string(),
            },
            SearchEngine {
                name: "DuckDuckGo".to_string(),
                url_template: "https://duckduckgo.com/?q=%s".to_string(),
            },
            SearchEngine {
                name: "Baidu".to_string(),
                url_template: "https://www.baidu.com/s?wd=%s".to_string(),
            },
        ],
        hotkey: HotkeySettings {
            modifiers: Modifiers::CONTROL | Modifiers::ALT | Modifiers::SHIFT,
            key: Code::KeyA,
        },
        suffix_description: map,
        debug_mode: DebugMode {
            enable: false,
            jdk_home: "".to_string(),
            java_agent: "".to_string(),
        },
        enable_double_ctrl: false,
    };
    settings
}
