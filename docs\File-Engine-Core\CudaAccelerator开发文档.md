# File-Engine-Core CUDA GPU 加速器开发文档

## 1. 概述

File-Engine-Core的CUDA GPU加速器是一个高性能的文件搜索引擎，利用NVIDIA GPU的并行计算能力实现海量文件数据的快速匹配和搜索。该系统通过JNI接口与Java层无缝集成，提供了完整的GPU加速文件搜索解决方案。

### 1.1 核心特性

- **GPU并行计算**: 利用CUDA架构实现大规模并行文件匹配
- **智能缓存系统**: 多层缓存机制优化内存使用和搜索性能
- **中文搜索支持**: 内置拼音转换和中文字符处理
- **模糊匹配算法**: 支持字符模式匹配和编辑距离计算
- **实时搜索**: 异步搜索和结果收集机制
- **内存优化**: 动态内存管理和LRU缓存策略
- **多GPU支持**: 支持多GPU设备切换和负载均衡

### 1.2 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                      Java Layer                             │
├─────────────────────────────────────────────────────────────┤
│                    JNI Interface                            │
├─────────────────────────────────────────────────────────────┤
│                   C++ Host Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Cache Manager │  │  Thread Pool    │  │ GPU Monitor  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   CUDA Device Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Global Cache   │  │  Search Kernel  │  │ String Utils │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 2. 架构设计

### 2.1 分层架构

#### 2.1.1 Java接口层
- **CudaAccelerator.java**: 单例模式的JNI接口类
- **核心方法**: match(), initCache(), clearCache(), getGPUMemUsage()
- **生命周期管理**: initialize(), release(), setDevice()

#### 2.1.2 JNI桥接层
- **dllmain.cpp**: 主要的JNI实现文件，包含所有native方法
- **接口映射**: Java方法到C++函数的映射
- **参数转换**: Java对象到C++数据结构的转换

#### 2.1.3 C++主机层
- **缓存管理**: 多级缓存系统和内存管理
- **线程池**: 异步搜索结果收集
- **GPU监控**: 设备状态监控和性能统计

#### 2.1.4 CUDA设备层
- **内核函数**: GPU并行搜索算法
- **设备内存**: 全局缓存和字符串处理
- **工具函数**: CUDA版本的字符串操作函数

### 2.2 核心组件

#### 2.2.1 缓存系统架构

```cpp
// 主缓存结构
struct list_cache {
    cache_data str_data;           // 字符串数据
    char* dev_output_bitmap;       // 匹配结果位图
    bool is_cache_valid;           // 缓存有效性
    std::atomic_bool is_match_done; // 匹配完成标志
    std::atomic_int is_output_done; // 输出完成状态
    unsigned matched_number;        // 匹配数量
};

// 字符串数据结构
struct cache_data {
    char* dev_strs;                        // GPU显存中的字符串
    size_t str_total_bytes;                // 总字节数
    size_t str_remain_blank_bytes;         // 剩余空间
    std::atomic_uint64_t record_num;       // 记录数量
    size_t* dev_str_addr;                  // 字符串地址数组
    unsigned short* str_length_array;      // 字符串长度数组
    size_t* global_parent_path_index_array; // 父路径索引数组
    concurrent_unordered_set<size_t> record_hash; // 记录哈希集合
};
```

#### 2.2.2 全局缓存系统

全局缓存用于存储文件路径的父目录部分，避免重复存储相同的路径前缀：

```cpp
namespace global_cache {
    struct global_cache_data {
        size_t total_bytes;                    // 总字节数
        char* dev_cache;                       // GPU缓存指针
        std::vector<size_t> dev_str_start_address; // 字符串起始地址
        std::vector<unsigned short> dev_str_len_vec; // 字符串长度
        size_t blank_start_address;            // 空白区域起始地址
        std::unordered_map<size_t, size_t> unique_hash_index_map; // 哈希索引映射
    };
}
```

## 3. 核心算法

### 3.1 并行搜索内核

#### 3.1.1 内核函数签名

```cpp
__global__ void check(
    const size_t* str_address_records,    // 字符串地址数组
    const size_t* global_path_cache,      // 全局路径缓存
    const size_t* total_num,              // 总记录数
    const int* search_case,               // 搜索选项
    const bool* is_ignore_case,           // 是否忽略大小写
    char* search_text,                    // 搜索文本
    char* keywords,                       // 关键词数组
    char* keywords_lower_case,            // 小写关键词数组
    const size_t* keywords_length,        // 关键词长度
    const bool* is_keyword_path,          // 是否为路径关键词
    char* output,                         // 输出结果位图
    const bool* is_stop_collect_var,      // 停止收集标志
    const bool* is_enable_fuzzy_match     // 是否启用模糊匹配
);
```

#### 3.1.2 搜索算法流程

1. **线程ID计算**: 使用3D网格计算全局线程ID
2. **边界检查**: 确保线程ID在有效范围内
3. **停止信号检查**: 检查是否需要提前终止
4. **字符串获取**: 从全局缓存获取文件名和路径
5. **关键词匹配**: 逐个检查所有关键词
6. **模糊匹配**: 支持字符模式匹配和拼音搜索
7. **结果输出**: 将匹配结果写入输出位图

### 3.2 字符串匹配算法

#### 3.2.1 精确匹配

```cpp
__device__ const char* strstr_cuda(const char* s1, const char* s2) {
    int n;
    if (*s2) {
        while (*s1) {
            for (n = 0; *(s1 + n) == *(s2 + n); ++n) {
                if (!*(s2 + n + 1)) {
                    return s1;
                }
            }
            ++s1;
        }
        return nullptr;
    }
    return s1;
}
```

#### 3.2.2 模式字符匹配

```cpp
__device__ bool pattern_char_match(const char* pattern, const char* str) {
    const char* p = pattern;
    const char* s = str;
    const char* last_matched = nullptr;
    bool continuous_matched = false;
    
    while (p < pattern_end && s < str_end) {
        if (*p == *s) {
            if (!continuous_matched && last_matched && last_matched + 1 == s) {
                continuous_matched = true;
            }
            last_matched = s;
            ++p;
            
            if (continuous_matched) {
                // 快速匹配剩余字符
                ++s;
                while (p < pattern_end && s < str_end) {
                    if (*p == *s) ++p;
                    ++s;
                }
                break;
            }
        }
        ++s;
    }
    
    return (p == pattern_end) && continuous_matched;
}
```

### 3.3 中文拼音转换

#### 3.3.1 拼音字典

系统内置了完整的汉字拼音转换字典：

```cpp
// 395个拼音字符串
__device__ constexpr char spell_dict[396][7] = {
    "a", "ai", "an", "ang", "ao", "ba", "bai", "ban", "bang", "bao",
    // ... 完整的拼音字典
};

// 对应的汉字编码值
__device__ constexpr int spell_value[] = {
    -20319, -20317, -20304, -20295, -20292, -20283, -20265,
    // ... 完整的编码值数组
};
```

#### 3.3.2 拼音转换算法

```cpp
__device__ void convert_to_pinyin(
    const char* chinese_str, 
    char* output_str,
    const size_t output_size, 
    char* pinyin_initials, 
    const size_t pinyin_init_size
) {
    const auto length = strlen_cuda(chinese_str);
    size_t out_len = 0;
    size_t initials_len = 0;

    for (size_t j = 0; j < length;) {
        const unsigned char val = chinese_str[j];
        
        if (val < 128) {
            // 非汉字字符直接复制
            str_add_single(output_str, chinese_str[j], &out_len, output_size);
            str_add_single(pinyin_initials, chinese_str[j], &initials_len, pinyin_init_size);
            ++j;
            continue;
        }

        // 汉字处理
        const int chrasc = chinese_str[j] * 256 + chinese_str[j + 1] + 256;
        
        // 查找拼音字典
        for (int i = sizeof spell_value / sizeof spell_value[0] - 1; i >= 0; --i) {
            if (spell_value[i] <= chrasc) {
                const char* pinyin = spell_dict[i];
                const size_t pinyin_len = strlen_cuda(pinyin);

                // 添加完整拼音
                if (out_len + pinyin_len < output_size - 1) {
                    strcpy_cuda(output_str + out_len, pinyin);
                    out_len += pinyin_len;
                }

                // 添加拼音首字母
                if (initials_len < pinyin_init_size - 1) {
                    pinyin_initials[initials_len++] = pinyin[0];
                    pinyin_initials[initials_len] = '\0';
                }
                break;
            }
        }
        j += 2; // 汉字双字节
    }
}
```

## 4. 缓存机制

### 4.1 分层缓存设计

#### 4.1.1 缓存架构

1. **主缓存(Main Cache)**: 存储文件名和索引信息
2. **全局缓存(Global Cache)**: 存储去重的父路径信息

### 4.2 动态字符串存储机制

#### 4.2.1 存储架构设计

CUDA加速器采用了一种高效的动态字符串存储机制，避免了定长字符串数组的内存浪费问题。该机制的核心思想是将所有字符串连续存储在一个大的内存块中，通过地址数组和长度数组来管理每个字符串的位置和大小。

```
GPU内存布局示例:
┌─────────────────────────────────────────────────────────────┐
│                    dev_strs (连续字符串存储区)                   │
├─────────────────────────────────────────────────────────────┤
│ "test.txt\0" │ "document.pdf\0" │ "image.jpg\0" │ "..." │   │
│   (9字节)    │     (12字节)     │   (10字节)   │       │空白│
└─────────────────────────────────────────────────────────────┘
     ↑              ↑                ↑
   addr[0]        addr[1]          addr[2]
   len[0]=9       len[1]=12        len[2]=10
```

#### 4.2.2 核心数据结构

```cpp
struct cache_data {
    // 主存储区域
    char* dev_strs;                        // GPU显存中的连续字符串存储区
    size_t str_total_bytes;                // 存储区总大小
    size_t str_remain_blank_bytes;         // 剩余可用空间
    
    // 索引管理
    std::atomic_uint64_t record_num;       // 当前字符串数量
    size_t* dev_str_addr;                  // 字符串起始地址数组
    size_t str_addr_capacity;              // 地址数组容量
    unsigned short* str_length_array;      // 字符串长度数组
    
    // 路径优化
    size_t* global_parent_path_index_array; // 父路径全局缓存索引
    
    // 去重管理
    concurrent_unordered_set<size_t> record_hash; // 字符串哈希集合
};
```

#### 4.2.3 字符串存储原理

**1. 连续内存分配**

所有字符串被存储在一个连续的GPU内存块(`dev_strs`)中，每个字符串以`\0`结尾：

```cpp
void create_and_insert_cache(const std::vector<std::string>& records_vec, 
                            const size_t total_bytes, const std::string& key) {
    auto cache = new list_cache;
    
    // 计算总需要的内存大小（包括预留空间）
    const auto alloc_bytes = total_bytes + CACHE_REMAIN_BLANK_SIZE_IN_BYTES;
    
    // 分配连续的GPU内存块
    gpuErrchk(cudaMalloc(&cache->str_data.dev_strs, alloc_bytes), true, nullptr);
    gpuErrchk(cudaMemset(cache->str_data.dev_strs, 0, alloc_bytes), true, nullptr);
    
    cache->str_data.str_total_bytes = alloc_bytes;
    cache->str_data.str_remain_blank_bytes = CACHE_REMAIN_BLANK_SIZE_IN_BYTES;
}
```

**2. 地址索引机制**

每个字符串在连续内存中的起始地址被记录在`dev_str_addr`数组中：

```cpp
// 地址数组的容量计算
const auto str_addr_capacity = record_count + CACHE_REMAIN_BLANK_SIZE_IN_BYTES / MAX_PATH_LENGTH;

// 分配地址数组
gpuErrchk(cudaMalloc(&cache->str_data.dev_str_addr, str_addr_capacity * sizeof(size_t)), true, nullptr);

// 分配长度数组（主机内存）
cache->str_data.str_length_array = new unsigned short[str_addr_capacity]();
```

**3. 字符串插入过程**

```cpp
// 字符串插入的详细过程
cudaStream_t stream;
gpuErrchk(cudaStreamCreate(&stream), true, nullptr);

auto target_addr = cache->str_data.dev_strs;  // 当前插入位置
auto save_str_addr_ptr = cache->str_data.dev_str_addr;  // 地址数组指针

for (size_t i = 0; i < records_vec.size(); ++i) {
    const std::string& record_full_path = records_vec[i];
    
    // 提取文件名部分
    char file_name[MAX_PATH_LENGTH];
    global_cache::get_file_name(record_full_path.c_str(), file_name);
    const auto file_name_length = strlen(file_name);
    
    // 异步复制字符串到GPU内存
    gpuErrchk(cudaMemcpyAsync(target_addr, file_name, file_name_length, 
                             cudaMemcpyHostToDevice, stream), true, nullptr);
    
    // 记录字符串的起始地址
    const auto str_address = reinterpret_cast<size_t>(target_addr);
    gpuErrchk(cudaMemcpyAsync(save_str_addr_ptr, &str_address, sizeof(size_t), 
                             cudaMemcpyHostToDevice, stream), true, nullptr);
    
    // 记录字符串长度
    cache->str_data.str_length_array[i] = static_cast<unsigned short>(file_name_length);
    
    // 更新指针位置（+1是为了\0终止符）
    target_addr += file_name_length + 1;
    ++save_str_addr_ptr;
}
```

#### 4.2.4 动态扩容机制

当需要添加新字符串但地址数组容量不足时，系统会自动扩容：

```cpp
void add_records_to_cache(const std::string& key, const std::vector<std::string>& records) {
    auto cache = cache_map.find(key)->second;
    
    for (const auto& record_full_path : records) {
        const auto index = cache->str_data.record_num.load();
        
        // 检查地址数组是否需要扩容
        if (cache->str_data.str_addr_capacity <= index) {
            // 计算新的容量
            const auto new_str_addr_capacity = cache->str_data.str_addr_capacity + 10;
            const auto new_str_addr_alloc_size = new_str_addr_capacity * sizeof(size_t);
            
            // 重新分配GPU地址数组
            size_t* new_dev_str_addr;
            gpuErrchk(cudaMallocAsync(&new_dev_str_addr, new_str_addr_alloc_size, stream), true, nullptr);
            gpuErrchk(cudaMemsetAsync(new_dev_str_addr, 0, new_str_addr_alloc_size, stream), true, nullptr);
            
            // 复制旧数据到新数组
            gpuErrchk(cudaMemcpyAsync(new_dev_str_addr, cache->str_data.dev_str_addr,
                                     cache->str_data.str_addr_capacity * sizeof(size_t), 
                                     cudaMemcpyDeviceToDevice, stream), true, nullptr);
            
            // 释放旧数组，更新指针
            gpuErrchk(cudaFreeAsync(cache->str_data.dev_str_addr, stream), true, nullptr);
            cache->str_data.dev_str_addr = new_dev_str_addr;
            
            // 扩容主机端数组
            auto* new_str_length_array = new unsigned short[new_str_addr_capacity]();
            auto* new_global_cache_array = new size_t[new_str_addr_capacity]();
            
            // 复制旧数据
            for (size_t i = 0; i < cache->str_data.str_addr_capacity; ++i) {
                new_str_length_array[i] = cache->str_data.str_length_array[i];
                new_global_cache_array[i] = cache->str_data.global_parent_path_index_array[i];
            }
            
            // 更新指针和容量
            delete[] cache->str_data.str_length_array;
            delete[] cache->str_data.global_parent_path_index_array;
            cache->str_data.str_length_array = new_str_length_array;
            cache->str_data.global_parent_path_index_array = new_global_cache_array;
            cache->str_data.str_addr_capacity = new_str_addr_capacity;
        }
        
        // 继续添加新字符串的逻辑...
    }
}
```

#### 4.2.5 字符串访问机制

在GPU内核中访问字符串时，通过索引获取地址和长度：

```cpp
__global__ void check(const size_t* str_address_records, ...) {
    const size_t thread_id = GET_TID();
    
    // 通过线程ID获取对应字符串的地址
    const auto file_name_str = reinterpret_cast<const char*>(str_address_records[thread_id]);
    
    // 字符串长度在主机端的length_array中管理
    // 在实际使用中，通过strlen_cuda或预存的长度信息获取
    if (file_name_str == nullptr || !file_name_str[0] || strlen_cuda(file_name_str) >= MAX_PATH_LENGTH) {
        output[thread_id] = 0;
        return;
    }
    
    // 进行字符串匹配操作...
}
```

#### 4.2.6 内存效率优化

**1. 空间预留策略**

```cpp
// 为每个缓存预留额外空间，减少频繁重新分配
#define CACHE_REMAIN_BLANK_SIZE_IN_BYTES 30000

// 计算地址数组容量时考虑预留空间
const auto str_addr_capacity = record_count + CACHE_REMAIN_BLANK_SIZE_IN_BYTES / MAX_PATH_LENGTH;
```

**2. 内存碎片管理**

由于字符串长度不同，删除操作可能产生内存碎片。系统采用重建缓存的方式解决：

```cpp
void remove_records_from_cache(const std::string& key, std::vector<std::string>& records) {
    auto cache = cache_map.find(key)->second;
    
         // 读取所有现有字符串到主机内存（参考实际代码逻辑）
     std::vector<std::string> remain_strs;
     size_t new_total_bytes = 0;
     
     const auto tmp_records = new char[MAX_PATH_LENGTH * cache->str_data.record_num.load()]();
     cudaStream_t stream;
     gpuErrchk(cudaStreamCreate(&stream), true, nullptr);
     
     for (size_t i = 0; i < cache->str_data.record_num.load(); ++i) {
         char* str_address;
         char* tmp = tmp_records + MAX_PATH_LENGTH * i;
         gpuErrchk(cudaMemcpyAsync(&str_address, cache->str_data.dev_str_addr + i,
                                  sizeof(size_t), cudaMemcpyDeviceToHost, stream), true, nullptr);
         gpuErrchk(cudaMemcpyAsync(tmp, str_address, cache->str_data.str_length_array[i],
                                  cudaMemcpyDeviceToHost, stream), true, nullptr);
     }
     
     gpuErrchk(cudaStreamSynchronize(stream), true, nullptr);
     
     for (size_t i = 0; i < cache->str_data.record_num.load(); ++i) {
         const auto parent_path_hash = cache->str_data.global_parent_path_index_array[i];
         const auto parent_path = global_cache::copy_to_host_by_hash(parent_path_hash);
         
         char* tmp = tmp_records + MAX_PATH_LENGTH * i;
         const auto full_record_path = parent_path + tmp;
         const auto& record = std::find(records.begin(), records.end(), full_record_path);
         
         if (record == records.end()) {
             new_total_bytes += (strlen(tmp) + 1);
             remain_strs.emplace_back(full_record_path);
         }
     }
     
     delete[] tmp_records;
     gpuErrchk(cudaStreamDestroy(stream), true, nullptr);
    
    // 清理旧缓存
    clear_cache(key);
    
    // 重新创建紧凑的缓存
    create_and_insert_cache(remain_strs, new_total_bytes, key);
}
```

#### 4.2.7 完整内存布局示例

为了更好地理解动态字符串存储机制，以下是一个完整的内存布局示例：

```
假设有3个文件: "test.txt", "document.pdf", "a.jpg"

=== GPU内存区域 (dev_strs) ===
地址: 0x10000000
┌─────────────────────────────────────────────────────────────┐
│ t│e│s│t│.│t│x│t│\0│d│o│c│u│m│e│n│t│.│p│d│f│\0│a│.│j│p│g│\0│ │
└─────────────────────────────────────────────────────────────┘
  0 1 2 3 4 5 6 7 8  9 10.....................21 22 23 24 25 26 27

=== GPU地址数组 (dev_str_addr) ===
地址: 0x20000000
┌─────────────────┬─────────────────┬─────────────────┐
│  0x10000000     │  0x10000009     │  0x10000016     │  <- 字符串起始地址
│   (test.txt)    │ (document.pdf)  │    (a.jpg)      │
└─────────────────┴─────────────────┴─────────────────┘
     addr[0]           addr[1]           addr[2]

=== 主机内存长度数组 (str_length_array) ===
┌─────┬─────┬─────┐
│  8  │ 12  │  5  │  <- 字符串长度（不包括\0）
└─────┴─────┴─────┘
len[0] len[1] len[2]

=== 主机内存父路径索引 (global_parent_path_index_array) ===
┌─────────┬─────────┬─────────┐
│ hash1   │ hash2   │ hash3   │  <- 父路径在全局缓存中的哈希
└─────────┴─────────┴─────────┘
  path[0]   path[1]   path[2]

=== 全局缓存 (global_cache) ===
存储去重的父路径: "C:\Users\<USER>\Documents\", "E:\Images\"
通过哈希映射到具体的GPU内存地址
```

#### 4.2.8 动态字符串访问流程

```cpp
// 参考实际check内核函数中的字符串访问流程
__global__ void check(const size_t* str_address_records,
                     const size_t* global_path_cache,
                     const size_t* total_num,
                     // ... 其他参数
                     char* output) {
    const size_t thread_id = GET_TID();
    if (thread_id >= *total_num) {
        return;
    }
    
    // 步骤1: 获取文件名地址
    const auto file_name_str = reinterpret_cast<const char*>(str_address_records[thread_id]);
    if (file_name_str == nullptr || !file_name_str[0] || strlen_cuda(file_name_str) >= MAX_PATH_LENGTH) {
        output[thread_id] = 0;
        return;
    }
    
    // 步骤2: 获取父路径地址
    const auto file_path_str = reinterpret_cast<const char*>(global_path_cache[thread_id]);
    if (file_path_str == nullptr || !file_path_str[0] || strlen_cuda(file_path_str) >= MAX_PATH_LENGTH) {
        output[thread_id] = 0;
        return;
    }
    
    // 步骤3: 进行匹配检查
    if (not_matched(file_name_str, file_path_str, *is_ignore_case, keywords,
                   keywords_lower_case, static_cast<int>(*keywords_length),
                   is_keyword_path, is_enable_fuzzy_match)) {
        output[thread_id] = 0;
        return;
    }
    
    // 步骤4: 设置匹配结果
    output[thread_id] = 1;
}
```

#### 4.2.9 性能优势

**1. 内存使用效率**

- **空间节省**: 避免定长数组的空间浪费，实际文件名长度通常远小于MAX_PATH_LENGTH
- **动态分配**: 可节省60-80%的内存空间
- **去重优化**: 通过全局缓存避免重复存储相同的父路径

**2. 访问性能**

- **连续内存布局**: 提高GPU缓存命中率和内存带宽利用率
- **合并访问**: GPU线程可以进行合并的内存访问操作
- **减少分配开销**: 大块内存分配减少GPU内存管理开销

**3. 可扩展性**

- **动态增长**: 支持运行时动态添加/删除字符串
- **自动扩容**: 地址数组自动扩容机制
- **线性扩展**: 内存使用量随数据量线性增长，无额外开销

**4. 并发安全**

- **原子操作**: 使用原子变量管理记录数量
- **读写分离**: 读取操作不影响写入操作的性能
- **线程安全**: 支持多线程并发访问和修改

#### 4.1.2 缓存数据结构

```cpp
// 主缓存管理器
concurrency::concurrent_unordered_map<std::string, list_cache*> cache_map;

// 全局缓存单例
static global_cache::global_cache_data global_cache_obj;

// 缓存同步锁
std::shared_mutex cache_lock;
```

### 4.2 内存管理策略

#### 4.2.1 动态内存分配

```cpp
void create_and_insert_cache(
    const std::vector<std::string>& records_vec, 
    const size_t total_bytes,
    const std::string& key
) {
    auto cache = new list_cache;
    cache->str_data.record_num = records_vec.size();

    // 分配GPU内存
    const auto alloc_bytes = total_bytes + CACHE_REMAIN_BLANK_SIZE_IN_BYTES;
    gpuErrchk(cudaMalloc(&cache->str_data.dev_strs, alloc_bytes), true, nullptr);
    gpuErrchk(cudaMemset(cache->str_data.dev_strs, 0, alloc_bytes), true, nullptr);

    // 分配地址数组
    const auto str_addr_capacity = record_count + CACHE_REMAIN_BLANK_SIZE_IN_BYTES / MAX_PATH_LENGTH;
    const auto str_addr_alloc_size = str_addr_capacity * sizeof(size_t);
    gpuErrchk(cudaMalloc(&cache->str_data.dev_str_addr, str_addr_alloc_size), true, nullptr);

    // 分配长度数组
    cache->str_data.str_length_array = new unsigned short[str_addr_capacity]();
    cache->str_data.global_parent_path_index_array = new size_t[str_addr_capacity]();
}
```

#### 4.2.2 缓存扩容机制

```cpp
void add_records_to_cache(const std::string& key, const std::vector<std::string>& records) {
    auto&& cache_iter = cache_map.find(key);
    if (cache_iter != cache_map.end()) {
        auto cache = cache_iter->second;
        
        // 检查是否需要扩容地址数组
        if (cache->str_data.str_addr_capacity <= index) {
            const auto new_str_addr_capacity = cache->str_data.str_addr_capacity + 10;
            const auto new_str_addr_alloc_size = new_str_addr_capacity * sizeof(size_t);
            
            // 重新分配GPU内存
            size_t* new_dev_str_addr;
            gpuErrchk(cudaMallocAsync(&new_dev_str_addr, new_str_addr_alloc_size, stream), true, nullptr);
            gpuErrchk(cudaMemcpyAsync(new_dev_str_addr, cache->str_data.dev_str_addr,
                cache->str_data.str_addr_capacity * sizeof(size_t), cudaMemcpyDeviceToDevice, stream), true, nullptr);
            
            // 释放旧内存
            gpuErrchk(cudaFreeAsync(cache->str_data.dev_str_addr, stream), true, nullptr);
            cache->str_data.dev_str_addr = new_dev_str_addr;
            
            // 重新分配主机内存
            auto* new_str_length_array = new unsigned short[new_str_addr_capacity]();
            auto* new_global_cache_array = new size_t[new_str_addr_capacity]();
            
            // 复制旧数据
            for (size_t i = 0; i < cache->str_data.str_addr_capacity; ++i) {
                new_str_length_array[i] = cache->str_data.str_length_array[i];
                new_global_cache_array[i] = cache->str_data.global_parent_path_index_array[i];
            }
            
            // 释放旧数组
            delete[] cache->str_data.str_length_array;
            delete[] cache->str_data.global_parent_path_index_array;
            
            // 更新指针
            cache->str_data.str_length_array = new_str_length_array;
            cache->str_data.global_parent_path_index_array = new_global_cache_array;
            cache->str_data.str_addr_capacity = new_str_addr_capacity;
        }
    }
}
```

### 4.3 全局缓存优化

#### 4.3.1 路径去重机制

```cpp
void global_cache::insert(const std::vector<std::string>& str_vec) {
    std::unique_lock wck(shared_mutex_);
    
    for (const auto& str : str_vec) {
        const auto str_hash = str_hasher(str);
        
        // 检查是否已存在
        if (global_cache_obj.unique_hash_index_map.find(str_hash) ==
            global_cache_obj.unique_hash_index_map.end()) {
            
            // 添加新字符串到GPU内存
            global_cache_obj.unique_hash_index_map.insert(std::make_pair(str_hash, i));
            gpuErrchk(cudaMemcpyAsync(
                reinterpret_cast<void*>(global_cache_obj.blank_start_address),
                str.c_str(), str.length(), cudaMemcpyHostToDevice, stream), true, nullptr);
            
            // 更新索引信息
            global_cache_obj.dev_str_len_vec.emplace_back(static_cast<unsigned short>(str.length()));
            global_cache_obj.dev_str_start_address.emplace_back(global_cache_obj.blank_start_address);
            global_cache_obj.blank_start_address += (str.length() + 1);
        }
    }
}
```

## 5. 性能优化

### 5.1 GPU内存优化

#### 5.1.1 异步内存操作

```cpp
void start_kernel(...) {
    // 创建CUDA流数组
    auto* streams = new cudaStream_t[map_size];
    for (size_t i = 0; i < map_size; ++i) {
        gpuErrchk(cudaStreamCreate(&(streams[i])), true, nullptr);
    }
    
    // 异步启动内核
    for (auto&& each : cache_map) {
        check<<<grid_size, block_size, 0, streams[count]>>>(
            cache->str_data.dev_str_addr,
            global_path_mapping_array,
            dev_total_number,
            // ... 其他参数
        );
        
        // 异步回调
        gpuErrchk(cudaLaunchHostFunc(streams[count], cudaCallback, cache), true, nullptr);
        ++count;
    }
    
    // 同步所有流
    for (size_t i = 0; i < map_size; ++i) {
        gpuErrchk(cudaStreamSynchronize(streams[i]), true, nullptr);
    }
}
```

### 5.2 线程池优化

#### 5.2.1 动态字符串结果收集

在结果收集阶段，系统需要从GPU的动态字符串存储中读取匹配的结果。这个过程涉及复杂的内存访问和字符串重构：

```cpp
void collect_results(
    std::atomic_uint& result_counter,
    const unsigned max_results,
    const std::vector<std::string>& search_case_vec,
    Concurrency::concurrent_vector<collected_result_data>& collected_results
) {
    for (const auto& [key, cache_struct] : cache_map) {
        if (!cache_struct->is_cache_valid || !cache_struct->is_match_done.load()) {
            continue;
        }
        
        // 检查并设置输出状态（原子操作）
        if (int expected = 0; !cache_struct->is_output_done.compare_exchange_strong(expected, 1)) {
            continue;
        }
        
        unsigned matched_number = 0;
        
        // 复制GPU结果位图到主机内存
        const auto output_ptr = new char[cache_struct->output_bitmap_size]();
        gpuErrchk(cudaMemcpy(output_ptr, cache_struct->dev_output_bitmap, 
                            cache_struct->output_bitmap_size, cudaMemcpyDeviceToHost), true, nullptr);
        
        // 遍历所有可能的匹配结果
        for (size_t i = 0; i < cache_struct->output_bitmap_size; ++i) {
            if (stop_func()) break;
            
            // 检查该位置是否匹配成功
            if (static_cast<bool>(output_ptr[i])) {
                // 步骤1: 获取文件名字符串地址
                char* str_address = nullptr;
                gpuErrchk(cudaMemcpy(&str_address, cache_struct->str_data.dev_str_addr + i, 
                                    sizeof(size_t), cudaMemcpyDeviceToHost), false, nullptr);
                
                if (str_address == nullptr) continue;
                
                // 步骤2: 获取字符串长度
                const auto str_len = cache_struct->str_data.str_length_array[i];
                
                // 步骤3: 复制文件名到主机内存
                char matched_record_str_file_name[MAX_PATH_LENGTH]{0};
                gpuErrchk(cudaMemcpy(matched_record_str_file_name, str_address, str_len,
                                    cudaMemcpyDeviceToHost), false, "collect results failed");
                
                // 步骤4: 获取父路径信息
                const auto parent_path_hash = cache_struct->str_data.global_parent_path_index_array[i];
                const auto parent_path = global_cache::copy_to_host_by_hash(parent_path_hash);
                
                // 步骤5: 重构完整路径
                const auto matched_record_str = parent_path + matched_record_str_file_name;
                
                // 步骤6: 根据搜索条件过滤结果（实际代码逻辑）
                const auto _collect_func = [&](const std::string& _key, const std::string& _matched_record_str,
                    unsigned* matched_number) {
                    ++result_counter;
                    if (result_counter.load() > max_results) {
                        is_results_number_exceed = true;
                    }
                    collected_results.push_back(collected_result_data{_key, _matched_record_str});
                    ++*matched_number;
                };
                
                // 判断文件和文件夹
                if (search_case_vec.empty()) {
                    _collect_func(key, matched_record_str, &matched_number);
                } else {
                    if (std::find(search_case_vec.begin(), search_case_vec.end(), "f") != search_case_vec.end()) {
                        if (is_dir_or_file(matched_record_str.c_str()) == 1) {
                            _collect_func(key, matched_record_str, &matched_number);
                        }
                    } else if (std::find(search_case_vec.begin(), search_case_vec.end(), "d") != search_case_vec.end()) {
                        if (is_dir_or_file(matched_record_str.c_str()) == 0) {
                            _collect_func(key, matched_record_str, &matched_number);
                        }
                    } else {
                        _collect_func(key, matched_record_str, &matched_number);
                    }
                }
            }
        }
        
        // 更新匹配数量并标记完成
        cache_struct->matched_number = matched_number;
        cache_struct->is_output_done = 2;
        delete[] output_ptr;
    }
}

// 实际代码中的文件/文件夹判断逻辑（在collect_results函数内部）
if (search_case_vec.empty()) {
    _collect_func(key, matched_record_str, &matched_number);
} else {
    if (std::find(search_case_vec.begin(), search_case_vec.end(), "f") != search_case_vec.end()) {
        if (is_dir_or_file(matched_record_str.c_str()) == 1) {
            _collect_func(key, matched_record_str, &matched_number);
        }
    } else if (std::find(search_case_vec.begin(), search_case_vec.end(), "d") != search_case_vec.end()) {
        if (is_dir_or_file(matched_record_str.c_str()) == 0) {
            _collect_func(key, matched_record_str, &matched_number);
        }
    } else {
        _collect_func(key, matched_record_str, &matched_number);
    }
}
```

#### 5.2.2 内存访问特点

结果收集过程中的实际内存访问模式：

**1. 位图驱动的访问**

系统首先复制结果位图到主机内存，然后基于位图来决定访问哪些字符串：

```cpp
// 从实际collect_results函数中的核心逻辑
const auto output_ptr = new char[cache_struct->output_bitmap_size]();
gpuErrchk(cudaMemcpy(output_ptr, cache_struct->dev_output_bitmap, 
                    cache_struct->output_bitmap_size, cudaMemcpyDeviceToHost), true, nullptr);

for (size_t i = 0; i < cache_struct->output_bitmap_size; ++i) {
    if (static_cast<bool>(output_ptr[i])) {  // 只处理匹配成功的结果
        // 逐个获取字符串地址和内容
        char* str_address = nullptr;
        gpuErrchk(cudaMemcpy(&str_address, cache_struct->str_data.dev_str_addr + i, 
                            sizeof(size_t), cudaMemcpyDeviceToHost), false, nullptr);
        
        // 复制字符串内容
        const auto str_len = cache_struct->str_data.str_length_array[i];
        gpuErrchk(cudaMemcpy(matched_record_str_file_name, str_address, str_len,
                            cudaMemcpyDeviceToHost), false, "collect results failed");
    }
}
```

**2. 按需访问策略**

只对匹配成功的字符串进行内存传输，避免传输未匹配的数据：
- 减少了不必要的GPU-CPU数据传输
- 提高了内存带宽的利用效率
- 降低了系统的整体延迟

#### 5.2.3 结果收集线程池

系统使用BS::thread_pool实现多线程结果收集：

```cpp
// 线程池的初始化（从实际代码中）
static BS::thread_pool<>* collect_results_thread_pool_ptr;
static std::once_flag init_thread_pool_flag;

std::call_once(init_thread_pool_flag, [&result_collect_thread_num]() {
    collect_results_thread_pool_ptr = new BS::thread_pool(result_collect_thread_num);
});

// 提交多个结果收集任务
std::vector<std::future<void>> future_vec;
Concurrency::concurrent_vector<collected_result_data> collected_results_vec;

for (auto i = 0; i < result_collect_thread_num; ++i) {
    future_vec.emplace_back(collect_results_thread_pool_ptr->submit_task([&] {
        collect_results(result_counter, max_results, search_case_vec, collected_results_vec);
    }));
}

// 等待所有任务完成
for (auto&& each_future : future_vec) {
    each_future.wait();
}
```

这种设计允许多个线程并行处理不同缓存的结果收集，提高了整体性能。

### 5.3 内存使用监控

#### 5.3.1 GPU内存使用统计

```cpp
JNIEXPORT jint JNICALL Java_file_engine_dllInterface_gpu_CudaAccelerator_getGPUMemUsage(JNIEnv*, jobject) {
    size_t free;
    size_t total;
    gpuErrchk(cudaMemGetInfo(&free, &total), true, "Get memory info failed");
    
    auto&& total_mem = total;
    auto&& memory_used = get_device_memory_used();
    
    if (memory_used == INFINITE) {
        return 100;
    }
    
    return static_cast<jint>(memory_used * 100 / total_mem);
}
```

#### 5.3.2 设备内存查询

```cpp
size_t get_device_memory_used() {
    cudaDeviceProp prop;
    gpuErrchk(cudaGetDeviceProperties(&prop, current_using_device), true, "Get device info failed.");
    
    auto&& device_name = prop.name;
    auto&& device_name_wstr = string2wstring(device_name);
    auto&& dxgi_device = gpu_name_adapter_map.find(device_name_wstr);
    
    if (dxgi_device == gpu_name_adapter_map.end()) {
        return INFINITE;
    }
    
    // 通过DXGI接口查询GPU内存使用情况
    auto&& adapter_luid = dxgi_device->second.AdapterLuid;
    auto&& luid_str = "0x" + n2hexstr(adapter_luid.HighPart) + "_" + "0x" + n2hexstr(adapter_luid.LowPart);
    
    PDH_STATUS status;
    auto&& memory_map = query_pdh_val(status);
    
    for (auto& [gpu_name, memory_used] : memory_map) {
        if (gpu_name.find(luid_wstr) != std::wstring::npos) {
            return memory_used;
        }
    }
    
    return INFINITE;
}
```

## 6. 错误处理与监控

### 6.1 CUDA错误处理

#### 6.1.1 错误检查宏

```cpp
#define gpuErrchk(ans, is_exit, info) gpuAssert((ans), __FILE__, __LINE__, __FUNCTION__, (is_exit), (info))

inline void gpuAssert(cudaError_t code, const char* file, int line, const char* function, bool is_exit, const char* info) {
    if (code != cudaSuccess) {
        if (info == nullptr) {
            fprintf(stderr, "GPU assert: %s %s %d %s\n", cudaGetErrorString(code), file, line, function);
        } else {
            fprintf(stderr, "GPU assert: %s %s %d %s %s\n", cudaGetErrorString(code), file, line, function, info);
        }
        
        if (is_exit) {
            send_restart_event();
        }
    }
}
```

#### 6.1.2 重启事件机制

```cpp
void send_restart_event() {
    if (!error_flag) {
        error_flag = true;
        fprintf(stderr, "Send RestartEvent.\n");
        
        JNIEnv* env = nullptr;
        JavaVMAttachArgs args{JNI_VERSION_10, nullptr, nullptr};
        
        if (jvm_ptr->AttachCurrentThread(reinterpret_cast<void**>(&env), &args) != JNI_OK) {
            fprintf(stderr, "get thread JNIEnv ptr failed");
            return;
        }
        
        auto&& gpu_class = env->FindClass("file.engine.dllInterface.gpu.GPUAccelerator");
        auto&& restart_method = env->GetMethodID(gpu_class, "sendRestartOnError0", "()V");
        env->CallStaticVoidMethod(gpu_class, restart_method);
        
        env->DeleteLocalRef(gpu_class);
        jvm_ptr->DetachCurrentThread();
    }
}
```

### 6.2 资源管理

#### 6.2.1 优雅关闭机制

```cpp
void release_all() {
    clear_all_cache();
    free_cuda_search_memory();
    free_stop_signal();
    global_cache::clear();
}

void clear_all_cache() {
    std::vector<std::string> all_keys_vec;
    for (auto& [key, _] : cache_map) {
        all_keys_vec.emplace_back(key);
    }
    
    for (auto& key : all_keys_vec) {
        clear_cache(key);
    }
}
```

#### 6.2.2 内存泄漏防护

```cpp
void clear_cache(const std::string& key) {
    try {
        const auto cache = cache_map.at(key);
        cache->is_cache_valid = false;
        
        // 释放GPU内存
        gpuErrchk(cudaFree(cache->str_data.dev_strs), false, get_cache_info(key, cache).c_str());
        gpuErrchk(cudaFree(cache->str_data.dev_str_addr), false, get_cache_info(key, cache).c_str());
        
        // 释放主机内存
        delete[] cache->str_data.str_length_array;
        delete[] cache->str_data.global_parent_path_index_array;
        delete cache;
        
        // 从缓存映射中删除
        cache_map.unsafe_erase(key);
    } catch (std::out_of_range&) {
        // 缓存不存在，忽略
    } catch (std::exception& e) {
        fprintf(stderr, "clear cache failed: %s\n", e.what());
    }
}
```

## 7. 开发指南

### 7.1 环境配置

#### 7.1.1 开发环境要求

- **CUDA Toolkit**: 11.0或更高版本
- **Visual Studio**: 2019或更高版本
- **GPU设备**: 支持CUDA的NVIDIA GPU（计算能力3.5+）
- **内存要求**: 至少4GB GPU显存

#### 7.1.2 编译配置

```xml
<!-- cudaAccelerator.vcxproj -->
<ItemGroup>
  <CudaCompile Include="kernels.cu">
    <FileType>Document</FileType>
  </CudaCompile>
  <CudaCompile Include="str_utils.cu" />
  <CudaCompile Include="str_convert.cu" />
</ItemGroup>
```

## 9. 常见问题

### 9.1 编译问题

**Q: 编译时出现CUDA版本不兼容错误**

A: 检查CUDA Toolkit版本与Visual Studio版本的兼容性，确保安装了正确的CUDA集成组件。

**Q: 链接错误：找不到cudart.lib**

A: 确保在项目属性中正确配置了CUDA库路径：
```
配置属性 -> VC++目录 -> 库目录 -> 添加CUDA lib路径
配置属性 -> 链接器 -> 输入 -> 附加依赖项 -> 添加cudart.lib
```

---

## 附录

### A. 常用常量定义

```cpp
#define MAX_PATH_LENGTH 500
#define MAX_THREAD_PER_BLOCK 512
#define CACHE_REMAIN_BLANK_SIZE_IN_BYTES 30000
#define MAX_KEYWORDS_NUMBER 150
```

### B. 重要数据结构总结

- `list_cache`: 主缓存结构
- `cache_data`: 缓存数据结构
- `global_cache_data`: 全局缓存结构
- `collected_result_data`: 收集结果结构
- `stop_signal`: 停止信号结构

### C. 核心函数列表

**主机端函数:**
- `start_kernel()`: 启动CUDA内核
- `collect_results()`: 结果收集函数
- `create_and_insert_cache()`: 创建和插入缓存
- `add_records_to_cache()`: 添加记录到缓存
- `remove_records_from_cache()`: 从缓存删除记录
- `clear_cache()`: 清理缓存
- `is_dir_or_file()`: 判断文件/文件夹类型

**设备端函数:**
- `check()`: 主搜索内核函数
- `not_matched()`: 匹配检查函数
- `convert_to_pinyin()`: 拼音转换函数
- `strstr_cuda()`: CUDA字符串查找
- `strlen_cuda()`: CUDA字符串长度计算
- `strcpy_cuda()`: CUDA字符串复制
- `pattern_char_match()`: 模式字符匹配

**全局缓存函数:**
- `global_cache::insert()`: 插入全局缓存
- `global_cache::remove()`: 删除全局缓存
- `global_cache::get_address_by_hash()`: 通过哈希获取地址
- `global_cache::copy_to_host_by_hash()`: 复制到主机内存
- `global_cache::get_file_name()`: 获取文件名
- `global_cache::get_parent_path()`: 获取父路径

---

*本文档版本: 1.0*  
*维护者: File-Engine-Core开发团队*