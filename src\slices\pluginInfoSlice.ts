// pluginInfoSlice.ts
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { PluginInfo } from "../Component/MainAnwser";
import { RootState } from "../store";

interface PluginInfoState {
  value: PluginInfo;
}

const initialState: PluginInfoState = {
  value: null,
};

export const pluginInfoSlice = createSlice({
  name: "pluginInfo",
  initialState,
  reducers: {
    setPluginInfo: (state, action: PayloadAction<PluginInfo>) => {
      state.value = action.payload;
    },
  },
});

export const { setPluginInfo } = pluginInfoSlice.actions;

export const selectPluginInfo = (state: RootState) => state.pluginInfo.value;

export default pluginInfoSlice.reducer;
