import React from "react";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON>, Folder, Shield } from "lucide-react";
import { Card, CardContent } from "../ui/card";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";

interface CtrlMenuProps {
  isVisible: boolean;
}

const KeyBadge: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Badge variant="secondary" className="px-2 py-1 text-xs font-mono">
    {children}
  </Badge>
);

const MenuItem: React.FC<{
  icon: React.ReactNode;
  label: string;
  keys: string[];
  delay: string;
  isVisible: boolean;
}> = ({ icon, label, keys, delay, isVisible }) => (
  <Button
    variant="ghost"
    className={`w-full justify-between p-3 h-auto transition-all duration-200 ${
      isVisible ? "animate-itemPopup" : "opacity-0"
    }`}
    style={{
      animationDelay: isVisible ? delay : "0s",
      animationFillMode: "both",
    }}
  >
    <div className="flex items-center gap-3">
      {icon}
      <span className="font-medium text-sm">{label}</span>
    </div>
    <div className="flex items-center gap-1">
      {keys.map((key) => (
        <KeyBadge key={key}>{key}</KeyBadge>
      ))}
    </div>
  </Button>
);

export const CtrlMenu: React.FC<CtrlMenuProps> = ({ isVisible }) => {
  const { t } = useTranslation();

  return (
    <Card
      className={`absolute bottom-0 -left-1 z-20 min-w-[280px] transition-all duration-200 ${
        isVisible
          ? "opacity-100 animate-materialPopup pointer-events-auto"
          : "opacity-0 pointer-events-none scale-85 translate-y-2"
      }`}
      style={{
        transformOrigin: "bottom left",
        boxShadow:
          "0 8px 32px rgba(0, 0, 0, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1)",
      }}
    >
      <CardContent className="p-2 space-y-1">
        <MenuItem
          icon={<Copy className="w-4 h-4" />}
          label={t("searchBar.copyPath")}
          keys={["Ctrl", "C"]}
          delay="0.3s"
          isVisible={isVisible}
        />
        <MenuItem
          icon={<Folder className="w-4 h-4" />}
          label={t("searchBar.openParentDirectory")}
          keys={["Ctrl", "P"]}
          delay="0.2s"
          isVisible={isVisible}
        />
        <MenuItem
          icon={<Shield className="w-4 h-4" />}
          label={t("searchBar.openWithAdmin")}
          keys={["Ctrl", "O"]}
          delay="0.1s"
          isVisible={isVisible}
        />
      </CardContent>
    </Card>
  );
};
