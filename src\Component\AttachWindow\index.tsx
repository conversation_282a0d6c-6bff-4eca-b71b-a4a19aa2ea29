import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setInputValue, selectInputValue } from "../../slices/inputValueSlice";
import CompactFileList from "../CompactFileList";
import {
  QueryResponse,
  Data,
  AiverthingCoreResponse,
  DataType,
} from "../MainAnwser";
import {
  getResult,
  prepareSearch,
  searchAsync,
  removeResult,
  getCalculatedWindowInfo,
  setExplorerEditPath,
  isShiftDoubleClicked,
  bringWindowToTop,
} from "../../api/aiverything";
import {
  getCurrentWindow,
  LogicalPosition,
  LogicalSize,
} from "@tauri-apps/api/window";
import { useTranslation } from "react-i18next";

const AttachWindow: React.FC = () => {
  const dispatch = useDispatch();
  const inputValue = useSelector(selectInputValue);
  const [queryData, setQueryData] = useState<QueryResponse[]>([]);
  const [selectedFile, setSelectedFile] = useState<Data>();
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const prepareTimeRef = useRef<NodeJS.Timeout | null>(null);
  const lastSearchUuidRef = useRef<string>(null);
  const [windowPosState, setWindowPosState] = useState<any>({});
  const switchingFocus = useRef<boolean>(false);

  const { t } = useTranslation();

  const maxRetryTimes = 20;
  const maxResultNum = 200;
  const maxDisplayResults = 200; // 显示结果的最大数量

  // 限制查询数据大小的函数
  const limitQueryData = (data: QueryResponse[]): QueryResponse[] => {
    if (!data || data.length === 0) return [];

    const limitedData: QueryResponse[] = [];
    let totalCount = 0;

    for (const section of data) {
      if (!section || !section.data) continue;

      const availableSlots = maxDisplayResults - totalCount;
      if (availableSlots <= 0) break;

      const limitedSection = {
        ...section,
        data: section.data.slice(0, availableSlots),
      };

      limitedData.push(limitedSection);
      totalCount += limitedSection.data.length;

      if (totalCount >= maxDisplayResults) break;
    }

    return limitedData;
  };

  const transformCoreDataToData = (coreData, type: DataType): Data => {
    const pathParts = coreData.path.split("/");
    const fileName = pathParts.pop()?.split("\\").pop() as string;
    const extension = pathParts[pathParts.length - 1]?.split(".").pop() || "";

    return {
      key: coreData.path,
      name: fileName,
      icon: "",
      type: type,
      extension: extension,
      size: { number: coreData.fileSize },
      path: coreData.path,
      lastModified: new Date(coreData.modifyDate),
      createdAt: new Date(), // Assuming createdAt is not available in coreData
      metaData: {}, // Assuming metaData is not available in coreData
      highlightPath: coreData.highlightPath,
      highlightFileName: coreData.highlightFileName,
    };
  };

  const transformCoreResponseToQueryResponse = (
    coreResponse: AiverthingCoreResponse
  ): QueryResponse[] => {
    const dataTypes: { [key: string]: DataType } = {
      App: DataType.App,
      Folder: DataType.Folders,
      Doc: DataType.Doc,
      Sheet: DataType.Sheet,
      Slide: DataType.Slide,
      Picture: DataType.Picture,
      Audio: DataType.Audio,
      Video: DataType.Video,
      Developer: DataType.Developer,
      Default: DataType.Others,
    };

    const queryResponses: QueryResponse[] = [];

    coreResponse.data.forEach((eachDataTypeResult) => {
      const coreDataArray = eachDataTypeResult.results;
      const dataType =
        dataTypes[eachDataTypeResult.dataType] || DataType.Others;
      if (coreDataArray.length > 0) {
        const dataArray = coreDataArray.map((eachCoreData) =>
          transformCoreDataToData(eachCoreData, dataType)
        );
        queryResponses.push({
          datatype: dataType,
          data: dataArray,
        });
      }
    });

    return queryResponses;
  };

  const fetchResultsUntilDone = async (uuid: string, retryTimes: number) => {
    if (lastSearchUuidRef.current !== uuid) {
      return;
    }
    const res = await getResult(uuid);
    console.log("True Search with uuid: ", res);
    const newData = transformCoreResponseToQueryResponse(res.data);
    console.log("New data: ", newData);

    // 复用现有数据对象
    // let newDataReuse = [];
    // newData.forEach((item) => {
    //   const dataTypeListReuse = [];
    //   const oldDataTypeResults = queryData.filter(
    //     (each) => each.datatype === item.datatype
    //   );
    //   item.data.forEach((dataItem) => {
    //     const oldData = oldDataTypeResults[0]?.data.find(
    //       (each) => each.path === dataItem.path
    //     );
    //     if (oldData) {
    //       dataTypeListReuse.push(oldData);
    //     } else {
    //       dataTypeListReuse.push(dataItem);
    //     }
    //   });
    //   newDataReuse.push({ datatype: item.datatype, data: dataTypeListReuse });
    // });

    // 应用数据限制
    const limitedData = limitQueryData(newData);
    setQueryData(limitedData);

    if (res.data.isDone || retryTimes === 0) {
      console.log("True Search happened: ", res);
    } else {
      setTimeout(() => {
        fetchResultsUntilDone(uuid, retryTimes - 1);
      }, 150);
    }
  };

  useEffect(() => {
    if (inputValue !== null && inputValue !== undefined && inputValue !== "") {
      // 清除之前的计时器
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      if (prepareTimeRef.current) {
        clearTimeout(prepareTimeRef.current);
      }

      prepareTimeRef.current = setTimeout(async () => {
        console.log("Prepare Search with", inputValue);
        const lastUuidToRemove = lastSearchUuidRef.current;
        if (lastUuidToRemove !== null) {
          await removeResult(lastUuidToRemove);
        }
        const prepareRes = await prepareSearch(inputValue, maxResultNum);
        const res = prepareRes?.data;
        console.log("Search prepared: ", res);
        lastSearchUuidRef.current = res;
        setSelectedFile(null);
        await fetchResultsUntilDone(res, 0);

        if (timerRef.current) {
          clearTimeout(timerRef.current);
        }

        timerRef.current = setTimeout(() => {
          (async () => {
            console.log("True Search with", inputValue);
            const searchRes = await searchAsync(inputValue, maxResultNum);
            const res = searchRes?.data;
            console.log("Start search: ", res);
            if (lastSearchUuidRef.current !== res) {
              lastSearchUuidRef.current = res;
              setSelectedFile(null);
            }
            await fetchResultsUntilDone(res, maxRetryTimes);
          })().catch((err) => {
            console.error("True search failed: ", err);
          });
        }, 400);
      }, 200);

      return () => {
        if (timerRef.current) {
          clearTimeout(timerRef.current);
        }
        if (prepareTimeRef.current) {
          clearTimeout(prepareTimeRef.current);
        }
      };
    } else {
      setQueryData([]);
    }
  }, [inputValue]);

  useEffect(() => {
    const setSizeFun = async (width, height) => {
      const appWindow = getCurrentWindow();
      await appWindow.setResizable(true);

      const scaleFactor = await appWindow.scaleFactor();
      const ratio = window.devicePixelRatio / scaleFactor;

      const newSize = new LogicalSize(width * ratio, height * ratio);
      await appWindow.setSize(newSize);
      await appWindow.setResizable(false);
    };
    if (queryData.length > 0 && inputValue) {
      const calculatedWindowPos = getCalculatedWindowInfo();
      calculatedWindowPos.then((res) => {
        setSizeFun(Math.ceil(res.bar_width), Math.ceil(res.bar_height));
      });
    } else {
      const calculatedWindowPos = getCalculatedWindowInfo();
      calculatedWindowPos.then((res) => {
        setSizeFun(Math.ceil(res.bar_width), Math.ceil(res.textfield_height));
      });
    }
  }, [queryData]);

  useEffect(() => {
    const positionInterval = setInterval(async () => {
      const windowPos = await getCalculatedWindowInfo();
      setWindowPosState(windowPos);
      const window = getCurrentWindow();

      if (queryData.length > 0 && inputValue) {
        window.setPosition(
          new LogicalPosition(
            Math.ceil(windowPos.bar_x),
            Math.ceil(windowPos.bar_y)
          )
        );
      } else {
        window.setPosition(
          new LogicalPosition(
            Math.ceil(windowPos.bar_x),
            Math.ceil(
              windowPos.bar_y +
                windowPos.bar_height -
                windowPos.textfield_height
            )
          )
        );
      }

      const switchFocus = await isShiftDoubleClicked();
      if (switchFocus && !switchingFocus.current) {
        switchingFocus.current = true;
        const switchSuccess = await bringWindowToTop();
        if (!switchSuccess) {
          console.log("switch focus failed");
        }
        switchingFocus.current = false;
      }
    }, 16);

    // 清理函数
    return () => clearInterval(positionInterval);
  }, [queryData]);

  useEffect(() => {
    const window = getCurrentWindow();

    // 监听窗口失去焦点事件
    const unlistenBlur = window.listen("tauri://blur", () => {
      dispatch(setInputValue(""));
    });

    // 清理函数
    return () => {
      unlistenBlur.then((unlisten) => unlisten());
    };
  }, [dispatch]);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Alt") {
        event.preventDefault();
      } else if (event.key === "Enter") {
        onItemClick(selectedFile);
      } else if (event.key === "ArrowUp" || event.key === "ArrowDown") {
        event.preventDefault();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [selectedFile]);

  const onItemClick = (file: Data) => {
    if (file.type == DataType.Folders) {
      setExplorerEditPath(file.path, "");
    } else {
      const parentPath = file.path.substring(0, file.path.lastIndexOf("\\"));
      console.log("parent path", parentPath);
      setExplorerEditPath(parentPath, file.name);
    }
    dispatch(setInputValue(""));
  };

  return (
    <div
      style={{
        width: "100%",
        height: "100%",
        background: "transparent",
        display: "flex",
        flexDirection: "column",
        position: "fixed",
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
      }}
    >
      <div
        style={{
          flex: 1,
          overflowY: "hidden",
        }}
      >
        <CompactFileList
          data={queryData}
          onSelect={setSelectedFile}
          onItemClick={onItemClick}
          searchUuid={lastSearchUuidRef.current}
        />
      </div>
      <div
        style={{
          position: "sticky",
          bottom: 0,
          backgroundColor: "transparent",
          height: windowPosState.textfield_height,
        }}
      >
        <input
          style={{
            background: "transparent",
          }}
          autoFocus
          value={inputValue}
          type="text"
          className="w-full h-full focus:outline-none rounded-md text-lg"
          placeholder={t("attach.doubleClickShift")}
          onChange={(e) => dispatch(setInputValue(e.target.value))}
        />
      </div>
    </div>
  );
};

export default AttachWindow;
