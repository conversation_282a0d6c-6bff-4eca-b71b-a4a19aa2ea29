# Aiverything 快捷键操作详细指南

## 📋 目录
- [快捷键概述](#快捷键概述)
- [全局快捷键](#全局快捷键)
- [搜索结果操作快捷键](#搜索结果操作快捷键)
- [快捷键自定义设置](#快捷键自定义设置)
- [快捷键冲突解决](#快捷键冲突解决)
- [使用技巧与最佳实践](#使用技巧与最佳实践)
- [常见问题](#常见问题)

---

## 🎯 快捷键概述

Aiverything 提供了丰富的快捷键支持，让您可以高效地进行文件搜索和操作。

---

## ⌨️ 全局快捷键

全局快捷键可以在系统任何位置使用，无需先打开 Aiverything 窗口。

### 主要全局快捷键

| 快捷键组合 | 功能说明 | 使用场景 |
|-----------|----------|----------|
| `Ctrl + Shift + Alt + A` | 打开/隐藏搜索窗口 | 日常最常用的呼出方式 |
| `双击 Ctrl` | 快速打开搜索窗口 | 单手操作，便于快速启动 |

![open search bar](./pictures/open%20search%20bar.gif)

### 全局快捷键详细说明

#### 1. `Ctrl + Shift + Alt + A` - 主要呼出快捷键
- **功能**：打开或隐藏 Aiverything 搜索窗口
- **特点**：
  - 首次按下时打开搜索窗口
  - 再次按下时隐藏窗口
  - 在任何应用程序中都有效
  - 支持自定义修改


#### 2. `双击 Ctrl` - 快速呼出
- **功能**：连续快速按两次 Ctrl 键打开搜索窗口
- **特点**：
  - 单手操作更便捷
  - 可在设置中启用/禁用
  - 按键间隔不能超过500毫秒


#### 3. `双击 Shift` - 附加模式
- **功能**：在资源管理器附加模式下快速搜索
- **使用场景**：
  - 启用"贴靠资源管理器"功能时
  - 快速切换到搜索窗口进行搜索

---

## 🔍 搜索结果操作快捷键

在搜索结果列表中，您可以使用以下快捷键快速操作文件：

### 文件操作快捷键

| 快捷键组合 | 功能说明 | 操作结果 |
|-----------|----------|----------|
| `Enter` | 打开选中文件 | 使用默认程序打开文件 |
| `Shift + Enter` | 以管理员权限打开 | 获得更高权限访问文件 |
| `Alt + Enter` | 复制文件路径 | 将完整路径复制到剪贴板 |
| `Ctrl + Enter` | 打开文件所在文件夹 | 在资源管理器中定位文件 |

![launch shortcut](./pictures/launch%20shortcut.png)

### 导航快捷键

| 快捷键 | 功能说明 |
|--------|----------|
| `↑` | 选择上一个搜索结果 |
| `↓` | 选择下一个搜索结果 |
| `Page Up` | 向上翻页 |
| `Page Down` | 向下翻页 |

### 快捷键操作详解

#### 文件打开操作
- **`Enter`**：使用系统默认程序打开文件
  - 文档文件会用Office等程序打开
  - 图片文件会用默认图片查看器打开
  - 可执行文件会直接运行

#### 管理员权限操作
- **`Shift + Enter`**：以管理员权限运行程序或打开文件
  - 适用于需要管理员权限的程序
  - 在UAC提示下会要求确认权限

#### 路径复制功能
- **`Alt + Enter`**：复制文件完整路径
  - 路径会自动复制到系统剪贴板
  - 可以直接粘贴到其他程序中使用

#### 文件夹定位
- **`Ctrl + Enter`**：在资源管理器中打开文件所在位置
  - 会自动打开文件管理器
  - 并且自动选中目标文件

---

## ⚙️ 快捷键自定义设置

Aiverything 支持自定义快捷键，让您可以根据个人使用习惯进行配置。

### 进入设置界面

1. 右键点击系统托盘中的 Aiverything 图标
2. 选择"设置"菜单项
3. 在设置窗口中找到"快捷键设置"选项

![shortcut key settings](./pictures/shorcut%20key%20settings.png)

### 可自定义的快捷键选项

#### 全局快捷键设置
- **呼出搜索快捷键**：默认为 `Ctrl + Shift + Alt + A`
  - 可修改为其他组合键
  - 支持单个修饰键或组合修饰键
  - 建议避免与常用软件冲突

#### 文件操作快捷键设置
- **复制路径快捷键**：默认为 `Alt`
- **打开父文件夹快捷键**：默认为 `Ctrl`  
- **管理员权限打开快捷键**：默认为 `Shift`

#### 双击功能设置
- **双击 Ctrl 启用/禁用**：控制是否启用双击Ctrl呼出功能

---

## ⚠️ 快捷键冲突解决

### 常见冲突情况

#### 与系统快捷键冲突
- **问题**：设置的快捷键与Windows系统快捷键冲突
- **解决方案**：
  - 避免使用 `Win + 字母` 组合
  - 避免使用 `Ctrl + Alt + Del` 等系统保留组合
  - 建议使用三键组合降低冲突概率

#### 与其他软件冲突
- **常见冲突软件**：
  - QQ、微信等聊聯工具
  - IDE开发工具（VS Code、IntelliJ等）
  - 截图工具、录屏软件

---

### 工作流程优化

#### 高效搜索工作流
1. `Ctrl + Shift + Alt + A` 呼出搜索
2. 输入关键词进行搜索
3. `↑/↓` 选择目标文件
4. 根据需要选择对应操作：
   - `Enter` 直接打开
   - `Alt + Enter` 复制路径
   - `Ctrl + Enter` 定位文件夹

---

## ❓ 常见问题

### 快捷键无响应问题

**Q: 为什么按快捷键没有反应？**

A: 可能的原因和解决方案：

1. **权限不足**
   - 以管理员权限运行 Aiverything
   - 检查UAC设置是否过于严格

2. **快捷键冲突**
   - 检查是否有其他程序占用相同快捷键
   - 尝试修改为其他快捷键组合

3. **程序未运行**
   - 确认 Aiverything 在系统托盘中运行
   - 检查程序是否崩溃或异常退出

### 双击功能问题

**Q: 双击 Ctrl 不工作？**

A: 检查以下设置：

1. **功能是否启用**
   - 设置 → 快捷键设置 → 确认"双击 Ctrl"功能已启用

2. **按键间隔**
   - 确保两次按键间隔在500毫秒内
   - 按键要轻快，避免长按

3. **系统兼容性**
   - 部分键盘驱动可能影响双击检测
   - 尝试更新键盘驱动程序

### 自定义快捷键不生效

**Q: 修改快捷键后不生效？**

A: 解决步骤：

1. **重启程序**
   - 右键托盘图标 → 退出
   - 重新启动 Aiverything

2. **确认设置保存**
   - 检查设置是否正确保存
   - 重新进入设置确认配置

3. **权限问题**
   - 以管理员权限运行程序
   - 检查程序是否有写入配置文件的权限

---

## 📞 获取帮助

如果您在使用快捷键时遇到任何问题，可以通过以下方式获取帮助：

- **在线文档**：[https://aiverything.me/docs](https://aiverything.me/docs)
- **GitHub Issues**：[问题反馈](https://github.com/panwangwin/aiverything-official-forum/issues)
- **QQ交流群**：893463594

---

*本指南将持续更新，感谢您使用 Aiverything！* 