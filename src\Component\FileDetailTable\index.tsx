import {
  FileIcon as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  FolderIcon,
  ClockIcon,
} from "lucide-react";
import { useTranslation } from "react-i18next";

const FileDetailTable = ({ file }) => {
  const { t } = useTranslation();

  const fileDetails = [
    {
      icon: <FileIconLucide className="w-4 h-4 mr-2" />,
      label: t("searchBar.detail.fileName"),
      value: file.name,
      title: file.name,
    },
    {
      icon: <FolderIcon className="w-4 h-4 mr-2" />,
      label: t("searchBar.detail.fileLocation"),
      value: file.path,
      title: file.path,
    },
    {
      icon: <ClockIcon className="w-4 h-4 mr-2" />,
      label: t("searchBar.detail.lastModified"),
      value: file.lastModified?.toLocaleString() || "-",
      title: file.lastModified?.toLocaleString() || "-",
    },
    {
      icon: <ClockIcon className="w-4 h-4 mr-2" />,
      label: t("searchBar.detail.createdAt"),
      value: file.createdAt?.toLocaleString() || "-",
      title: file.createdAt?.toLocaleString() || "-",
    },
  ];

  return (
    <div className="w-full max-w-md mx-auto rounded-lg overflow-hidden dark:bg-gray-800">
      {fileDetails.map((detail, index) => {
        if (index === 0) {
          return (
            <div key={index} className="flex p-2 bg-gray-100 dark:bg-gray-800">
              <div className="flex items-center text-xs text-gray-600 dark:text-gray-400 w-1/3">
                {detail.icon}
                <span className="truncate max-w-xs" title={detail.title}>
                  {detail.label}
                </span>
              </div>
              <div className="text-xs text-right text-gray-900 dark:text-gray-200 w-2/3">
                <div
                  className="truncate max-w-xs font-medium"
                  title={detail.title}
                >
                  {detail.value}
                </div>
              </div>
            </div>
          );
        } else {
          return (
            <div key={index} className="flex p-2 bg-gray-100 dark:bg-gray-800">
              <div className="flex items-center text-xs text-gray-600 dark:text-gray-400 w-2/3">
                {detail.icon}
                <span className="truncate max-w-xs" title={detail.title}>
                  {detail.label}
                </span>
              </div>
              <div className="text-xs text-right text-gray-800 dark:text-gray-200 w-2/3">
                <div
                  className="truncate max-w-xs font-medium"
                  title={detail.title}
                >
                  {detail.value}
                </div>
              </div>
            </div>
          );
        }
      })}
    </div>
  );
};

export default FileDetailTable;
