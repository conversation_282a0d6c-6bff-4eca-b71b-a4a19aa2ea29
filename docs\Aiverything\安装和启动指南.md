# Aiverything 安装和启动指南

## 📋 目录
- [系统要求](#系统要求)
- [下载准备](#下载准备)
- [安装步骤](#安装步骤)
- [首次启动](#首次启动)
- [启动方式](#启动方式)
- [卸载程序](#卸载程序)
- [常见问题](#常见问题)

---

## 💻 系统要求

### 最低系统要求

#### 操作系统
- **Windows 10** (版本 1809 或更高)
- **Windows 11** (所有版本)
- **系统架构**: 64位 (x64)
- **32位系统**: 不支持

#### 硬件要求
- **处理器**: Intel Core i3 或 AMD Ryzen 3 及以上
- **内存**: 最少 4GB RAM，推荐 8GB 及以上
- **存储空间**: 至少 500MB 可用空间
- **显卡**: 
  - 独立显卡: GTX 1060 / RX 580 或更高（可选，用于GPU加速）

#### 软件依赖
- **Visual C++ Redistributable**: 2015-2022 版本
- **WebView2 Runtime**: 用于插件界面显示
- **Java运行环境**: 程序内置Java 21运行时

### 推荐系统配置

#### 最佳性能配置
- **操作系统**: Windows 11 最新版本
- **处理器**: Intel Core i5 或 AMD Ryzen 5 及以上
- **内存**: 16GB RAM 或更多
- **存储**: SSD 固态硬盘
- **显卡**: 
  - NVIDIA: GTX 1660 或更高（支持CUDA加速）
  - AMD: RX 6600 或更高（支持OpenCL加速）

#### 大文件索引配置
如果需要索引超过100万个文件，建议：
- **内存**: 32GB RAM
- **存储**: NVMe SSD
- **显卡**: RTX 3060 或更高

---

## 📥 下载准备

### 官方下载渠道

#### 主要下载源
1. **GitHub Release**（推荐）
   - 链接: [https://github.com/panwangwin/aiverything-official-forum/releases](https://github.com/panwangwin/aiverything-official-forum/releases)
   - 优势: 版本完整，更新及时
   - 适用: 网络条件良好的用户

2. **夸克网盘**
   - 链接: [https://pan.quark.cn/s/5ac2bf9154d7](https://pan.quark.cn/s/5ac2bf9154d7)
   - 优势: 国内高速下载
   - 适用: 网络条件有限的用户

#### 文件说明
- **文件名格式**: `aiverything_x.x.x_x64-setup.exe`
- **文件大小**: 约 180-200MB
- **文件类型**: Windows 可执行安装程序

---

## 🔧 安装步骤

### 第一步：下载安装包

1. **访问下载页面**
   - 从官方渠道选择合适的下载源
   - 选择最新稳定版本

2. **下载文件**
   - 点击下载链接
   - 等待下载完成
   - 文件大小约180-200MB

### 第二步：执行安装

1. **启动安装程序**
   - 双击 `aiverything_x.x.x_x64-setup.exe`
   - 等待安装程序加载

2. **欢迎界面**
   - 阅读欢迎信息
   - 点击"下一步"继续

3. **许可协议**
   - 仔细阅读许可协议
   - 勾选"我接受许可协议"
   - 点击"下一步"

4. **选择安装位置**
   - 默认安装路径：`C:\Program Files\Aiverything`
   - 可点击"浏览"选择其他位置
   - 确保目标位置有足够空间
   - 点击"下一步"

5. **开始安装**
   - 点击"安装"开始安装过程
   - 等待安装完成
   - 安装时间约1-3分钟

6. **完成安装**
   - 安装完成后会显示完成界面
   - 可选择"立即启动Aiverything"
   - 点击"完成"退出安装程序

---

## 🚀 首次启动

### 自动启动流程

1. **程序启动**
   - 如果勾选了"立即启动"，程序会自动启动
   - 系统托盘会出现Aiverything图标

2. **初始化配置**
   - 程序会自动创建配置文件
   - 初始化用户设置
   - 准备索引引擎

3. **文件索引建立**
   - 程序会自动开始文件索引
   - 默认索引所有本地磁盘的所有文件
   - 索引过程会在后台进行

### 首次启动详细流程

#### 第一步：程序初始化
```
正在初始化Aiverything...
- 创建配置文件: config.json
- 初始化索引核心用户设置: ./core/user/settings.json
- 加载核心组件
- 初始化GPU加速模块
```

#### 第二步：索引配置
- **自动检测磁盘**: 程序会自动检测系统中的所有磁盘
- **选择索引范围**: 默认选择所有本地磁盘进行索引
- **排除系统目录**: 自动排除Windows系统目录和临时文件

#### 第三步：开始索引
```
正在建立文件索引...
- 扫描磁盘文件
- 建立索引数据库
- 优化索引结构
- 完成索引建立
```

#### 第四步：完成启动
- **托盘图标**: 系统托盘出现Aiverything图标
- **准备就绪**: 程序准备接受搜索请求
- **快捷键激活**: 全局快捷键 `Ctrl + Shift + Alt + A` 可用

### 首次启动预期时间

#### 索引时间估算
- **小型系统** (< 10万文件): 10秒
- **中型系统** (10-50万文件): 1-3分钟
- **大型系统** (50-100万文件): 3-5分钟
- **超大系统** (> 100万文件): 5分钟-30分钟

#### 影响因素
- **文件数量**: 文件越多，索引时间越长
- **磁盘类型**: SSD比HDD快3-5倍
- **CPU性能**: 影响索引处理速度
- **内存大小**: 影响索引缓存效率

---

## 🔑 启动方式

### 方式一：全局快捷键

#### 默认快捷键
- **组合键**: `Ctrl + Shift + Alt + A`
- **功能**: 打开/隐藏搜索窗口
- **适用场景**: 任何时候快速搜索

#### 使用步骤
1. 同时按下 `Ctrl + Shift + Alt + A`
2. 搜索窗口会立即显示
3. 光标自动定位到搜索框
4. 开始输入搜索内容

#### 快捷键自定义
1. 右键点击系统托盘图标
2. 选择"设置"
3. 进入"快捷键设置"
4. 修改"全局快捷键"
5. 点击"应用"保存

### 方式二：双击Ctrl键

#### 功能说明
- **操作**: 快速连按两次Ctrl键
- **默认状态**: 禁用（需要在设置中开启）
- **适用场景**: 单手操作，快速呼出

#### 开启步骤
1. 右键点击系统托盘图标
2. 选择"设置"
3. 进入"快捷键设置"
4. 勾选"双击Ctrl"功能
5. 点击"应用"保存

#### 使用技巧
- 两次Ctrl按键间隔不超过500毫秒
- 确保没有其他程序占用此功能
- 部分键盘可能不支持此功能

### 方式三：系统托盘

#### 左键点击
- **功能**: 直接打开搜索窗口
- **操作**: 单击系统托盘中的Aiverything图标
- **适用场景**: 鼠标操作偏好用户

#### 右键菜单
- **搜索**: 打开搜索窗口
- **设置**: 打开设置界面
- **关于**: 查看程序信息
- **退出**: 完全关闭程序

---

## 🗑️ 卸载程序

### 标准卸载方式

#### 控制面板卸载
1. 打开控制面板
2. 点击"程序"或"程序和功能"
3. 找到"Aiverything"
4. 点击"卸载"
5. 按照提示完成卸载

---

## ❓ 常见问题

### 安装问题

#### Q1: 安装时提示"需要管理员权限"
**解决方案**:
1. 右键点击安装程序
2. 选择"以管理员身份运行"
3. 在UAC提示中点击"是"

#### Q2: 安装时提示"文件损坏"
**解决方案**:
1. 重新下载安装包
2. 检查网络连接稳定性
3. 临时关闭杀毒软件
4. 使用不同的下载源

#### Q3: 安装后无法启动
**解决方案**:
1. 检查系统是否为64位
2. 安装Visual C++ Redistributable
3. 更新显卡驱动
4. 以管理员权限运行程序

### 启动问题

#### Q4: 快捷键不响应
**解决方案**:
1. 检查是否有其他程序占用快捷键
2. 重新设置快捷键组合
3. 以管理员权限运行程序
4. 重启计算机

#### Q5: 系统托盘没有图标
**解决方案**:
1. 检查任务栏设置
2. 确保程序正在运行
3. 重启程序
4. 检查系统托盘区域设置

#### Q6: 首次索引时间过长
**解决方案**:
1. 这是正常现象，耐心等待
2. 在索引设置中排除不必要的文件夹
3. 使用SSD硬盘可加速索引
4. 确保有足够的内存空间

### 性能问题

#### Q7: 程序启动缓慢
**解决方案**:
1. 重启计算机
2. 清理系统垃圾文件
3. 关闭不必要的后台程序
4. 检查硬盘空间是否充足

#### Q8: 搜索速度慢
**解决方案**:
1. 等待索引完成
2. 启用GPU加速
3. 增加系统内存
4. 优化索引设置

### 功能问题

#### Q9: 双击Ctrl功能不工作
**解决方案**:
1. 在设置中开启此功能
2. 检查键盘是否支持
3. 调整双击时间间隔
4. 重启程序

#### Q10: 无法搜索到某些文件
**解决方案**:
1. 检查文件是否在索引范围内
2. 确认文件没有被忽略
3. 手动更新索引
4. 检查文件权限

---

## 📞 获取帮助

### 官方支持

#### 文档资源
- **用户手册**: 详细的使用说明
- **常见问题**: 常见问题解答
- **视频教程**: 操作演示视频

#### 社区支持
- **QQ交流群**: 893463594
- **GitHub Issues**: 问题反馈和建议
- **官方论坛**: 用户交流平台

#### 联系方式
- **官方网站**: [https://aiverything.me/](https://aiverything.me/)
- **GitHub**: [https://github.com/panwangwin/aiverything-official-forum](https://github.com/panwangwin/aiverything-official-forum)
- **邮箱**: 通过GitHub Issues联系

### 反馈建议

#### 问题反馈
请提供以下信息：
1. 操作系统版本
2. 程序版本号
3. 详细的问题描述
4. 重现步骤
5. 错误截图或日志

#### 功能建议
1. 描述期望的功能
2. 说明使用场景
3. 提供参考例子
4. 评估重要性
