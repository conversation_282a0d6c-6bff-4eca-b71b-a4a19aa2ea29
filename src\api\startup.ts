import { invoke } from "@tauri-apps/api/core";

const startupApi = {
  hasStartup: "has_startup",
  addStartup: "add_startup",
  deleteStartup: "delete_startup",
};

export function hasStartup() {
  return invoke<any>(startupApi.hasStartup);
}

export function addStartup() {
  return invoke<any>(startupApi.addStartup);
}

export function deleteStartup() {
  return invoke<any>(startupApi.deleteStartup);
}
